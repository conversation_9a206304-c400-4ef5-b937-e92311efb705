# API Reference Documentation

## Overview

The Rubicon Programs Testing Application provides a comprehensive REST API built with Next.js API Routes. All endpoints use JSON for request/response bodies and follow RESTful conventions with proper HTTP status codes.

## Authentication

All protected endpoints require authentication via NextAuth.js session cookies or JWT tokens.

### Session-based Authentication
```http
Cookie: next-auth.session-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

### JWT Authentication (for external integrations)
```http
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## Base URL

- **Development**: `http://localhost:3000/api`
- **Production**: `https://your-domain.com/api`

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      // Additional error details
    }
  }
}
```

## Authentication Endpoints

### POST /api/auth/signup
Register a new user account.

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "zipCode": "12345"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "userId": "clm123abc456",
    "message": "Registration successful. Please check your email for verification instructions."
  }
}
```

**Status Codes:**
- `201`: User created successfully
- `400`: Validation error
- `409`: Email already exists

### POST /api/auth/signin
Authenticate user credentials.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "userpassword"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "clm123abc456",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "userType": "USER"
    },
    "session": {
      "expires": "2024-02-15T12:00:00.000Z"
    }
  }
}
```

### POST /api/auth/verify-email
Verify user email address.

**Request Body:**
```json
{
  "token": "verification-token-uuid"
}
```

### POST /api/auth/reset-password
Initiate password reset process.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

## User Management Endpoints

### GET /api/profile
Get current user's profile information.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "clm123abc456",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "dateOfBirth": "1990-01-15T00:00:00.000Z",
    "zipCode": "12345",
    "phoneNumber": "+1234567890",
    "userType": "USER",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "testAccess": [
      {
        "testType": "typing-keyboard",
        "displayName": "Keyboard Typing",
        "accessType": "UNLIMITED",
        "expiresAt": null
      }
    ]
  }
}
```

### PUT /api/profile
Update user profile information.

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "phoneNumber": "+1234567890",
  "dateOfBirth": "1990-01-15",
  "educationLevel": "Bachelor's Degree"
}
```

### POST /api/profile/change-password
Change user password.

**Request Body:**
```json
{
  "currentPassword": "oldpassword",
  "newPassword": "NewSecurePassword123!"
}
```

## Test Management Endpoints

### GET /api/tests/types
Get available test types.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "typing-keyboard",
      "name": "typing-keyboard",
      "displayName": "Keyboard Typing",
      "description": "Assess typing speed and accuracy on standard keyboard",
      "isActive": true,
      "userAccess": {
        "accessType": "UNLIMITED",
        "expiresAt": null
      }
    },
    {
      "id": "basic-math",
      "name": "basic-math", 
      "displayName": "Basic Math",
      "description": "Mathematical skills from 5th to 12th grade level",
      "isActive": true,
      "userAccess": {
        "accessType": "ONE_TIME",
        "expiresAt": null
      }
    }
  ]
}
```

### POST /api/tests/start
Start a new test session.

**Request Body:**
```json
{
  "testTypeId": "typing-keyboard",
  "isPractice": false,
  "configuration": {
    "timeLimit": 5,
    "allowPause": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "testId": "test_clm789xyz012",
    "testType": "typing-keyboard",
    "isPractice": false,
    "startedAt": "2024-01-15T10:30:00.000Z",
    "timeLimit": 5,
    "configuration": {
      "allowPause": true,
      "showCorrectAnswers": false
    }
  }
}
```

### GET /api/tests/{testId}
Get test session details.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "test_clm789xyz012",
    "userId": "clm123abc456",
    "testTypeId": "typing-keyboard",
    "status": "STARTED",
    "isPractice": false,
    "startedAt": "2024-01-15T10:30:00.000Z",
    "timeLimit": 5,
    "currentQuestionIndex": 0,
    "totalQuestions": 1,
    "answers": [],
    "score": null
  }
}
```

### POST /api/tests/{testId}/answer
Submit answer for current question.

**Request Body:**
```json
{
  "questionId": "question_123",
  "answer": "user typed text here",
  "timeSpent": 45000
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "isCorrect": true,
    "correctAnswer": "expected typed text here",
    "explanation": "Answer explanation if available",
    "nextQuestionId": null,
    "testCompleted": true
  }
}
```

### POST /api/tests/{testId}/pause
Pause test session.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "PAUSED",
    "pausedAt": "2024-01-15T10:35:00.000Z"
  }
}
```

### POST /api/tests/{testId}/resume
Resume paused test session.

### POST /api/tests/{testId}/cancel
Cancel test session.

**Request Body:**
```json
{
  "reason": "User cancelled test"
}
```

### GET /api/tests/{testId}/results
Get test results (only for completed tests).

**Response:**
```json
{
  "success": true,
  "data": {
    "testId": "test_clm789xyz012",
    "score": 85.5,
    "gradeLevelScore": "10.2",
    "accuracy": 92.3,
    "timeToComplete": 285,
    "completedAt": "2024-01-15T10:35:00.000Z",
    "detailedResults": {
      "wpm": 65,
      "grossWPM": 68,
      "netWPM": 63,
      "errorRate": 5.2,
      "keystrokeAnalysis": {
        "totalKeystrokes": 1247,
        "correctKeystrokes": 1151,
        "corrections": 96
      }
    },
    "certificate": {
      "eligible": true,
      "downloadUrl": "/api/certificates/test_clm789xyz012"
    }
  }
}
```

## Test History Endpoints

### GET /api/test-history
Get user's test history with pagination.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 50)
- `testType` (optional): Filter by test type
- `status` (optional): Filter by status (COMPLETED, CANCELLED)
- `isPractice` (optional): Filter by practice/official tests

**Response:**
```json
{
  "success": true,
  "data": {
    "tests": [
      {
        "id": "test_clm789xyz012",
        "testType": {
          "name": "typing-keyboard",
          "displayName": "Keyboard Typing"
        },
        "status": "COMPLETED",
        "isPractice": false,
        "score": 85.5,
        "startedAt": "2024-01-15T10:30:00.000Z",
        "completedAt": "2024-01-15T10:35:00.000Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 3,
      "totalCount": 25,
      "limit": 10
    }
  }
}
```

## Access Control Endpoints

### POST /api/test-requests
Submit request for test access.

**Request Body:**
```json
{
  "testTypeId": "digital-literacy",
  "reason": "Required for job application"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "requestId": "req_clm456def789",
    "status": "PENDING",
    "message": "Your request has been submitted and is awaiting review"
  }
}
```

### POST /api/one-time-codes/redeem
Redeem a one-time access code.

**Request Body:**
```json
{
  "code": "ABC12DEF"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "testType": "Digital Literacy",
    "accessType": "ONE_TIME",
    "message": "Code redeemed successfully. You now have one-time access to Digital Literacy test."
  }
}
```

## Admin Endpoints

*Note: All admin endpoints require ADMIN user type and appropriate permissions.*

### GET /api/admin/users
Get all users with search and filtering.

**Query Parameters:**
- `search` (optional): Search term for name/email
- `userType` (optional): Filter by user type
- `isActive` (optional): Filter by active status
- `page`, `limit`: Pagination

**Response:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "clm123abc456",
        "email": "<EMAIL>",
        "firstName": "John",
        "lastName": "Doe",
        "userType": "USER",
        "isDeactivated": false,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "testCount": 5,
        "lastTestDate": "2024-01-15T10:35:00.000Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 10,
      "totalCount": 95,
      "limit": 10
    }
  }
}
```

### POST /api/admin/users
Create new user account (admin only).

**Request Body:**
```json
{
  "firstName": "Jane",
  "lastName": "Smith",
  "email": "<EMAIL>",
  "userType": "USER",
  "zipCode": "54321",
  "sendWelcomeEmail": true
}
```

### PUT /api/admin/users/{userId}
Update user account.

### DELETE /api/admin/users/{userId}
Deactivate user account.

### POST /api/admin/users/{userId}/grant-access
Grant test access to user.

**Request Body:**
```json
{
  "testTypeId": "basic-math",
  "accessType": "UNLIMITED",
  "expiresAt": null
}
```

### POST /api/admin/users/{userId}/revoke-access
Revoke user's test access.

### GET /api/admin/test-requests
Get pending test access requests.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "req_clm456def789",
      "user": {
        "id": "clm123abc456",
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "testType": {
        "name": "digital-literacy",
        "displayName": "Digital Literacy"
      },
      "reason": "Required for job application",
      "status": "PENDING",
      "createdAt": "2024-01-15T09:00:00.000Z"
    }
  ]
}
```

### POST /api/admin/test-requests/{requestId}/review
Review test access request.

**Request Body:**
```json
{
  "decision": "APPROVED",
  "response": "Access granted for job preparation",
  "accessType": "ONE_TIME",
  "expiresAt": null
}
```

### POST /api/admin/one-time-codes
Generate one-time access codes.

**Request Body:**
```json
{
  "testTypeId": "typing-keyboard",
  "quantity": 5,
  "expiresAt": "2024-12-31T23:59:59.000Z"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "codes": [
      {
        "code": "ABC12DEF",
        "testType": "Keyboard Typing",
        "expiresAt": "2024-12-31T23:59:59.000Z"
      }
    ],
    "summary": {
      "generated": 5,
      "testType": "Keyboard Typing",
      "expiresAt": "2024-12-31T23:59:59.000Z"
    }
  }
}
```

### GET /api/admin/analytics
Get system analytics and metrics.

**Query Parameters:**
- `dateRange` (optional): Date range filter (7d, 30d, 90d, 1y)
- `testType` (optional): Filter by test type

**Response:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalUsers": 150,
      "activeUsers": 125,
      "totalTests": 543,
      "completedTests": 487,
      "averageScore": 78.5
    },
    "testsByType": {
      "typing-keyboard": { "total": 200, "completed": 185 },
      "basic-math": { "total": 150, "completed": 135 },
      "digital-literacy": { "total": 120, "completed": 110 },
      "basic-english": { "total": 73, "completed": 57 }
    },
    "trendsData": [
      { "date": "2024-01-01", "tests": 12, "users": 8 },
      { "date": "2024-01-02", "tests": 15, "users": 12 }
    ]
  }
}
```

## Typing Test Endpoints

### GET /api/typing/{testId}/session
Get typing test session data.

**Response:**
```json
{
  "success": true,
  "data": {
    "testId": "test_clm789xyz012",
    "testType": "keyboarding",
    "passageText": "The quick brown fox jumps over the lazy dog...",
    "startedAt": "2024-01-15T10:30:00.000Z",
    "currentPosition": 45,
    "typedText": "The quick brown fox jumps over the l",
    "timeElapsed": 30,
    "realTimeStats": {
      "wpm": 62,
      "accuracy": 95.5,
      "errors": 2
    }
  }
}
```

### POST /api/typing/{testId}/keystroke
Record keystroke data for typing tests.

**Request Body:**
```json
{
  "key": "a",
  "timestamp": 1705312345678,
  "position": 45,
  "isCorrect": true,
  "currentText": "The quick brown fox jumps over the la"
}
```

## Math Test Endpoints

### GET /api/math/{testId}/question
Get next math question (adaptive).

**Response:**
```json
{
  "success": true,
  "data": {
    "questionId": "math_q123",
    "questionText": "Solve for x: 2x + 5 = 13",
    "gradeLevel": 7,
    "currentGradeLevel": 7,
    "questionNumber": 5,
    "totalQuestions": "adaptive",
    "timeLimit": null
  }
}
```

### POST /api/math/{testId}/answer
Submit math answer.

**Request Body:**
```json
{
  "questionId": "math_q123",
  "answer": "4"
}
```

## Certificate & Reporting Endpoints

### GET /api/certificates/{testId}
Generate and download test certificate.

**Query Parameters:**
- `format` (optional): pdf, png (default: pdf)
- `template` (optional): standard, letterhead, simple

**Response:**
- Content-Type: application/pdf or image/png
- Binary file download

### GET /api/reports/{testId}
Generate detailed test report.

**Query Parameters:**
- `format`: json, pdf, csv
- `includeAnalytics`: true/false

**Response (JSON format):**
```json
{
  "success": true,
  "data": {
    "testInfo": {
      "testId": "test_clm789xyz012",
      "testType": "Keyboard Typing",
      "completedAt": "2024-01-15T10:35:00.000Z",
      "isPractice": false
    },
    "userInfo": {
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "results": {
      "score": 85.5,
      "gradeLevelScore": "10.2",
      "percentile": 75
    },
    "detailedAnalysis": {
      // Comprehensive test analysis
    }
  }
}
```

## Error Codes

### Common Error Codes

| Code | Status | Description |
|------|--------|-------------|
| `UNAUTHORIZED` | 401 | Authentication required |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `VALIDATION_ERROR` | 400 | Request validation failed |
| `RATE_LIMITED` | 429 | Too many requests |
| `INTERNAL_ERROR` | 500 | Internal server error |

### Authentication Errors

| Code | Description |
|------|-------------|
| `INVALID_CREDENTIALS` | Email or password incorrect |
| `EMAIL_NOT_VERIFIED` | Email verification required |
| `ACCOUNT_DEACTIVATED` | User account is deactivated |
| `PASSWORD_CHANGE_REQUIRED` | Password change required |
| `SESSION_EXPIRED` | Session has expired |

### Test-Specific Errors

| Code | Description |
|------|-------------|
| `NO_TEST_ACCESS` | User lacks access to test type |
| `TEST_ACCESS_EXPIRED` | Test access has expired |
| `TEST_NOT_FOUND` | Test session not found |
| `TEST_ALREADY_COMPLETED` | Test is already completed |
| `INVALID_TEST_STATE` | Test not in valid state for operation |
| `QUESTION_NOT_FOUND` | Question not found |
| `ANSWER_TIMEOUT` | Answer submission timed out |

## Rate Limiting

API endpoints are rate-limited to prevent abuse:

- **Authentication endpoints**: 5 requests per minute per IP
- **Test submission endpoints**: 30 requests per minute per user
- **General API endpoints**: 100 requests per minute per user
- **Admin endpoints**: 200 requests per minute per admin

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1705312800
```

## Webhooks (Future Enhancement)

The API supports webhook notifications for external integrations:

### Webhook Events
- `test.completed` - Test completion
- `user.registered` - New user registration
- `access.granted` - Test access granted
- `certificate.generated` - Certificate generated

### Webhook Payload Example
```json
{
  "event": "test.completed",
  "timestamp": "2024-01-15T10:35:00.000Z",
  "data": {
    "testId": "test_clm789xyz012",
    "userId": "clm123abc456",
    "testType": "typing-keyboard",
    "score": 85.5,
    "completedAt": "2024-01-15T10:35:00.000Z"
  }
}
```

---

*This comprehensive API reference provides complete documentation for integrating with and extending the Rubicon Programs Testing Application.*