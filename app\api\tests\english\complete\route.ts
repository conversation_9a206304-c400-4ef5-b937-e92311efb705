import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';

interface EnglishTestSession {
  testId: string;
  userId: string;
  currentGradeLevel: number;
  startingGradeLevel: number;
  questionsAnswered: {
    questionId: string;
    isCorrect: boolean;
    gradeLevel: number;
    questionType: string;
    timeSpent: number;
    answer: string;
  }[];
  currentPassageId?: string;
  passageQuestions: string[];
  adaptiveState: {
    correctByGrade: Record<number, number>;
    totalByGrade: Record<number, number>;
    consecutiveCorrect: number;
    consecutiveIncorrect: number;
  };
  runningScores: {
    languageMastery: number;
    readingComprehension: number;
    overall: number;
  };
  isPassageViewingLocked: boolean;
  startedAt: Date;
  lastActivityAt: Date;
}

// POST /api/tests/english/complete - Complete English test and get results
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { testId } = body;

    if (!testId) {
      return NextResponse.json({ error: 'Test ID is required' }, { status: 400 });
    }

    // Get test session
    const test = await prisma.tests.findFirst({
      where: {
        id: testId,
        userId: session.user.id,
        status: { in: ['STARTED', 'PAUSED', 'COMPLETED'] },
      },
      include: {
        test_types: true,
        users: true,
      },
    });

    if (!test) {
      return NextResponse.json({ error: 'Test session not found' }, { status: 404 });
    }

    // If already completed, return existing results
    if (test.status === 'COMPLETED') {
      const results = await getEnglishTestResults(testId);
      return NextResponse.json({
        success: true,
        data: results,
      });
    }

    // Get session data
    const sessionData = test.additionalData as { englishTestSession: EnglishTestSession };
    const englishSession = sessionData?.englishTestSession;

    if (!englishSession) {
      return NextResponse.json({ error: 'Invalid test session data' }, { status: 400 });
    }

    // Force complete the test
    await forceCompleteEnglishTest(testId, englishSession);

    // Get the completed results
    const results = await getEnglishTestResults(testId);

    return NextResponse.json({
      success: true,
      data: results,
    });

  } catch (error) {
    console.error('Error completing English test:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET /api/tests/english/complete - Get results for completed test
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const testId = searchParams.get('testId');

    if (!testId) {
      return NextResponse.json({ error: 'Test ID is required' }, { status: 400 });
    }

    // Verify test belongs to user and is completed
    const test = await prisma.tests.findFirst({
      where: {
        id: testId,
        userId: session.user.id,
        status: 'COMPLETED',
      },
    });

    if (!test) {
      return NextResponse.json({ error: 'Completed test not found' }, { status: 404 });
    }

    const results = await getEnglishTestResults(testId);

    return NextResponse.json({
      success: true,
      data: results,
    });

  } catch (error) {
    console.error('Error getting English test results:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

async function forceCompleteEnglishTest(testId: string, session: EnglishTestSession): Promise<void> {
  const responses = session.questionsAnswered.map(qa => ({
    questionId: qa.questionId,
    isCorrect: qa.isCorrect,
    gradeLevel: qa.gradeLevel,
    questionType: qa.questionType,
  }));

  // Calculate final scores
  const languageMasteryScore = calculateLanguageMasteryScore(responses);
  const readingComprehensionScore = calculateReadingComprehensionScore(responses);
  const overallScore = (languageMasteryScore + readingComprehensionScore) / 2;

  // Determine final grade level based on performance
  const finalGradeLevel = determineGradeLevel({
    languageMastery: languageMasteryScore,
    readingComprehension: readingComprehensionScore,
  });

  // Calculate time to complete
  const now = new Date();
  const startTime = new Date(session.startedAt);
  const timeToComplete = Math.floor((now.getTime() - startTime.getTime()) / 1000); // in seconds

  // Update test status
  await prisma.tests.update({
    where: { id: testId },
    data: {
      status: 'COMPLETED',
      completedAt: now,
      score: overallScore,
      gradeLevelScore: finalGradeLevel.toString(),
      additionalData: { englishTestSession: session },
    },
  });

  // Create test results record
  await prisma.english_test_results.create({
    data: {
      test_id: testId,
      language_mastery_score: languageMasteryScore,
      reading_comprehension_score: readingComprehensionScore,
      overall_score: overallScore,
      vocabulary_score: calculateSubScore(responses, 'VOCABULARY'),
      spelling_score: calculateSubScore(responses, 'SPELLING'),
      grammar_score: calculateSubScore(responses, 'GRAMMAR'),
      punctuation_score: calculateSubScore(responses, 'PUNCTUATION'),
      grade_level_achieved: finalGradeLevel,
    },
  });

  // Create general test_results record for compatibility
  await prisma.test_results.create({
    data: {
      testId: testId,
      userId: session.userId,
      testTypeId: 'basic-english', // This should be looked up properly
      score: overallScore,
      gradeLevelScore: finalGradeLevel.toString(),
      timeToComplete: timeToComplete,
      accuracy: overallScore, // For compatibility
      languageScore: languageMasteryScore,
      comprehensionScore: readingComprehensionScore,
      questionsCorrect: responses.filter(r => r.isCorrect).length,
      questionsTotal: responses.length,
      detailedResults: {
        breakdown: {
          vocabulary: calculateSubScore(responses, 'VOCABULARY'),
          spelling: calculateSubScore(responses, 'SPELLING'),
          grammar: calculateSubScore(responses, 'GRAMMAR'),
          punctuation: calculateSubScore(responses, 'PUNCTUATION'),
          readingComprehension: readingComprehensionScore,
        },
        gradeProgression: session.adaptiveState,
        questionsByGrade: getQuestionsByGrade(responses),
        totalTimeSpent: responses.reduce((sum, r) => sum + (r as any).timeSpent, 0),
      },
      completedAt: now,
    },
  });
}

async function getEnglishTestResults(testId: string) {
  // Get the completed test
  const test = await prisma.tests.findUnique({
    where: { id: testId },
    include: {
      test_types: true,
      users: true,
      english_test_results: true,
      test_results: true,
    },
  });

  if (!test || !test.english_test_results) {
    throw new Error('Test results not found');
  }

  const results = test.english_test_results;
  const sessionData = test.additionalData as { englishTestSession: EnglishTestSession };
  const englishSession = sessionData?.englishTestSession;

  return {
    testId: test.id,
    testType: test.test_types.name,
    isPractice: test.isPractice,
    user: {
      name: `${test.users?.firstName} ${test.users?.lastName}`,
      email: test.users?.email,
    },
    startedAt: test.startedAt,
    completedAt: test.completedAt,
    timeToComplete: test.test_results?.timeToComplete || 0,
    scores: {
      overall: results.overall_score,
      languageMastery: results.language_mastery_score,
      readingComprehension: results.reading_comprehension_score,
      vocabulary: results.vocabulary_score,
      spelling: results.spelling_score,
      grammar: results.grammar_score,
      punctuation: results.punctuation_score,
    },
    gradeLevel: {
      starting: englishSession?.startingGradeLevel || 8,
      achieved: results.grade_level_achieved,
      progression: englishSession?.adaptiveState || {},
    },
    performance: {
      questionsAnswered: englishSession?.questionsAnswered.length || 0,
      questionsCorrect: englishSession?.questionsAnswered.filter(q => q.isCorrect).length || 0,
      accuracy: results.overall_score,
      averageTimePerQuestion: englishSession?.questionsAnswered.length 
        ? (test.test_results?.timeToComplete || 0) / englishSession.questionsAnswered.length 
        : 0,
    },
    breakdown: {
      byQuestionType: getPerformanceByType(englishSession?.questionsAnswered || []),
      byGradeLevel: getPerformanceByGrade(englishSession?.questionsAnswered || []),
    },
    certificate: {
      eligible: results.overall_score >= 70, // Configurable threshold
      downloadUrl: `/api/certificates/${testId}`,
    },
    recommendations: generateRecommendations(results),
  };
}

function calculateLanguageMasteryScore(responses: any[]): number {
  const languageMasteryResponses = responses.filter(r => r.questionType !== 'READING_COMPREHENSION');
  if (languageMasteryResponses.length === 0) return 0;
  
  const correctResponses = languageMasteryResponses.filter(r => r.isCorrect).length;
  return (correctResponses / languageMasteryResponses.length) * 100;
}

function calculateReadingComprehensionScore(responses: any[]): number {
  const readingComprehensionResponses = responses.filter(r => r.questionType === 'READING_COMPREHENSION');
  if (readingComprehensionResponses.length === 0) return 0;
  
  const correctResponses = readingComprehensionResponses.filter(r => r.isCorrect).length;
  return (correctResponses / readingComprehensionResponses.length) * 100;
}

function determineGradeLevel(scores: { [key: string]: number }): number {
  const averageScore = Object.values(scores).reduce((a, b) => a + b, 0) / Object.values(scores).length;
  if (averageScore >= 90) return 12;
  if (averageScore >= 80) return 11;
  if (averageScore >= 70) return 10;
  if (averageScore >= 60) return 9;
  if (averageScore >= 50) return 8;
  if (averageScore >= 40) return 7;
  if (averageScore >= 30) return 6;
  return 5;
}

function calculateSubScore(responses: any[], questionType: string): number {
  const typeResponses = responses.filter(r => r.questionType === questionType);
  if (typeResponses.length === 0) return 0;
  
  const correct = typeResponses.filter(r => r.isCorrect).length;
  return (correct / typeResponses.length) * 100;
}

function getQuestionsByGrade(responses: any[]): Record<number, { correct: number; total: number }> {
  const byGrade: Record<number, { correct: number; total: number }> = {};
  
  responses.forEach(response => {
    const grade = response.gradeLevel;
    if (!byGrade[grade]) {
      byGrade[grade] = { correct: 0, total: 0 };
    }
    byGrade[grade].total++;
    if (response.isCorrect) {
      byGrade[grade].correct++;
    }
  });
  
  return byGrade;
}

function getPerformanceByType(responses: any[]): Record<string, { correct: number; total: number; percentage: number }> {
  const byType: Record<string, { correct: number; total: number; percentage: number }> = {};
  
  responses.forEach(response => {
    const type = response.questionType;
    if (!byType[type]) {
      byType[type] = { correct: 0, total: 0, percentage: 0 };
    }
    byType[type].total++;
    if (response.isCorrect) {
      byType[type].correct++;
    }
  });
  
  // Calculate percentages
  Object.keys(byType).forEach(type => {
    byType[type].percentage = (byType[type].correct / byType[type].total) * 100;
  });
  
  return byType;
}

function getPerformanceByGrade(responses: any[]): Record<number, { correct: number; total: number; percentage: number }> {
  const byGrade: Record<number, { correct: number; total: number; percentage: number }> = {};
  
  responses.forEach(response => {
    const grade = response.gradeLevel;
    if (!byGrade[grade]) {
      byGrade[grade] = { correct: 0, total: 0, percentage: 0 };
    }
    byGrade[grade].total++;
    if (response.isCorrect) {
      byGrade[grade].correct++;
    }
  });
  
  // Calculate percentages
  Object.keys(byGrade).forEach(grade => {
    const gradeNum = parseInt(grade);
    byGrade[gradeNum].percentage = (byGrade[gradeNum].correct / byGrade[gradeNum].total) * 100;
  });
  
  return byGrade;
}

function generateRecommendations(results: any): string[] {
  const recommendations: string[] = [];
  
  if (results.vocabulary_score < 70) {
    recommendations.push('Consider practicing vocabulary building exercises and reading diverse materials to expand word knowledge.');
  }
  
  if (results.grammar_score < 70) {
    recommendations.push('Focus on grammar fundamentals through targeted exercises and style guides.');
  }
  
  if (results.spelling_score < 70) {
    recommendations.push('Practice spelling through word lists, dictation exercises, and spelling games.');
  }
  
  if (results.punctuation_score < 70) {
    recommendations.push('Review punctuation rules and practice with editing exercises.');
  }
  
  if (results.reading_comprehension_score < 70) {
    recommendations.push('Improve reading comprehension through active reading strategies and practice with various text types.');
  }
  
  if (results.overall_score >= 85) {
    recommendations.push('Excellent performance! Consider advanced reading materials and writing challenges.');
  } else if (results.overall_score >= 70) {
    recommendations.push('Good performance overall. Focus on areas with lower scores for continued improvement.');
  }
  
  return recommendations;
}
