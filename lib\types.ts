

/**
 * @enum {string}
 * @description Represents the type of an English question.
 */
export enum EnglishQuestionType {
  VOCABULARY = 'VOCABULARY',
  SPELLING = 'SPELLING',
  GRAMMAR = 'GRAMMAR',
  PUNCTUATION = 'PUNCTUATION',
  READING_COMPREHENSION = 'READING_COMPREHENSION',
}

/**
 * @enum {string}
 * @description Represents the genre of a reading passage.
 */
export enum PassageGenre {
  FICTION = 'FICTION',
  NON_FICTION = 'NON_FICTION',
  POETRY = 'POETRY',
  TECHNICAL = 'TECHNICAL',
}

/**
 * @enum {number}
 * @description Represents the grade level for an English test.
 */
export enum EnglishGradeLevel {
  GRADE_5 = 5,
  GRADE_6 = 6,
  GRADE_7 = 7,
  GRADE_8 = 8,
  GRADE_9 = 9,
  GRADE_10 = 10,
  GRADE_11 = 11,
  GRADE_12 = 12,
}

/**
 * @interface EnglishQuestion
 * @description Represents a single English question.
 */
export interface EnglishQuestion {
  id: string;
  question_type: EnglishQuestionType;
  grade_level: number;
  question_text: string;
  correct_answer: string;
  explanation?: string;
  difficulty_score: number;
  times_used: number;
  times_correct: number;
  created_at: Date;
  updated_at: Date;
  is_active: boolean;
}

/**
 * @interface WrongAnswer
 * @description Represents a wrong answer for an English question.
 */
export interface WrongAnswer {
  id: string;
  question_id: string;
  wrong_answer: string;
  plausibility_score: number;
  times_shown: number;
  times_selected: number;
}

/**
 * @interface ReadingPassage
 * @description Represents a reading passage for a reading comprehension question.
 */
export interface ReadingPassage {
  id: string;
  title: string;
  passage_text: string;
  grade_level: number;
  word_count: number;
  complexity_score: number;
  genre: PassageGenre;
  times_used: number;
  is_active: boolean;
}

/**
 * @interface EnglishTestConfig
 * @description Represents the configuration for an English test.
 */
export interface EnglishTestConfig {
  min_questions_language_mastery: number;
  min_questions_reading_comp: number;
  passages_per_test: number;
  questions_per_passage_min: number;
  questions_per_passage_max: number;
  adaptive_testing_enabled: boolean;
  show_explanations_practice: boolean;
  time_limit_minutes: number;
  question_bank_threshold: number;
  ai_generation_ratio: number;
  include_none_of_above: boolean;
  none_of_above_frequency: number;
}

/**
 * @interface LanguageMasteryScore
 * @description Represents the subscores for the language mastery section of an English test.
 */
export interface LanguageMasteryScore {
  vocabulary: number;
  spelling: number;
  grammar: number;
  punctuation: number;
}

/**
 * @interface ReadingComprehensionScore
 * @description Represents the score for the reading comprehension section of an English test.
 */
export interface ReadingComprehensionScore {
  score: number;
}

/**
 * @interface EnglishTestResult
 * @description Represents the overall result of an English test.
 */
export interface EnglishTestResult {
  language_mastery_score: LanguageMasteryScore;
  reading_comprehension_score: ReadingComprehensionScore;
  overall_score: number;
  grade_level_achieved: number;
}

/**
 * @type QuestionWithAnswers
 * @description Represents an English question with its correct answer and an array of wrong answers.
 */
export type QuestionWithAnswers = EnglishQuestion & {
  wrong_answers: WrongAnswer[];
};

/**
 * @type PassageWithQuestions
 * @description Represents a reading passage with its associated questions.
 */
export type PassageWithQuestions = ReadingPassage & {
  questions: QuestionWithAnswers[];
};

/**
 * @type EnglishTestSession
 * @description Represents an English test session, extending the base TestSession with English-specific fields.
 */
export type EnglishTestSession = TestSession & {
  english_test_config: EnglishTestConfig;
  questions: QuestionWithAnswers[];
  passages: PassageWithQuestions[];
  current_question_index: number;
  current_passage_index: number;
  answers: any[]; // Define a more specific type for answers if possible
  results?: EnglishTestResult;
};

// Assuming a base TestSession interface exists
/**
 * @interface TestSession
 * @description Represents a base test session.
 */
export interface TestSession {
  id: string;
  userId: string;
  testTypeId: string;
  status: string; // e.g., 'STARTED', 'PAUSED', 'COMPLETED'
  startedAt: Date;
  completedAt?: Date;
}

export type UserWithAccess = users & {
  user_test_access: (user_test_access & { test_types: test_types })[];
};

export type TestWithDetails = tests & {
  test_types: test_types;
  test_results?: test_results[];
  users?: users;
};

export type TestResultWithDetails = test_results & {
  test: TestWithDetails;
  users: users;
};

export interface PasswordStrength {
  score: number;
  hasMinLength: boolean;
  hasUppercase: boolean;
  hasLowercase: boolean;
  hasNumber: boolean;
  hasSpecialChar: boolean;
}

export interface TestQuestion {
  id: string;
  content: string;
  questionType: string;
  options?: any;
  correctAnswer: any;
  explanation?: string;
  difficultyLevel?: number;
  gradeLevel?: string;
  metadata?: any;
}

export interface TestAnswer {
  questionId: string;
  answer: any;
  timeSpent?: number;
  isCorrect?: boolean;
}

export interface PrintFormat {
  type: 'scores-only' | 'letterhead' | 'certificate';
  orientation: 'portrait' | 'landscape';
}

declare module 'next-auth' {
  interface User {
    userType?: string;
    firstName?: string;
    lastName?: string;
    isPrimaryAdmin?: boolean;
    isDeactivated?: boolean;
    requirePasswordChange?: boolean;
  }

  interface Session {
    user: User & {
      id: string;
      userType: string;
      firstName: string;
      lastName: string;
      isPrimaryAdmin: boolean;
      isDeactivated: boolean;
      requirePasswordChange: boolean;
    };
    // Impersonation fields
    impersonating?: {
      originalAdmin: {
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        userType: string;
      };
      impersonatedUser: {
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        userType: string;
      };
    };
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    userType?: string;
    firstName?: string;
    lastName?: string;
    isPrimaryAdmin?: boolean;
    isDeactivated?: boolean;
    requirePasswordChange?: boolean;
    // Impersonation fields
    impersonating?: {
      originalAdmin: {
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        userType: string;
      };
      impersonatedUser: {
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        userType: string;
      };
    };
  }
}
