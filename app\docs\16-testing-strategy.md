# Testing Strategy Documentation

## Overview

The Rubicon Programs Testing Application implements a comprehensive testing strategy to ensure reliability, performance, and user experience quality. This document outlines testing methodologies, frameworks, and procedures used throughout the development lifecycle.

## Testing Philosophy

### Core Principles
- **Test-Driven Development (TDD)**: Write tests before implementation
- **Continuous Testing**: Automated tests run on every commit
- **User-Centric Testing**: Focus on user workflows and experiences
- **Risk-Based Testing**: Prioritize testing of critical functionality
- **Shift-Left Testing**: Catch issues early in the development cycle

### Testing Pyramid
```
    /\
   /  \     E2E Tests (10%)
  /____\    - User journeys
 /      \   - Critical paths
/________\  Integration Tests (20%)
\        /  - API endpoints
 \______/   - Database operations
  \____/    - Component integration
   \  /     Unit Tests (70%)
    \/      - Functions and methods
            - Component logic
            - Business rules
```

## Testing Frameworks & Tools

### Primary Testing Stack
```json
{
  "unit-testing": {
    "framework": "Jest 29.x",
    "assertions": "@testing-library/jest-dom",
    "react-testing": "@testing-library/react",
    "user-events": "@testing-library/user-event",
    "coverage": "Jest built-in coverage"
  },
  
  "integration-testing": {
    "api-testing": "Supertest",
    "database-testing": "Jest with test database",
    "mocking": "Jest mocks + MSW"
  },
  
  "e2e-testing": {
    "framework": "Playwright",
    "visual-regression": "Playwright Screenshots",
    "accessibility": "@axe-core/playwright"
  },
  
  "performance-testing": {
    "load-testing": "Artillery.io",
    "lighthouse": "@lighthouse-ci/cli",
    "bundle-analysis": "@next/bundle-analyzer"
  }
}
```

### Testing Configuration

#### Jest Configuration
```javascript
// jest.config.js
const nextJest = require('next/jest');

const createJestConfig = nextJest({
  dir: './',
});

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/components/(.*)$': '<rootDir>/components/$1',
    '^@/lib/(.*)$': '<rootDir>/lib/$1',
    '^@/hooks/(.*)$': '<rootDir>/hooks/$1',
    '^@/app/(.*)$': '<rootDir>/app/$1',
  },
  testEnvironment: 'jest-environment-jsdom',
  collectCoverageFrom: [
    'app/**/*.{js,jsx,ts,tsx}',
    'components/**/*.{js,jsx,ts,tsx}',
    'lib/**/*.{js,ts}',
    'hooks/**/*.{js,ts}',
    '!**/*.d.ts',
    '!**/node_modules/**',
    '!**/.next/**',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  testMatch: [
    '**/__tests__/**/*.(js|jsx|ts|tsx)',
    '**/*.(test|spec).(js|jsx|ts|tsx)',
  ],
  testPathIgnorePatterns: [
    '<rootDir>/.next/',
    '<rootDir>/node_modules/',
    '<rootDir>/e2e/',
  ],
};

module.exports = createJestConfig(customJestConfig);
```

#### Playwright Configuration
```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html'],
    ['junit', { outputFile: 'test-results/junit.xml' }],
  ],
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
```

## Unit Testing Strategy

### Component Testing
```typescript
// __tests__/components/button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Button } from '@/components/ui/button';

describe('Button Component', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument();
  });

  it('handles click events', async () => {
    const user = userEvent.setup();
    const handleClick = jest.fn();
    
    render(<Button onClick={handleClick}>Click me</Button>);
    
    await user.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('applies variant styles correctly', () => {
    const { rerender } = render(<Button variant="destructive">Delete</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-destructive');
    
    rerender(<Button variant="outline">Cancel</Button>);
    expect(button).toHaveClass('border');
  });

  it('is disabled when disabled prop is true', () => {
    render(<Button disabled>Disabled</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveClass('disabled:pointer-events-none');
  });

  it('shows loading state', () => {
    render(<Button loading>Loading...</Button>);
    
    expect(screen.getByRole('button')).toBeDisabled();
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });
});
```

### Hook Testing
```typescript
// __tests__/hooks/use-test-session.test.ts
import { renderHook, waitFor } from '@testing-library/react';
import { useTestSession } from '@/hooks/use-test-session';

// Mock fetch
global.fetch = jest.fn();

describe('useTestSession Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('initializes with loading state', () => {
    const { result } = renderHook(() => useTestSession('test-id'));
    
    expect(result.current.loading).toBe(true);
    expect(result.current.session).toBe(null);
    expect(result.current.error).toBe(null);
  });

  it('fetches test session on mount', async () => {
    const mockSession = {
      id: 'test-id',
      status: 'IN_PROGRESS',
      startedAt: '2024-01-01T10:00:00Z'
    };

    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: mockSession })
    });

    const { result } = renderHook(() => useTestSession('test-id'));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.session).toEqual(mockSession);
    expect(fetch).toHaveBeenCalledWith('/api/tests/test-id');
  });

  it('handles fetch errors correctly', async () => {
    (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

    const { result } = renderHook(() => useTestSession('test-id'));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toBe('Network error');
    expect(result.current.session).toBe(null);
  });

  it('submits answers correctly', async () => {
    const mockSession = { id: 'test-id', status: 'IN_PROGRESS' };
    
    (fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: mockSession })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, isCorrect: true })
      });

    const { result } = renderHook(() => useTestSession('test-id'));

    await waitFor(() => {
      expect(result.current.session).toEqual(mockSession);
    });

    const response = await result.current.submitAnswer('test answer');

    expect(response).toEqual({ success: true, isCorrect: true });
    expect(fetch).toHaveBeenLastCalledWith('/api/tests/test-id/answer', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ answer: 'test answer' })
    });
  });
});
```

### Utility Function Testing
```typescript
// __tests__/lib/score-calculator.test.ts
import { calculateTypingScore } from '@/lib/score-calculator';
import type { TypingSession } from '@/lib/types';

describe('calculateTypingScore', () => {
  it('calculates correct WPM for perfect accuracy', () => {
    const session: TypingSession = {
      timeElapsed: 300, // 5 minutes
      totalCharacters: 600, // 120 characters per minute
      correctCharacters: 600,
      incorrectCharacters: 0
    };

    const result = calculateTypingScore(session);

    expect(result.grossWPM).toBe(24); // 600 / 5 / 5
    expect(result.netWPM).toBe(24);
    expect(result.accuracy).toBe(100);
    expect(result.weightedWPM).toBe(24);
    expect(result.score).toBe(24);
  });

  it('penalizes errors correctly', () => {
    const session: TypingSession = {
      timeElapsed: 300,
      totalCharacters: 600,
      correctCharacters: 550,
      incorrectCharacters: 50
    };

    const result = calculateTypingScore(session);

    expect(result.grossWPM).toBe(24);
    expect(result.netWPM).toBe(14); // 24 - (50/5)
    expect(result.accuracy).toBe(91.7);
    expect(result.weightedWPM).toBe(13); // 14 * 0.917
  });

  it('handles edge cases', () => {
    const zeroTimeSession: TypingSession = {
      timeElapsed: 0,
      totalCharacters: 100,
      correctCharacters: 100,
      incorrectCharacters: 0
    };

    expect(() => calculateTypingScore(zeroTimeSession)).toThrow('Invalid time elapsed');

    const negativeScoreSession: TypingSession = {
      timeElapsed: 60,
      totalCharacters: 100,
      correctCharacters: 20,
      incorrectCharacters: 80
    };

    const result = calculateTypingScore(negativeScoreSession);
    expect(result.score).toBe(0); // Should not be negative
  });
});
```

## Integration Testing Strategy

### API Route Testing
```typescript
// __tests__/api/tests/[testId]/route.test.ts
import { createMocks } from 'node-mocks-http';
import { GET, PUT } from '@/app/api/tests/[testId]/route';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/db';

// Mock dependencies
jest.mock('next-auth');
jest.mock('@/lib/db');

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe('/api/tests/[testId]', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET', () => {
    it('returns test data for authenticated user', async () => {
      const mockSession = {
        user: { id: 'user-1', userType: 'USER' }
      };
      const mockTest = {
        id: 'test-1',
        userId: 'user-1',
        status: 'COMPLETED',
        test_types: { displayName: 'Typing Test' },
        test_results: { score: 85 }
      };

      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.tests.findUnique.mockResolvedValue(mockTest);

      const { req } = createMocks({ method: 'GET' });
      const response = await GET(req as any, { params: { testId: 'test-1' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.id).toBe('test-1');
      expect(data.data.testType).toBe('Typing Test');
    });

    it('returns 401 for unauthenticated requests', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const { req } = createMocks({ method: 'GET' });
      const response = await GET(req as any, { params: { testId: 'test-1' } });
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('UNAUTHORIZED');
    });

    it('returns 403 for unauthorized access', async () => {
      const mockSession = {
        user: { id: 'user-2', userType: 'USER' }
      };
      const mockTest = {
        id: 'test-1',
        userId: 'user-1', // Different user
        status: 'COMPLETED'
      };

      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.tests.findUnique.mockResolvedValue(mockTest);

      const { req } = createMocks({ method: 'GET' });
      const response = await GET(req as any, { params: { testId: 'test-1' } });
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error.code).toBe('FORBIDDEN');
    });

    it('returns 404 for non-existent test', async () => {
      const mockSession = {
        user: { id: 'user-1', userType: 'USER' }
      };

      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.tests.findUnique.mockResolvedValue(null);

      const { req } = createMocks({ method: 'GET' });
      const response = await GET(req as any, { params: { testId: 'non-existent' } });
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error.code).toBe('NOT_FOUND');
    });
  });

  describe('PUT', () => {
    it('updates test status for owner', async () => {
      const mockSession = {
        user: { id: 'user-1', userType: 'USER' }
      };
      const mockUpdatedTest = {
        id: 'test-1',
        userId: 'user-1',
        status: 'PAUSED',
        updatedAt: new Date()
      };

      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.tests.update.mockResolvedValue(mockUpdatedTest);

      const { req } = createMocks({
        method: 'PUT',
        body: { status: 'PAUSED' }
      });

      const response = await PUT(req as any, { params: { testId: 'test-1' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(mockPrisma.tests.update).toHaveBeenCalledWith({
        where: { id: 'test-1', userId: 'user-1' },
        data: { status: 'PAUSED', updatedAt: expect.any(Date) }
      });
    });

    it('validates request body', async () => {
      const mockSession = {
        user: { id: 'user-1', userType: 'USER' }
      };

      mockGetServerSession.mockResolvedValue(mockSession);

      const { req } = createMocks({
        method: 'PUT',
        body: { status: 'INVALID_STATUS' }
      });

      const response = await PUT(req as any, { params: { testId: 'test-1' } });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error.code).toBe('VALIDATION_ERROR');
    });
  });
});
```

### Database Integration Testing
```typescript
// __tests__/lib/database/users.test.ts
import { PrismaClient } from '@prisma/client';
import { createUserWithAccess, getUserWithTestAccess } from '@/lib/database/users';

// Use test database
const prisma = new PrismaClient({
  datasources: { db: { url: process.env.TEST_DATABASE_URL } }
});

describe('User Database Functions', () => {
  beforeEach(async () => {
    // Clean up test data
    await prisma.user_test_access.deleteMany();
    await prisma.users.deleteMany();
    await prisma.test_types.deleteMany();

    // Seed test types
    await prisma.test_types.createMany({
      data: [
        { id: 'typing', name: 'typing-keyboard', display_name: 'Typing Test' },
        { id: 'math', name: 'basic-math', display_name: 'Math Test' }
      ]
    });
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  describe('createUserWithAccess', () => {
    it('creates user with test access in transaction', async () => {
      const userData = {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        password: 'password123'
      };

      const testAccess = [
        { testTypeId: 'typing', accessType: 'UNLIMITED', grantedBy: 'admin-1' },
        { testTypeId: 'math', accessType: 'ONE_TIME', grantedBy: 'admin-1' }
      ];

      const user = await createUserWithAccess(userData, testAccess);

      expect(user.email).toBe('<EMAIL>');

      // Verify access records were created
      const userAccess = await prisma.user_test_access.findMany({
        where: { user_id: user.id }
      });

      expect(userAccess).toHaveLength(2);
      expect(userAccess.map(a => a.access_type)).toEqual(['UNLIMITED', 'ONE_TIME']);
    });

    it('rolls back transaction on failure', async () => {
      const userData = {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        password: 'password123'
      };

      const testAccess = [
        { testTypeId: 'non-existent', accessType: 'UNLIMITED', grantedBy: 'admin-1' }
      ];

      await expect(createUserWithAccess(userData, testAccess)).rejects.toThrow();

      // Verify no user was created
      const userCount = await prisma.users.count();
      expect(userCount).toBe(0);
    });
  });

  describe('getUserWithTestAccess', () => {
    it('returns user with active test access', async () => {
      // Create test user
      const user = await prisma.users.create({
        data: {
          email: '<EMAIL>',
          first_name: 'Test',
          last_name: 'User'
        }
      });

      // Create test access
      await prisma.user_test_access.create({
        data: {
          user_id: user.id,
          test_type_id: 'typing',
          access_type: 'UNLIMITED',
          is_active: true
        }
      });

      const result = await getUserWithTestAccess(user.id);

      expect(result).not.toBeNull();
      expect(result!.user_test_access).toHaveLength(1);
      expect(result!.user_test_access[0].access_type).toBe('UNLIMITED');
      expect(result!.user_test_access[0].test_types.display_name).toBe('Typing Test');
    });

    it('excludes inactive test access', async () => {
      const user = await prisma.users.create({
        data: {
          email: '<EMAIL>',
          first_name: 'Test',
          last_name: 'User'
        }
      });

      await prisma.user_test_access.create({
        data: {
          user_id: user.id,
          test_type_id: 'typing',
          access_type: 'UNLIMITED',
          is_active: false // Inactive access
        }
      });

      const result = await getUserWithTestAccess(user.id);

      expect(result!.user_test_access).toHaveLength(0);
    });
  });
});
```

## End-to-End Testing Strategy

### User Journey Testing
```typescript
// e2e/user-registration.spec.ts
import { test, expect } from '@playwright/test';

test.describe('User Registration Flow', () => {
  test('complete user registration and login', async ({ page }) => {
    // Navigate to registration page
    await page.goto('/auth/signup');

    // Fill registration form
    await page.fill('[data-testid="first-name"]', 'John');
    await page.fill('[data-testid="last-name"]', 'Doe');
    await page.fill('[data-testid="email"]', `test+${Date.now()}@example.com`);
    await page.fill('[data-testid="password"]', 'SecurePassword123!');
    await page.fill('[data-testid="zip-code"]', '12345');

    // Submit registration
    await page.click('[data-testid="register-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText(
      'Please check your email for verification instructions'
    );

    // Mock email verification (in real test, would check email)
    await page.goto('/auth/verify-email?token=mock-token');
    
    // Verify redirect to profile completion
    await expect(page).toHaveURL('/auth/complete-profile');

    // Complete profile
    await page.selectOption('[data-testid="education-level"]', 'bachelors');
    await page.check('[data-testid="english-first-yes"]');
    await page.click('[data-testid="complete-profile-button"]');

    // Verify redirect to dashboard
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('[data-testid="welcome-message"]')).toContainText('Welcome, John');
  });

  test('shows validation errors for invalid input', async ({ page }) => {
    await page.goto('/auth/signup');

    // Try to submit without filling required fields
    await page.click('[data-testid="register-button"]');

    // Verify validation errors
    await expect(page.locator('[data-testid="first-name-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="email-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="password-error"]')).toBeVisible();

    // Fill with invalid email
    await page.fill('[data-testid="email"]', 'invalid-email');
    await page.click('[data-testid="register-button"]');

    await expect(page.locator('[data-testid="email-error"]')).toContainText('Invalid email');

    // Fill with weak password
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', '123');
    await page.click('[data-testid="register-button"]');

    await expect(page.locator('[data-testid="password-error"]')).toContainText(
      'Password must be at least 8 characters'
    );
  });
});
```

### Test Taking Flow
```typescript
// e2e/typing-test.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Typing Test Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Login as test user
    await page.goto('/auth/signin');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="signin-button"]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('complete typing test successfully', async ({ page }) => {
    // Start typing test
    await page.click('[data-testid="start-typing-test"]');

    // Verify test interface loads
    await expect(page.locator('[data-testid="typing-passage"]')).toBeVisible();
    await expect(page.locator('[data-testid="typing-timer"]')).toBeVisible();
    await expect(page.locator('[data-testid="typing-stats"]')).toBeVisible();

    // Get the passage text
    const passageText = await page.locator('[data-testid="typing-passage"]').textContent();
    
    // Type the passage (simulate typing)
    const typingInput = page.locator('[data-testid="typing-input"]');
    await typingInput.focus();
    
    // Type first few characters to verify real-time stats
    await typingInput.type('The quick brown fox');
    
    // Verify real-time stats update
    await expect(page.locator('[data-testid="current-wpm"]')).not.toContainText('0');
    await expect(page.locator('[data-testid="current-accuracy"]')).toBeVisible();

    // Wait for typing to complete (mocked for speed)
    await page.evaluate(() => {
      window.dispatchEvent(new CustomEvent('test-complete', {
        detail: {
          wpm: 65,
          accuracy: 95,
          timeElapsed: 180
        }
      }));
    });

    // Verify results page
    await expect(page).toHaveURL(/\/tests\/.*\/results/);
    await expect(page.locator('[data-testid="final-wpm"]')).toContainText('65');
    await expect(page.locator('[data-testid="final-accuracy"]')).toContainText('95%');
    
    // Verify certificate download option
    await expect(page.locator('[data-testid="download-certificate"]')).toBeVisible();
  });

  test('handles test pause and resume', async ({ page }) => {
    await page.click('[data-testid="start-typing-test"]');
    await page.locator('[data-testid="typing-input"]').type('The quick');

    // Pause test
    await page.click('[data-testid="pause-test"]');
    await expect(page.locator('[data-testid="test-paused-message"]')).toBeVisible();

    // Resume test
    await page.click('[data-testid="resume-test"]');
    await expect(page.locator('[data-testid="typing-input"]')).toBeFocused();

    // Verify typing can continue
    await page.locator('[data-testid="typing-input"]').type(' brown fox');
    await expect(page.locator('[data-testid="current-wpm"]')).not.toContainText('0');
  });

  test('shows time warning and auto-submits', async ({ page }) => {
    await page.click('[data-testid="start-typing-test"]');

    // Mock timer to show warning
    await page.evaluate(() => {
      window.dispatchEvent(new CustomEvent('timer-warning', {
        detail: { timeRemaining: 60 }
      }));
    });

    await expect(page.locator('[data-testid="time-warning"]')).toBeVisible();
    await expect(page.locator('[data-testid="time-warning"]')).toContainText('1 minute remaining');

    // Mock timer expiry
    await page.evaluate(() => {
      window.dispatchEvent(new CustomEvent('timer-expired'));
    });

    // Verify auto-submission
    await expect(page).toHaveURL(/\/tests\/.*\/results/);
    await expect(page.locator('[data-testid="auto-submit-notice"]')).toBeVisible();
  });
});
```

### Accessibility Testing
```typescript
// e2e/accessibility.spec.ts
import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

test.describe('Accessibility Tests', () => {
  test('dashboard page meets WCAG standards', async ({ page }) => {
    await page.goto('/dashboard');
    
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('typing test interface is keyboard navigable', async ({ page }) => {
    await page.goto('/tests/typing');

    // Test keyboard navigation
    await page.keyboard.press('Tab');
    await expect(page.locator('[data-testid="start-test-button"]')).toBeFocused();

    await page.keyboard.press('Enter');
    await expect(page.locator('[data-testid="typing-input"]')).toBeFocused();

    // Test escape key functionality
    await page.keyboard.press('Escape');
    await expect(page.locator('[data-testid="pause-menu"]')).toBeVisible();

    // Test screen reader labels
    const typingInput = page.locator('[data-testid="typing-input"]');
    await expect(typingInput).toHaveAttribute('aria-label', 'Type the displayed text here');

    const timer = page.locator('[data-testid="test-timer"]');
    await expect(timer).toHaveAttribute('aria-live', 'polite');
  });

  test('form inputs have proper labels and error handling', async ({ page }) => {
    await page.goto('/auth/signup');

    // Check form labels
    const emailInput = page.locator('[data-testid="email"]');
    const emailLabel = page.locator('label[for="email"]');
    
    await expect(emailLabel).toBeVisible();
    await expect(emailInput).toHaveAttribute('aria-describedby');

    // Trigger validation error
    await page.click('[data-testid="register-button"]');
    
    const errorMessage = page.locator('[data-testid="email-error"]');
    await expect(errorMessage).toBeVisible();
    await expect(emailInput).toHaveAttribute('aria-invalid', 'true');
  });
});
```

## Performance Testing Strategy

### Load Testing
```javascript
// load-tests/api-load-test.yml
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 5
    - duration: 120
      arrivalRate: 10
    - duration: 60
      arrivalRate: 5
  processor: "./test-processor.js"

scenarios:
  - name: "User Registration Flow"
    weight: 30
    flow:
      - post:
          url: "/api/auth/signup"
          json:
            firstName: "Load"
            lastName: "Test"
            email: "{{ $randomString() }}@loadtest.com"
            password: "LoadTest123!"
            zipCode: "12345"
      - think: 2

  - name: "Test Taking Flow"
    weight: 50
    flow:
      - post:
          url: "/api/auth/signin"
          json:
            email: "<EMAIL>"
            password: "password123"
      - post:
          url: "/api/tests/start"
          json:
            testTypeId: "typing-keyboard"
            isPractice: false
      - think: 30
      - post:
          url: "/api/tests/{{ testId }}/answer"
          json:
            answer: "Sample typing text for load testing"
            timeSpent: 30000

  - name: "Results Viewing"
    weight: 20
    flow:
      - get:
          url: "/api/tests/{{ testId }}/results"
      - get:
          url: "/api/certificates/{{ testId }}"
```

### Performance Monitoring
```typescript
// __tests__/performance/bundle-size.test.ts
import { execSync } from 'child_process';
import { readFileSync } from 'fs';

describe('Bundle Size Performance', () => {
  test('JavaScript bundle size should be under limits', () => {
    // Build the application
    execSync('npm run build', { stdio: 'inherit' });

    // Read build manifest
    const buildId = execSync('cat .next/BUILD_ID', { encoding: 'utf8' }).trim();
    const manifest = JSON.parse(
      readFileSync(`.next/static/${buildId}/_buildManifest.js`, 'utf8')
        .replace('self.__BUILD_MANIFEST=', '')
        .replace(';self.__BUILD_MANIFEST_CB&&self.__BUILD_MANIFEST_CB()', '')
    );

    // Check main bundle size
    const mainBundles = manifest.pages['/'];
    let totalSize = 0;

    mainBundles.forEach(bundle => {
      const stats = execSync(`stat -c%s .next/static/chunks/${bundle}`, { encoding: 'utf8' });
      totalSize += parseInt(stats.trim(), 10);
    });

    // Main bundle should be under 250KB
    expect(totalSize).toBeLessThan(250 * 1024);
  });

  test('page-specific bundles should be optimally sized', () => {
    const pageBundleLimits = {
      '/dashboard': 100 * 1024,      // 100KB
      '/admin': 150 * 1024,          // 150KB
      '/tests/[testId]': 120 * 1024, // 120KB
    };

    Object.entries(pageBundleLimits).forEach(([page, limit]) => {
      // Implementation to check specific page bundle sizes
      // This would analyze the build output for each page
    });
  });
});
```

## Test Data Management

### Test Database Setup
```typescript
// __tests__/setup/database.ts
import { PrismaClient } from '@prisma/client';
import { execSync } from 'child_process';

const prisma = new PrismaClient({
  datasources: { db: { url: process.env.TEST_DATABASE_URL } }
});

export async function setupTestDatabase() {
  // Reset test database
  await prisma.$executeRaw`TRUNCATE TABLE users CASCADE`;
  await prisma.$executeRaw`TRUNCATE TABLE test_types CASCADE`;
  
  // Seed test data
  await seedTestData();
}

export async function seedTestData() {
  // Create test types
  await prisma.test_types.createMany({
    data: [
      {
        id: 'typing-keyboard',
        name: 'typing-keyboard',
        display_name: 'Keyboard Typing',
        description: 'Test typing speed and accuracy',
        is_active: true
      },
      {
        id: 'basic-math',
        name: 'basic-math',
        display_name: 'Basic Mathematics',
        description: 'Math skills assessment',
        is_active: true
      }
    ]
  });

  // Create test users
  const testUsers = await prisma.users.createMany({
    data: [
      {
        id: 'test-user-1',
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'User',
        password: '$2b$12$hashedpassword',
        email_verified: new Date(),
        user_type: 'USER'
      },
      {
        id: 'test-admin-1',
        email: '<EMAIL>',
        first_name: 'Admin',
        last_name: 'User',
        password: '$2b$12$hashedpassword',
        email_verified: new Date(),
        user_type: 'ADMIN'
      }
    ]
  });

  // Create test access
  await prisma.user_test_access.createMany({
    data: [
      {
        user_id: 'test-user-1',
        test_type_id: 'typing-keyboard',
        access_type: 'UNLIMITED',
        is_active: true
      }
    ]
  });
}

export async function cleanupTestDatabase() {
  await prisma.$disconnect();
}
```

### Test Fixtures
```typescript
// __tests__/fixtures/test-data.ts
export const testUsers = {
  standardUser: {
    id: 'user-1',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    userType: 'USER' as const,
    emailVerified: new Date('2024-01-01'),
    isDeactivated: false,
    requirePasswordChange: false
  },

  adminUser: {
    id: 'admin-1',
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User',
    userType: 'ADMIN' as const,
    isPrimaryAdmin: false,
    emailVerified: new Date('2024-01-01'),
    isDeactivated: false
  },

  primaryAdmin: {
    id: 'primary-admin-1',
    email: '<EMAIL>',
    firstName: 'Primary',
    lastName: 'Admin',
    userType: 'ADMIN' as const,
    isPrimaryAdmin: true,
    emailVerified: new Date('2024-01-01')
  }
};

export const testSessions = {
  validSession: {
    user: testUsers.standardUser,
    expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
  },

  adminSession: {
    user: testUsers.adminUser,
    expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
  }
};

export const typingTestData = {
  completedTest: {
    id: 'test-1',
    userId: 'user-1',
    testTypeId: 'typing-keyboard',
    status: 'COMPLETED' as const,
    startedAt: new Date('2024-01-01T10:00:00Z'),
    completedAt: new Date('2024-01-01T10:05:00Z'),
    typing_session: {
      timeElapsed: 300,
      totalCharacters: 600,
      correctCharacters: 570,
      incorrectCharacters: 30
    }
  }
};
```

## Continuous Integration Testing

### GitHub Actions Workflow
```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Setup test database
      run: |
        npx prisma migrate deploy
        npx prisma db seed
      env:
        TEST_DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test
    
    - name: Run unit tests
      run: npm test -- --coverage --watchAll=false
      env:
        TEST_DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/coverage-final.json

  e2e-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Install Playwright
      run: npx playwright install --with-deps
    
    - name: Build application
      run: npm run build
      env:
        DATABASE_URL: ${{ secrets.TEST_DATABASE_URL }}
    
    - name: Run E2E tests
      run: npx playwright test
      env:
        DATABASE_URL: ${{ secrets.TEST_DATABASE_URL }}
        NEXTAUTH_SECRET: test-secret-for-ci
        NEXTAUTH_URL: http://localhost:3000
    
    - name: Upload Playwright report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: playwright-report
        path: playwright-report/

  performance-tests:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build application
      run: npm run build
    
    - name: Run Lighthouse CI
      run: |
        npm install -g @lhci/cli@0.12.x
        lhci autorun
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
```

---

*This comprehensive testing strategy ensures high-quality, reliable, and performant code throughout the Rubicon Programs Testing Application development lifecycle.*