
'use client';

import { useState } from 'react';
import MathPracticeTest from '@/app/tests/math/practice/math-practice-test';

export default function MathPracticePage() {
  const [showTest, setShowTest] = useState(false);
  const [results, setResults] = useState<any>(null);

  const handleComplete = (results: any) => {
    setResults(results);
    setShowTest(false);
  };

  const handleCancel = () => {
    setShowTest(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      {!showTest && !results && (
        <div className="max-w-2xl mx-auto text-center">
          <h1 className="text-3xl font-bold mb-8">Math Practice Test</h1>
          <button 
            onClick={() => setShowTest(true)}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Start Math Practice Test
          </button>
        </div>
      )}

      {showTest && (
        <MathPracticeTest
          onComplete={handleComplete}
          onCancel={handleCancel}
        />
      )}

      {results && (
        <div className="max-w-2xl mx-auto text-center">
          <h2 className="text-2xl font-bold mb-4">Practice Test Results</h2>
          <div className="bg-white p-6 rounded-lg shadow">
            {/* TODO: Display math practice test results */}
            <p>Score: {results.score}</p>
          </div>
          <button 
            onClick={() => {setResults(null); setShowTest(false);}}
            className="mt-4 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            Try Again
          </button>
        </div>
      )}
    </div>
  );
}
