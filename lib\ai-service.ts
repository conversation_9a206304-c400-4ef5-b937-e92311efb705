
import { prisma } from '@/lib/db';

export interface AIQuestionRequest {
  gradeLevel: number;
  questionType?: string;
  difficulty?: number;
}

export interface AIQuestionResponse {
  questionText: string;
  answer: string;
  explanation?: string;
  category?: string;
}

export interface AIServiceConfig {
  provider: string;
  apiKey?: string;
  model: string;
  enabled: boolean;
}

// Abstract AI service interface
export abstract class AIService {
  constructor(protected config: AIServiceConfig) {}

  abstract generateMathQuestion(request: AIQuestionRequest): Promise<AIQuestionResponse>;
}

// Abacus AI implementation
export class AbacusAIService extends AIService {
  async generateMathQuestion(request: AIQuestionRequest): Promise<AIQuestionResponse> {
    const { gradeLevel, difficulty = 3 } = request;

    const response = await fetch('https://apps.abacus.ai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey || process.env.ABACUSAI_API_KEY}`
      },
      body: JSON.stringify({
        model: this.config.model,
        messages: [
          {
            role: 'system',
            content: `You are an expert math question generator for ${gradeLevel}th grade level. Create accurate, grade-appropriate math problems with clear solutions.`
          },
          {
            role: 'user',
            content: `Generate a math question for grade ${gradeLevel}. Difficulty level: ${difficulty}/5.

Please respond in JSON format with the following structure:
{
  "questionText": "The math problem statement",
  "answer": "The correct answer (just the final answer)",
  "explanation": "Step-by-step solution explaining how to solve the problem",
  "category": "arithmetic|algebra|geometry|word_problem"
}

Make sure the problem is appropriate for grade ${gradeLevel} level and the answer is mathematically correct.`
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 800,
        temperature: 0.7
      })
    });

    if (!response.ok) {
      throw new Error(`AI service request failed: ${response.status}`);
    }

    const data = await response.json();
    const content = data.choices[0]?.message?.content;
    
    if (!content) {
      throw new Error('No content received from AI service');
    }

    try {
      const questionData = JSON.parse(content);
      
      // Validate required fields
      if (!questionData.questionText || !questionData.answer) {
        throw new Error('AI response missing required fields');
      }

      return {
        questionText: questionData.questionText,
        answer: questionData.answer.toString(),
        explanation: questionData.explanation,
        category: questionData.category
      };
    } catch (error) {
      throw new Error('Invalid JSON response from AI service');
    }
  }
}

// OpenAI implementation (placeholder for future use)
export class OpenAIService extends AIService {
  async generateMathQuestion(request: AIQuestionRequest): Promise<AIQuestionResponse> {
    // Placeholder for OpenAI implementation
    throw new Error('OpenAI service not yet implemented');
  }
}

// Factory function to create AI service instances
export function createAIService(config: AIServiceConfig): AIService {
  switch (config.provider.toLowerCase()) {
    case 'abacusai':
      return new AbacusAIService(config);
    case 'openai':
      return new OpenAIService(config);
    default:
      throw new Error(`Unsupported AI provider: ${config.provider}`);
  }
}

// Function to get AI service configuration from database
export async function getAIServiceConfig(): Promise<AIServiceConfig> {
  const settings = await prisma.app_settings.findFirst();
  
  if (!settings) {
    // Return default config if no settings found
    return {
      provider: 'abacusai',
      apiKey: process.env.ABACUSAI_API_KEY,
      model: 'gpt-4.1-mini',
      enabled: true
    };
  }

  return {
    provider: settings.aiServiceProvider,
    apiKey: settings.aiServiceApiKey || process.env.ABACUSAI_API_KEY,
    model: settings.aiServiceModel,
    enabled: settings.aiServiceEnabled
  };
}

// Main function to generate AI questions
export async function generateAIQuestion(gradeLevel: number): Promise<AIQuestionResponse> {
  const config = await getAIServiceConfig();
  
  if (!config.enabled) {
    throw new Error('AI question generation is currently disabled');
  }

  const service = createAIService(config);
  return service.generateMathQuestion({ gradeLevel });
}
