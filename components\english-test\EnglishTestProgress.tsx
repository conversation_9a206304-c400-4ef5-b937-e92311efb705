import React from 'react';
import { Progress } from '@/components/ui/progress';

interface EnglishTestProgressProps {
  languageMastery: number;
  readingComprehension: number;
  currentGradeLevel: string;
  questionTypeBreakdown: {
    [key: string]: number;
  };
  timeRemaining: number;
}

const EnglishTestProgress: React.FC<EnglishTestProgressProps> = ({ languageMastery, readingComprehension, currentGradeLevel, questionTypeBreakdown, timeRemaining }) => {
  return (
    <div className="p-4 border rounded-lg">
      <h3 className="text-lg font-semibold">Test Progress</h3>
      <div className="my-4">
        <p>Language Mastery</p>
        <Progress value={languageMastery} />
        <p>Reading Comprehension</p>
        <Progress value={readingComprehension} />
      </div>
      <div className="my-4">
        <p>Current Grade Level: {currentGradeLevel}</p>
        <p>Time Remaining: {new Date(timeRemaining * 1000).toISOString().substr(11, 8)}</p>
      </div>
      <div>
        <h4 className="font-semibold">Question Breakdown</h4>
        <ul>
          {Object.entries(questionTypeBreakdown).map(([type, count]) => (
            <li key={type}>{type}: {count}</li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default EnglishTestProgress;

