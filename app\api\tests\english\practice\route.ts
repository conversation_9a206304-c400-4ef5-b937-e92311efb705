import { NextRequest, NextResponse } from 'next/server';

// Types for practice test
interface PracticeQuestion {
  id: string;
  type: 'multiple-choice' | 'spelling' | 'grammar' | 'reading-comprehension';
  questionText: string;
  options?: string[];
  correctAnswer: string;
  explanation: string;
  gradeLevel: string;
  passageId?: string;
}

interface ReadingPassage {
  id: string;
  title: string;
  content: string;
  gradeLevel: string;
  questions: PracticeQuestion[];
}

interface PracticeSession {
  id: string;
  gradeLevel: string;
  questions: PracticeQuestion[];
  passages: ReadingPassage[];
  currentQuestionIndex: number;
  totalQuestions: number;
  startTime: number;
  completed: boolean;
}

// In-memory storage for practice sessions (temporary, no persistence)
const practiceSessions = new Map<string, PracticeSession>();

// Mock question bank - in real implementation, this would come from database
const mockQuestionBank = {
  'grade-3': {
    'multiple-choice': [
      {
        id: 'mc-3-1',
        type: 'multiple-choice' as const,
        questionText: 'Which word rhymes with "cat"?',
        options: ['dog', 'bat', 'fish', 'bird'],
        correctAnswer: 'bat',
        explanation: '"Bat" rhymes with "cat" because they both end with the "-at" sound.',
        gradeLevel: 'grade-3'
      },
      {
        id: 'mc-3-2',
        type: 'multiple-choice' as const,
        questionText: 'What is the opposite of "happy"?',
        options: ['sad', 'glad', 'mad', 'bad'],
        correctAnswer: 'sad',
        explanation: '"Sad" is the opposite of "happy" - they are antonyms.',
        gradeLevel: 'grade-3'
      }
    ],
    'spelling': [
      {
        id: 'sp-3-1',
        type: 'spelling' as const,
        questionText: 'Spell the word that means "a small furry animal that meows": ___',
        correctAnswer: 'cat',
        explanation: 'The correct spelling is "cat" - c-a-t.',
        gradeLevel: 'grade-3'
      }
    ],
    'grammar': [
      {
        id: 'gr-3-1',
        type: 'grammar' as const,
        questionText: 'Which sentence is correct?',
        options: ['I are happy.', 'I am happy.', 'I is happy.', 'I be happy.'],
        correctAnswer: 'I am happy.',
        explanation: '"I am" is the correct form. "I" always uses "am" in the present tense.',
        gradeLevel: 'grade-3'
      }
    ]
  },
  'grade-5': {
    'multiple-choice': [
      {
        id: 'mc-5-1',
        type: 'multiple-choice' as const,
        questionText: 'Which word is a synonym for "enormous"?',
        options: ['tiny', 'huge', 'small', 'medium'],
        correctAnswer: 'huge',
        explanation: '"Huge" is a synonym for "enormous" - both mean very large.',
        gradeLevel: 'grade-5'
      }
    ],
    'spelling': [
      {
        id: 'sp-5-1',
        type: 'spelling' as const,
        questionText: 'Spell the word that means "to make something different": ___',
        correctAnswer: 'change',
        explanation: 'The correct spelling is "change" - c-h-a-n-g-e.',
        gradeLevel: 'grade-5'
      }
    ],
    'grammar': [
      {
        id: 'gr-5-1',
        type: 'grammar' as const,
        questionText: 'Identify the subject in this sentence: "The tall tree swayed in the wind."',
        options: ['tall', 'tree', 'swayed', 'wind'],
        correctAnswer: 'tree',
        explanation: 'The subject is "tree" (with "tall" as an adjective describing it). The subject is what the sentence is about.',
        gradeLevel: 'grade-5'
      }
    ]
  }
};

const mockReadingPassages = {
  'grade-3': [
    {
      id: 'passage-3-1',
      title: 'The Little Garden',
      content: `Sam loved to work in his little garden. Every morning, he would water his tomatoes and carrots. The plants grew bigger each day. Sam's favorite part was picking the ripe vegetables to share with his family. His mom made delicious soup with the vegetables Sam grew.`,
      gradeLevel: 'grade-3',
      questions: [
        {
          id: 'rc-3-1',
          type: 'reading-comprehension' as const,
          questionText: 'What did Sam do every morning?',
          options: ['Pick vegetables', 'Water his plants', 'Make soup', 'Share with family'],
          correctAnswer: 'Water his plants',
          explanation: 'The passage states that "Every morning, he would water his tomatoes and carrots."',
          gradeLevel: 'grade-3',
          passageId: 'passage-3-1'
        },
        {
          id: 'rc-3-2',
          type: 'reading-comprehension' as const,
          questionText: 'What was Sam\'s favorite part of gardening?',
          options: ['Watering plants', 'Watching them grow', 'Picking vegetables', 'Making soup'],
          correctAnswer: 'Picking vegetables',
          explanation: 'The passage says "Sam\'s favorite part was picking the ripe vegetables to share with his family."',
          gradeLevel: 'grade-3',
          passageId: 'passage-3-1'
        }
      ]
    }
  ],
  'grade-5': [
    {
      id: 'passage-5-1',
      title: 'The Science of Rainbows',
      content: `Rainbows appear when sunlight and rain occur at the same time. The water droplets in the air act like tiny prisms, splitting white light into its component colors. This process is called refraction. The colors always appear in the same order: red, orange, yellow, green, blue, indigo, and violet. Scientists use the acronym "ROY G. BIV" to remember this sequence. Double rainbows occur when light is reflected twice inside the water droplets.`,
      gradeLevel: 'grade-5',
      questions: [
        {
          id: 'rc-5-1',
          type: 'reading-comprehension' as const,
          questionText: 'What causes rainbows to appear?',
          options: ['Only sunlight', 'Only rain', 'Sunlight and rain together', 'Wind and clouds'],
          correctAnswer: 'Sunlight and rain together',
          explanation: 'The passage states that "Rainbows appear when sunlight and rain occur at the same time."',
          gradeLevel: 'grade-5',
          passageId: 'passage-5-1'
        },
        {
          id: 'rc-5-2',
          type: 'reading-comprehension' as const,
          questionText: 'What does "ROY G. BIV" help scientists remember?',
          options: ['Types of light', 'Order of colors', 'Weather patterns', 'Water droplet sizes'],
          correctAnswer: 'Order of colors',
          explanation: 'The passage explains that scientists use "ROY G. BIV" to remember the sequence of colors in a rainbow.',
          gradeLevel: 'grade-5',
          passageId: 'passage-5-1'
        }
      ]
    }
  ]
};

// Helper function to generate practice test questions
function generatePracticeQuestions(gradeLevel: string): { questions: PracticeQuestion[], passages: ReadingPassage[] } {
  const bankQuestions = mockQuestionBank[gradeLevel as keyof typeof mockQuestionBank] || mockQuestionBank['grade-3'];
  const passages = mockReadingPassages[gradeLevel as keyof typeof mockReadingPassages] || mockReadingPassages['grade-3'];
  
  // Get 50% of full test (assuming full test is ~40 questions, practice is ~20)
  const practiceQuestionCount = 20;
  const questions: PracticeQuestion[] = [];
  
  // Mix question types (excluding reading comprehension which comes from passages)
  const types = ['multiple-choice', 'spelling', 'grammar'];
  const questionsPerType = Math.floor(practiceQuestionCount * 0.6 / types.length); // 60% for non-reading
  
  types.forEach(type => {
    const typeQuestions = bankQuestions[type as keyof typeof bankQuestions] || [];
    for (let i = 0; i < questionsPerType && i < typeQuestions.length; i++) {
      questions.push(typeQuestions[i]);
    }
  });
  
  // Add 1-2 reading passages (40% of questions)
  const selectedPassages = passages.slice(0, 2);
  selectedPassages.forEach(passage => {
    questions.push(...passage.questions);
  });
  
  // Shuffle questions
  for (let i = questions.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [questions[i], questions[j]] = [questions[j], questions[i]];
  }
  
  return { questions, passages: selectedPassages };
}

// Generate session ID
function generateSessionId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

// POST: Create new practice session
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { gradeLevel } = body;
    
    if (!gradeLevel) {
      return NextResponse.json({ error: 'Grade level is required' }, { status: 400 });
    }
    
    const sessionId = generateSessionId();
    const { questions, passages } = generatePracticeQuestions(gradeLevel);
    
    const session: PracticeSession = {
      id: sessionId,
      gradeLevel,
      questions,
      passages,
      currentQuestionIndex: 0,
      totalQuestions: questions.length,
      startTime: Date.now(),
      completed: false
    };
    
    // Store session temporarily (no persistence)
    practiceSessions.set(sessionId, session);
    
    // Clean up old sessions (keep only last hour)
    const oneHourAgo = Date.now() - 60 * 60 * 1000;
    for (const [id, sess] of practiceSessions.entries()) {
      if (sess.startTime < oneHourAgo) {
        practiceSessions.delete(id);
      }
    }
    
    return NextResponse.json({
      sessionId,
      gradeLevel,
      totalQuestions: questions.length,
      currentQuestion: questions[0],
      passages: passages.map(p => ({ id: p.id, title: p.title, content: p.content })),
      progress: {
        current: 1,
        total: questions.length,
        percentage: Math.round((1 / questions.length) * 100)
      }
    });
    
  } catch (error) {
    console.error('Error creating practice session:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT: Submit answer and get immediate feedback
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { sessionId, questionId, answer } = body;
    
    if (!sessionId || !questionId || answer === undefined) {
      return NextResponse.json({ error: 'Session ID, question ID, and answer are required' }, { status: 400 });
    }
    
    const session = practiceSessions.get(sessionId);
    if (!session) {
      return NextResponse.json({ error: 'Practice session not found' }, { status: 404 });
    }
    
    if (session.completed) {
      return NextResponse.json({ error: 'Practice session already completed' }, { status: 400 });
    }
    
    const currentQuestion = session.questions[session.currentQuestionIndex];
    if (!currentQuestion || currentQuestion.id !== questionId) {
      return NextResponse.json({ error: 'Invalid question' }, { status: 400 });
    }
    
    // Validate answer format
    let isCorrect = false;
    let normalizedAnswer = answer;
    
    if (currentQuestion.type === 'spelling') {
      normalizedAnswer = answer.toLowerCase().trim();
      isCorrect = normalizedAnswer === currentQuestion.correctAnswer.toLowerCase();
    } else {
      isCorrect = answer === currentQuestion.correctAnswer;
    }
    
    // Move to next question
    session.currentQuestionIndex++;
    const isLastQuestion = session.currentQuestionIndex >= session.questions.length;
    
    if (isLastQuestion) {
      session.completed = true;
    }
    
    const response: any = {
      feedback: {
        correct: isCorrect,
        correctAnswer: currentQuestion.correctAnswer,
        explanation: currentQuestion.explanation,
        userAnswer: answer
      },
      completed: isLastQuestion,
      progress: {
        current: session.currentQuestionIndex + 1,
        total: session.questions.length,
        percentage: Math.round(((session.currentQuestionIndex + 1) / session.questions.length) * 100)
      }
    };
    
    // Add next question if not completed
    if (!isLastQuestion) {
      const nextQuestion = session.questions[session.currentQuestionIndex];
      response.nextQuestion = nextQuestion;
      
      // Include passage if it's a reading comprehension question
      if (nextQuestion.type === 'reading-comprehension' && nextQuestion.passageId) {
        const passage = session.passages.find(p => p.id === nextQuestion.passageId);
        if (passage) {
          response.passage = {
            id: passage.id,
            title: passage.title,
            content: passage.content
          };
        }
      }
    } else {
      // Practice completed - no storage, just return completion message
      response.completionMessage = {
        title: 'Practice Complete!',
        message: 'Great job completing the practice test! You can now return to the dashboard or start another practice session.',
        showReturnToDashboard: true
      };
      
      // Clean up session
      setTimeout(() => {
        practiceSessions.delete(sessionId);
      }, 5000); // Keep for 5 seconds to allow final requests
    }
    
    return NextResponse.json(response);
    
  } catch (error) {
    console.error('Error submitting practice answer:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET: Get current question or session status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');
    
    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }
    
    const session = practiceSessions.get(sessionId);
    if (!session) {
      return NextResponse.json({ error: 'Practice session not found' }, { status: 404 });
    }
    
    if (session.completed) {
      return NextResponse.json({
        completed: true,
        completionMessage: {
          title: 'Practice Complete!',
          message: 'This practice session has been completed.',
          showReturnToDashboard: true
        }
      });
    }
    
    const currentQuestion = session.questions[session.currentQuestionIndex];
    const response: any = {
      sessionId,
      gradeLevel: session.gradeLevel,
      currentQuestion,
      progress: {
        current: session.currentQuestionIndex + 1,
        total: session.questions.length,
        percentage: Math.round(((session.currentQuestionIndex + 1) / session.questions.length) * 100)
      },
      completed: false
    };
    
    // Include passage if it's a reading comprehension question
    if (currentQuestion.type === 'reading-comprehension' && currentQuestion.passageId) {
      const passage = session.passages.find(p => p.id === currentQuestion.passageId);
      if (passage) {
        response.passage = {
          id: passage.id,
          title: passage.title,
          content: passage.content
        };
      }
    }
    
    return NextResponse.json(response);
    
  } catch (error) {
    console.error('Error getting practice session:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE: End practice session early
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');
    
    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }
    
    const session = practiceSessions.get(sessionId);
    if (!session) {
      return NextResponse.json({ error: 'Practice session not found' }, { status: 404 });
    }
    
    // Clean up session
    practiceSessions.delete(sessionId);
    
    return NextResponse.json({
      message: 'Practice session ended successfully',
      returnToDashboard: true
    });
    
  } catch (error) {
    console.error('Error ending practice session:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
