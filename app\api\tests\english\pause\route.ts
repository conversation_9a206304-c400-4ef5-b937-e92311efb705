import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';

// POST /api/tests/english/pause - Pause English test
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { testId } = body;

    if (!testId) {
      return NextResponse.json({ error: 'Test ID is required' }, { status: 400 });
    }

    // Get test session
    const test = await prisma.tests.findFirst({
      where: {
        id: testId,
        userId: session.user.id,
        status: 'STARTED',
      },
    });

    if (!test) {
      return NextResponse.json({ error: 'Active test session not found' }, { status: 404 });
    }

    // Check if test pausing is enabled
    const appSettings = await prisma.app_settings.findFirst();
    if (!appSettings?.testPausingEnabled) {
      return NextResponse.json({ error: 'Test pausing is not enabled' }, { status: 403 });
    }

    // Update test status to paused
    await prisma.tests.update({
      where: { id: testId },
      data: {
        status: 'PAUSED',
        pausedAt: new Date(),
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        testId,
        status: 'PAUSED',
        pausedAt: new Date(),
        message: 'Test has been paused successfully',
      },
    });

  } catch (error) {
    console.error('Error pausing English test:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT /api/tests/english/pause - Resume English test
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { testId } = body;

    if (!testId) {
      return NextResponse.json({ error: 'Test ID is required' }, { status: 400 });
    }

    // Get test session
    const test = await prisma.tests.findFirst({
      where: {
        id: testId,
        userId: session.user.id,
        status: 'PAUSED',
      },
    });

    if (!test) {
      return NextResponse.json({ error: 'Paused test session not found' }, { status: 404 });
    }

    // Check if test has expired (optional time limit check)
    if (test.timeLimit && test.pausedAt) {
      const pausedTime = test.pausedAt.getTime();
      const startTime = test.startedAt.getTime();
      const elapsedTimeBeforePause = Math.floor((pausedTime - startTime) / 1000); // in seconds
      const timeLimit = test.timeLimit * 60; // convert minutes to seconds

      if (elapsedTimeBeforePause >= timeLimit) {
        return NextResponse.json({ 
          error: 'Test time limit exceeded while paused',
          expired: true 
        }, { status: 410 });
      }
    }

    // Update test status back to started
    await prisma.tests.update({
      where: { id: testId },
      data: {
        status: 'STARTED',
        pausedAt: null,
        updatedAt: new Date(),
      },
    });

    // Get current test state to return
    const sessionData = test.additionalData as any;
    const englishSession = sessionData?.englishTestSession;

    return NextResponse.json({
      success: true,
      data: {
        testId,
        status: 'STARTED',
        resumedAt: new Date(),
        currentGradeLevel: englishSession?.currentGradeLevel || 8,
        questionsAnswered: englishSession?.questionsAnswered?.length || 0,
        runningScores: englishSession?.runningScores || {
          languageMastery: 0,
          readingComprehension: 0,
          overall: 0,
        },
        timeRemaining: test.timeLimit ? 
          Math.max(0, test.timeLimit * 60 - Math.floor((Date.now() - test.startedAt.getTime()) / 1000)) : 
          null,
        message: 'Test has been resumed successfully',
      },
    });

  } catch (error) {
    console.error('Error resuming English test:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET /api/tests/english/pause - Get pause status
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const testId = searchParams.get('testId');

    if (!testId) {
      return NextResponse.json({ error: 'Test ID is required' }, { status: 400 });
    }

    // Get test session
    const test = await prisma.tests.findFirst({
      where: {
        id: testId,
        userId: session.user.id,
        status: { in: ['STARTED', 'PAUSED'] },
      },
    });

    if (!test) {
      return NextResponse.json({ error: 'Test session not found' }, { status: 404 });
    }

    const sessionData = test.additionalData as any;
    const englishSession = sessionData?.englishTestSession;

    let timeRemaining = null;
    if (test.timeLimit) {
      const now = Date.now();
      let elapsedTime;

      if (test.status === 'PAUSED' && test.pausedAt) {
        // Calculate elapsed time up to pause
        elapsedTime = Math.floor((test.pausedAt.getTime() - test.startedAt.getTime()) / 1000);
      } else {
        // Calculate total elapsed time
        elapsedTime = Math.floor((now - test.startedAt.getTime()) / 1000);
      }

      timeRemaining = Math.max(0, test.timeLimit * 60 - elapsedTime);
    }

    return NextResponse.json({
      success: true,
      data: {
        testId,
        status: test.status,
        startedAt: test.startedAt,
        pausedAt: test.pausedAt,
        timeLimit: test.timeLimit,
        timeRemaining,
        canPause: await canPauseTest(),
        currentState: {
          currentGradeLevel: englishSession?.currentGradeLevel || 8,
          questionsAnswered: englishSession?.questionsAnswered?.length || 0,
          runningScores: englishSession?.runningScores || {
            languageMastery: 0,
            readingComprehension: 0,
            overall: 0,
          },
        },
      },
    });

  } catch (error) {
    console.error('Error getting English test pause status:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

async function canPauseTest(): Promise<boolean> {
  const appSettings = await prisma.app_settings.findFirst();
  return appSettings?.testPausingEnabled || false;
}
