# Deployment Documentation

## Overview

This document provides comprehensive deployment guidance for the Rubicon Programs Testing Application, covering various deployment strategies, infrastructure requirements, and operational procedures for production environments.

## Deployment Architecture

### System Requirements

#### Minimum Production Requirements
```yaml
Server Specifications:
  CPU: 4 cores (2.4 GHz+)
  RAM: 8 GB
  Storage: 100 GB SSD
  Network: 1 Gbps connection
  OS: Ubuntu 20.04 LTS or CentOS 8+

Database Requirements:
  PostgreSQL: Version 13+
  CPU: 2 cores
  RAM: 4 GB
  Storage: 50 GB SSD (expandable)
  Connections: 100 concurrent

Load Balancer:
  Nginx or AWS ALB
  SSL/TLS termination
  HTTP/2 support
  WebSocket support for real-time features
```

#### Recommended Production Setup
```yaml
Application Servers (2+ instances):
  CPU: 8 cores
  RAM: 16 GB
  Storage: 200 GB SSD

Database Cluster:
  Primary: 4 cores, 8 GB RAM, 100 GB SSD
  Read Replica: 2 cores, 4 GB RAM, 100 GB SSD
  Backup Storage: 500 GB

Redis Cache:
  CPU: 2 cores
  RAM: 4 GB
  Persistence: AOF + RDB

CDN: CloudFlare or AWS CloudFront
Monitoring: DataDog, New Relic, or Prometheus
Backup: Automated daily backups with 30-day retention
```

### Infrastructure Components

```mermaid
graph TB
    Internet[Internet Users] --> CDN[CDN/CloudFlare]
    CDN --> LB[Load Balancer/Nginx]
    LB --> APP1[App Server 1]
    LB --> APP2[App Server 2]
    APP1 --> DB[(PostgreSQL Primary)]
    APP2 --> DB
    APP1 --> REDIS[(Redis Cache)]
    APP2 --> REDIS
    DB --> REPLICA[(Read Replica)]
    DB --> BACKUP[(Backup Storage)]
    APP1 --> STORAGE[(File Storage)]
    APP2 --> STORAGE
```

## Deployment Strategies

### 1. Docker Deployment

#### Dockerfile Configuration
```dockerfile
# Multi-stage build for production
FROM node:18-alpine AS base

# Dependencies stage
FROM base AS deps
WORKDIR /app
COPY package.json package-lock.json ./
RUN npm ci --only=production && npm cache clean --force

# Build stage
FROM base AS builder
WORKDIR /app
COPY package.json package-lock.json ./
RUN npm ci
COPY . .
RUN npm run build

# Production stage
FROM base AS runner
WORKDIR /app

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy built application
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

#### Docker Compose Configuration
```yaml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
    depends_on:
      - db
      - redis
    restart: unless-stopped
    networks:
      - app-network

  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - app-network

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - app-network

volumes:
  postgres_data:
  redis_data:

networks:
  app-network:
    driver: bridge
```

### 2. Kubernetes Deployment

#### Kubernetes Manifests
```yaml
# app-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rubicon-app
  labels:
    app: rubicon-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: rubicon-app
  template:
    metadata:
      labels:
        app: rubicon-app
    spec:
      containers:
      - name: rubicon-app
        image: rubicon-testing:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: database-url
        - name: NEXTAUTH_SECRET
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: nextauth-secret
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5

---
# app-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: rubicon-app-service
spec:
  selector:
    app: rubicon-app
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: ClusterIP

---
# app-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rubicon-app-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - rubiconprograms.org
    secretName: rubicon-tls
  rules:
  - host: rubiconprograms.org
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: rubicon-app-service
            port:
              number: 80
```

### 3. Cloud Platform Deployments

#### Vercel Deployment
```json
// vercel.json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/next"
    }
  ],
  "routes": [
    {
      "src": "/api/(.*)",
      "dest": "/api/$1"
    },
    {
      "src": "/(.*)",
      "dest": "/$1"
    }
  ],
  "env": {
    "DATABASE_URL": "@database_url",
    "NEXTAUTH_SECRET": "@nextauth_secret",
    "NEXTAUTH_URL": "@nextauth_url"
  },
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  }
}
```

#### AWS ECS Deployment
```json
{
  "family": "rubicon-testing-app",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::account:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "rubicon-app",
      "image": "account.dkr.ecr.region.amazonaws.com/rubicon-testing:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "essential": true,
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "DATABASE_URL",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:database-url"
        },
        {
          "name": "NEXTAUTH_SECRET",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:nextauth-secret"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/rubicon-testing-app",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"],
        "interval": 30,
        "timeout": 5,
        "retries": 3,
        "startPeriod": 60
      }
    }
  ]
}
```

## Database Deployment

### PostgreSQL Setup

#### Production Database Configuration
```sql
-- Create production database
CREATE DATABASE rubicon_testing_production;
CREATE USER rubicon_prod WITH ENCRYPTED PASSWORD 'secure_production_password';
GRANT ALL PRIVILEGES ON DATABASE rubicon_testing_production TO rubicon_prod;

-- Performance tuning
ALTER SYSTEM SET shared_buffers = '2GB';
ALTER SYSTEM SET effective_cache_size = '6GB';
ALTER SYSTEM SET maintenance_work_mem = '512MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET effective_io_concurrency = 200;

-- Reload configuration
SELECT pg_reload_conf();
```

#### Database Migration Strategy
```bash
#!/bin/bash
# deploy-database.sh

set -e

echo "Starting database deployment..."

# Backup current database
pg_dump $DATABASE_URL > "backup_$(date +%Y%m%d_%H%M%S).sql"

# Run migrations
npx prisma migrate deploy

# Update indexes
psql $DATABASE_URL -f scripts/create-indexes.sql

# Update stored procedures
psql $DATABASE_URL -f scripts/stored-procedures.sql

# Analyze tables for query optimization
psql $DATABASE_URL -c "ANALYZE;"

echo "Database deployment completed successfully"
```

#### Read Replica Setup
```sql
-- On primary server
CREATE USER replicator WITH REPLICATION LOGIN ENCRYPTED PASSWORD 'replica_password';

-- postgresql.conf
wal_level = replica
max_wal_senders = 3
wal_keep_segments = 32
archive_mode = on
archive_command = 'test ! -f /var/lib/postgresql/archive/%f && cp %p /var/lib/postgresql/archive/%f'

-- pg_hba.conf
host replication replicator replica_ip/32 md5

-- On replica server
pg_basebackup -h primary_ip -D /var/lib/postgresql/data -U replicator -W -v -P
```

## SSL/TLS Configuration

### Nginx SSL Configuration
```nginx
# /etc/nginx/sites-available/rubicon-testing
server {
    listen 80;
    server_name rubiconprograms.org www.rubiconprograms.org;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name rubiconprograms.org www.rubiconprograms.org;

    # SSL Configuration
    ssl_certificate /etc/ssl/certs/rubicon.crt;
    ssl_certificate_key /etc/ssl/private/rubicon.key;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Modern SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # OCSP stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    ssl_trusted_certificate /etc/ssl/certs/ca-certificates.crt;

    # Application proxy
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # Static file caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        proxy_pass http://127.0.0.1:3000;
    }

    # Security.txt
    location = /.well-known/security.txt {
        return 200 "Contact: <EMAIL>\nExpires: 2025-12-31T23:59:59.000Z\n";
        add_header Content-Type text/plain;
    }
}
```

## Environment Setup

### Production Environment Configuration
```bash
#!/bin/bash
# production-setup.sh

# System updates
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PostgreSQL
sudo apt-get install -y postgresql postgresql-contrib

# Install Redis
sudo apt-get install -y redis-server

# Install Nginx
sudo apt-get install -y nginx

# Install PM2 for process management
sudo npm install -g pm2

# Install system monitoring tools
sudo apt-get install -y htop iotop netstat-nat

# Configure firewall
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw --force enable

# Configure log rotation
sudo tee /etc/logrotate.d/rubicon-testing << EOF
/var/log/rubicon-testing/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 nodejs nodejs
    postrotate
        pm2 reloadLogs
    endscript
}
EOF
```

### Application Process Management
```json
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'rubicon-testing',
    script: './server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    log_file: '/var/log/rubicon-testing/combined.log',
    error_file: '/var/log/rubicon-testing/error.log',
    out_file: '/var/log/rubicon-testing/out.log',
    log_date_format: 'YYYY-MM-DD HH:mm Z',
    merge_logs: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    restart_delay: 4000,
    kill_timeout: 5000,
    listen_timeout: 3000,
    
    // Auto-restart on crash
    autorestart: true,
    max_restarts: 10,
    min_uptime: '10s',
    
    // Health monitoring
    health_check_grace_period: 3000,
    health_check_fatal: true,
    
    // Environment specific config
    env_production: {
      NODE_ENV: 'production',
      INSTANCE_ID: process.env.HOSTNAME
    }
  }]
};
```

## Monitoring & Health Checks

### Application Health Checks
```typescript
// app/api/health/route.ts
import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

interface HealthStatus {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  version: string;
  uptime: number;
  checks: {
    database: boolean;
    redis?: boolean;
    fileSystem: boolean;
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
  };
  errors?: string[];
}

export async function GET(): Promise<NextResponse<HealthStatus>> {
  const startTime = Date.now();
  const errors: string[] = [];
  
  const status: HealthStatus = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.APP_VERSION || '1.0.0',
    uptime: process.uptime(),
    checks: {
      database: false,
      fileSystem: false,
      memory: {
        used: 0,
        total: 0,
        percentage: 0
      }
    }
  };

  // Database health check
  try {
    await prisma.$queryRaw`SELECT 1`;
    status.checks.database = true;
  } catch (error) {
    errors.push(`Database connection failed: ${error.message}`);
  }

  // Memory check
  const memUsage = process.memoryUsage();
  status.checks.memory = {
    used: memUsage.heapUsed,
    total: memUsage.heapTotal,
    percentage: (memUsage.heapUsed / memUsage.heapTotal) * 100
  };

  // File system check
  try {
    const fs = await import('fs/promises');
    await fs.access('./package.json');
    status.checks.fileSystem = true;
  } catch (error) {
    errors.push(`File system check failed: ${error.message}`);
  }

  // Determine overall status
  if (errors.length > 0) {
    status.status = 'unhealthy';
    status.errors = errors;
  }

  const responseStatus = status.status === 'healthy' ? 200 : 503;
  return NextResponse.json(status, { status: responseStatus });
}
```

### System Monitoring Script
```bash
#!/bin/bash
# monitoring.sh

LOG_FILE="/var/log/rubicon-testing/monitoring.log"
ALERT_EMAIL="<EMAIL>"

# Function to log with timestamp
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> $LOG_FILE
}

# Check application health
check_app_health() {
    local response=$(curl -s -w "%{http_code}" http://localhost:3000/api/health -o /tmp/health_check.json)
    
    if [ "$response" != "200" ]; then
        log "ALERT: Application health check failed with status $response"
        mail -s "Rubicon Testing App Health Alert" $ALERT_EMAIL < /tmp/health_check.json
        return 1
    fi
    
    return 0
}

# Check database connectivity
check_database() {
    local result=$(sudo -u postgres psql -c "SELECT 1;" 2>/dev/null | grep -c "1 row")
    
    if [ "$result" != "1" ]; then
        log "ALERT: Database connectivity check failed"
        echo "Database connectivity failed at $(date)" | mail -s "Database Alert" $ALERT_EMAIL
        return 1
    fi
    
    return 0
}

# Check disk space
check_disk_space() {
    local usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    
    if [ "$usage" -gt 85 ]; then
        log "ALERT: Disk usage is at ${usage}%"
        echo "Disk usage is critically high at ${usage}%" | mail -s "Disk Space Alert" $ALERT_EMAIL
        return 1
    fi
    
    return 0
}

# Check memory usage
check_memory() {
    local usage=$(free | grep Mem | awk '{printf "%.0f", ($3/$2) * 100}')
    
    if [ "$usage" -gt 90 ]; then
        log "ALERT: Memory usage is at ${usage}%"
        echo "Memory usage is critically high at ${usage}%" | mail -s "Memory Usage Alert" $ALERT_EMAIL
        return 1
    fi
    
    return 0
}

# Check SSL certificate expiration
check_ssl_cert() {
    local days_left=$(echo | openssl s_client -connect localhost:443 -servername rubiconprograms.org 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2 | xargs -I {} date -d {} +%s)
    local current_time=$(date +%s)
    local days_until_expiry=$(( (days_left - current_time) / 86400 ))
    
    if [ "$days_until_expiry" -lt 30 ]; then
        log "ALERT: SSL certificate expires in $days_until_expiry days"
        echo "SSL certificate expires in $days_until_expiry days" | mail -s "SSL Certificate Expiration Alert" $ALERT_EMAIL
        return 1
    fi
    
    return 0
}

# Run all checks
log "Starting monitoring checks"
check_app_health && log "Application health: OK"
check_database && log "Database connectivity: OK"
check_disk_space && log "Disk space: OK"
check_memory && log "Memory usage: OK"
check_ssl_cert && log "SSL certificate: OK"
log "Monitoring checks completed"
```

## Backup Strategy

### Database Backup Script
```bash
#!/bin/bash
# backup-database.sh

BACKUP_DIR="/var/backups/rubicon-testing"
DATABASE_URL="${DATABASE_URL}"
RETENTION_DAYS=30
S3_BUCKET="rubicon-backups"

# Create backup directory
mkdir -p $BACKUP_DIR

# Generate backup filename with timestamp
BACKUP_FILE="rubicon_db_backup_$(date +%Y%m%d_%H%M%S).sql.gz"
BACKUP_PATH="$BACKUP_DIR/$BACKUP_FILE"

# Create database backup
echo "Starting database backup..."
pg_dump $DATABASE_URL | gzip > $BACKUP_PATH

if [ $? -eq 0 ]; then
    echo "Database backup completed: $BACKUP_PATH"
    
    # Upload to S3 (optional)
    if command -v aws &> /dev/null; then
        aws s3 cp $BACKUP_PATH s3://$S3_BUCKET/database/
        echo "Backup uploaded to S3"
    fi
    
    # Clean up old backups
    find $BACKUP_DIR -name "rubicon_db_backup_*.sql.gz" -mtime +$RETENTION_DAYS -delete
    echo "Old backups cleaned up"
    
else
    echo "Database backup failed!"
    exit 1
fi
```

### Application Files Backup
```bash
#!/bin/bash
# backup-files.sh

APP_DIR="/var/www/rubicon-testing"
BACKUP_DIR="/var/backups/rubicon-testing/files"
RETENTION_DAYS=7

mkdir -p $BACKUP_DIR

# Backup configuration and uploads
BACKUP_FILE="rubicon_files_backup_$(date +%Y%m%d_%H%M%S).tar.gz"
BACKUP_PATH="$BACKUP_DIR/$BACKUP_FILE"

tar -czf $BACKUP_PATH \
    --exclude='node_modules' \
    --exclude='.next' \
    --exclude='logs' \
    -C $APP_DIR \
    .env.production \
    public/uploads \
    config/

echo "Files backup completed: $BACKUP_PATH"

# Clean up old backups
find $BACKUP_DIR -name "rubicon_files_backup_*.tar.gz" -mtime +$RETENTION_DAYS -delete
```

## Continuous Deployment

### GitHub Actions CI/CD Pipeline
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test
    
    - name: Run type check
      run: npm run type-check
    
    - name: Run linting
      run: npm run lint

  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build application
      run: npm run build
    
    - name: Build Docker image
      run: docker build -t rubicon-testing:${{ github.sha }} .
    
    - name: Push to registry
      run: |
        echo ${{ secrets.DOCKER_PASSWORD }} | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
        docker push rubicon-testing:${{ github.sha }}

  deploy:
    needs: build
    runs-on: ubuntu-latest
    environment: production
    
    steps:
    - name: Deploy to production
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.PRODUCTION_HOST }}
        username: ${{ secrets.PRODUCTION_USER }}
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        script: |
          cd /var/www/rubicon-testing
          docker pull rubicon-testing:${{ github.sha }}
          docker-compose down
          docker-compose up -d
          docker system prune -f
```

## Security Considerations

### Production Security Checklist
- [ ] SSL/TLS certificates properly configured
- [ ] Firewall rules configured (ports 22, 80, 443 only)
- [ ] Database connections encrypted
- [ ] Environment variables secured
- [ ] Regular security updates applied
- [ ] Backup encryption enabled
- [ ] Access logs monitored
- [ ] Rate limiting configured
- [ ] CORS properly configured
- [ ] Security headers implemented

### Security Hardening Script
```bash
#!/bin/bash
# security-hardening.sh

echo "Starting security hardening..."

# Update system packages
apt update && apt upgrade -y

# Configure automatic security updates
echo 'Unattended-Upgrade::Automatic-Reboot "false";' >> /etc/apt/apt.conf.d/50unattended-upgrades
dpkg-reconfigure -plow unattended-upgrades

# Configure fail2ban
apt install -y fail2ban
cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local

# Disable root login
sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
systemctl restart ssh

# Configure PostgreSQL security
sudo -u postgres psql -c "ALTER USER postgres PASSWORD 'secure_random_password';"
echo "host all all 127.0.0.1/32 md5" >> /etc/postgresql/*/main/pg_hba.conf
systemctl restart postgresql

echo "Security hardening completed"
```

---

*This comprehensive deployment documentation provides complete guidance for deploying, configuring, and maintaining the Rubicon Programs Testing Application in production environments.*