# Test System Documentation

## Overview

The Rubicon Programs Testing Application features a sophisticated, multi-modal testing engine designed to assess four critical professional skill areas. The system supports adaptive testing, real-time progress tracking, comprehensive scoring algorithms, and detailed analytics.

## Test Categories

### 1. Typing Tests

#### Keyboard Typing Assessment
**Purpose**: Evaluate typing speed and accuracy on standard QWERTY keyboards

**Test Mechanics:**
- **Duration**: 5 minutes (configurable)
- **Content**: Professional business passages
- **Metrics**: Words per minute (WPM), accuracy percentage, weighted WPM
- **Real-time Tracking**: Keystroke logging, error analysis, speed progression

**Scoring Algorithm:**
```typescript
interface TypingMetrics {
  grossWPM: number;        // Total characters ÷ 5 ÷ minutes
  netWPM: number;          // Gross WPM - (errors ÷ minutes)
  accuracy: number;        // (correct characters ÷ total characters) × 100
  weightedWPM: number;     // Net WPM × (accuracy ÷ 100)
  totalKeystrokes: number; // All key presses including corrections
  errorRate: number;       // Errors per minute
}

export function calculateTypingScore(session: TypingSession): TypingResult {
  const timeInMinutes = session.timeElapsed / 60;
  const totalChars = session.typedText.length;
  
  const grossWPM = (totalChars / 5) / timeInMinutes;
  const netWPM = grossWPM - (session.incorrectCharacters / timeInMinutes);
  const accuracy = (session.correctCharacters / totalChars) * 100;
  const weightedWPM = netWPM * (accuracy / 100);
  
  return {
    grossWPM: Math.round(grossWPM),
    netWPM: Math.round(netWPM), 
    accuracy: Math.round(accuracy * 10) / 10,
    weightedWPM: Math.round(weightedWPM),
    score: Math.min(100, Math.max(0, weightedWPM)), // 0-100 scale
    gradeLevelEquivalent: mapWPMToGradeLevel(weightedWPM)
  };
}
```

#### 10-Key Numeric Testing
**Purpose**: Assess numeric keypad proficiency for data entry positions

**Test Mechanics:**
- **Duration**: 5 minutes of continuous numeric entry
- **Content**: Random number sequences (3-10 digits each)
- **Metrics**: Keystrokes per hour (KPH), accuracy percentage
- **Professional Standards**: 8,000+ KPH for entry-level positions

**Implementation:**
```typescript
export class TenKeyTestEngine {
  generateNumberSequences(difficulty: 'easy' | 'medium' | 'hard'): string[] {
    const sequenceLengths = {
      easy: [3, 4, 5],
      medium: [4, 5, 6, 7],
      hard: [5, 6, 7, 8, 9, 10]
    };
    
    const sequences: string[] = [];
    const lengths = sequenceLengths[difficulty];
    
    for (let i = 0; i < 50; i++) {
      const length = lengths[Math.floor(Math.random() * lengths.length)];
      let sequence = '';
      
      for (let j = 0; j < length; j++) {
        sequence += Math.floor(Math.random() * 10);
      }
      
      sequences.push(sequence);
    }
    
    return sequences;
  }
  
  calculateKPH(correctKeystrokes: number, timeInMinutes: number): number {
    return Math.round((correctKeystrokes / timeInMinutes) * 60);
  }
}
```

### 2. Digital Literacy Assessment

#### Computer Knowledge Testing
**Purpose**: Evaluate fundamental computer and technology skills

**Question Categories:**
- **Hardware Knowledge**: Computer components, peripherals, basic troubleshooting
- **Software Operations**: Operating systems, common applications, file management
- **Internet Skills**: Web browsing, email, online safety, search techniques
- **Digital Communication**: Email etiquette, social media awareness, video conferencing

**Question Types:**
```typescript
interface DigitalLiteracyQuestion {
  id: string;
  category: 'hardware' | 'software' | 'internet' | 'communication';
  questionType: 'multiple_choice' | 'true_false' | 'simulation';
  content: string;
  options?: string[];
  correctAnswer: any;
  explanation: string;
  difficultyLevel: 1 | 2 | 3;
  points: number;
}

// Example questions
const sampleQuestions: DigitalLiteracyQuestion[] = [
  {
    id: 'dl-001',
    category: 'hardware',
    questionType: 'multiple_choice',
    content: 'Which component is considered the "brain" of the computer?',
    options: ['Hard Drive', 'RAM', 'CPU', 'Motherboard'],
    correctAnswer: 'CPU',
    explanation: 'The CPU (Central Processing Unit) processes all instructions and calculations.',
    difficultyLevel: 1,
    points: 2
  },
  {
    id: 'dl-002', 
    category: 'internet',
    questionType: 'simulation',
    content: 'Compose a professional email requesting a meeting.',
    correctAnswer: {
      hasSubject: true,
      hasGreeting: true,
      hasPurpose: true,
      hasClosing: true,
      isProfessionalTone: true
    },
    explanation: 'Professional emails should include subject, greeting, clear purpose, and closing.',
    difficultyLevel: 2,
    points: 5
  }
];
```

### 3. Basic Mathematics Assessment

#### Adaptive Grade-Level Testing
**Purpose**: Determine mathematical competency from 5th to 12th grade level

**Adaptive Algorithm:**
```typescript
export class AdaptiveMathEngine {
  private currentGradeLevel: number = 5;
  private correctAnswersByGrade: Record<number, number> = {};
  private incorrectAnswersByGrade: Record<number, number> = {};
  
  async getNextQuestion(testId: string): Promise<MathQuestion | null> {
    // Get question at current grade level
    const question = await this.getQuestionByGradeLevel(this.currentGradeLevel);
    
    if (!question) {
      return this.completeTest(testId);
    }
    
    return question;
  }
  
  processAnswer(answer: string, correctAnswer: string, gradeLevel: number): void {
    if (answer === correctAnswer) {
      this.correctAnswersByGrade[gradeLevel] = 
        (this.correctAnswersByGrade[gradeLevel] || 0) + 1;
      this.maybeAdvanceGrade(gradeLevel);
    } else {
      this.incorrectAnswersByGrade[gradeLevel] = 
        (this.incorrectAnswersByGrade[gradeLevel] || 0) + 1;
      this.maybeReduceGrade(gradeLevel);
    }
  }
  
  private maybeAdvanceGrade(gradeLevel: number): void {
    const correct = this.correctAnswersByGrade[gradeLevel] || 0;
    const incorrect = this.incorrectAnswersByGrade[gradeLevel] || 0;
    const total = correct + incorrect;
    
    // Advance if 80% accuracy with at least 3 questions
    if (total >= 3 && (correct / total) >= 0.8 && gradeLevel < 12) {
      this.currentGradeLevel = gradeLevel + 1;
    }
  }
  
  private maybeReduceGrade(gradeLevel: number): void {
    const correct = this.correctAnswersByGrade[gradeLevel] || 0;
    const incorrect = this.incorrectAnswersByGrade[gradeLevel] || 0;
    const total = correct + incorrect;
    
    // Reduce if below 40% accuracy with at least 3 questions
    if (total >= 3 && (correct / total) < 0.4 && gradeLevel > 5) {
      this.currentGradeLevel = gradeLevel - 1;
    }
  }
  
  calculateFinalGradeLevel(): number {
    let highestSuccessfulGrade = 5;
    
    for (let grade = 5; grade <= 12; grade++) {
      const correct = this.correctAnswersByGrade[grade] || 0;
      const incorrect = this.incorrectAnswersByGrade[grade] || 0;
      const total = correct + incorrect;
      
      if (total >= 3 && (correct / total) >= 0.7) {
        highestSuccessfulGrade = grade;
      }
    }
    
    return highestSuccessfulGrade;
  }
}
```

#### Mathematical Content Areas
- **Grade 5-6**: Basic arithmetic, fractions, decimals, simple geometry
- **Grade 7-8**: Algebra basics, percentages, ratios, data interpretation
- **Grade 9-10**: Linear equations, polynomials, advanced geometry
- **Grade 11-12**: Advanced algebra, trigonometry, statistics

### 4. Basic English Assessment

#### Reading Comprehension & Language Skills
**Purpose**: Evaluate language proficiency and reading comprehension abilities

**Assessment Components:**
- **Grammar & Syntax**: Proper sentence structure and usage rules
- **Vocabulary**: Word knowledge and context usage
- **Reading Comprehension**: Understanding and analyzing text passages
- **Writing Mechanics**: Spelling, punctuation, capitalization

**Implementation Example:**
```typescript
interface EnglishAssessmentQuestion {
  id: string;
  type: 'grammar' | 'vocabulary' | 'comprehension' | 'mechanics';
  passage?: string;        // For comprehension questions
  question: string;
  options?: string[];      // For multiple choice
  correctAnswer: any;
  explanation: string;
  gradeLevel: number;      // 5-12 grade level
  skillArea: string;       // Specific skill being tested
}

export class EnglishAssessmentEngine {
  async generateComprehensionQuestions(passage: string, gradeLevel: number): Promise<EnglishAssessmentQuestion[]> {
    const questions: EnglishAssessmentQuestion[] = [];
    
    // Generate different types of comprehension questions
    questions.push(
      this.createMainIdeaQuestion(passage, gradeLevel),
      this.createDetailQuestion(passage, gradeLevel),
      this.createInferenceQuestion(passage, gradeLevel),
      this.createVocabularyInContextQuestion(passage, gradeLevel)
    );
    
    return questions;
  }
  
  private createMainIdeaQuestion(passage: string, gradeLevel: number): EnglishAssessmentQuestion {
    return {
      id: `comp-main-${Date.now()}`,
      type: 'comprehension',
      passage,
      question: 'What is the main idea of this passage?',
      options: this.generateMainIdeaOptions(passage),
      correctAnswer: this.identifyMainIdea(passage),
      explanation: 'The main idea is the central message or primary point of the passage.',
      gradeLevel,
      skillArea: 'main_idea_identification'
    };
  }
}
```

## Test Engine Architecture

### Test Session Management

```typescript
export class TestSessionManager {
  private sessions: Map<string, TestSession> = new Map();
  
  async createTestSession(
    userId: string, 
    testTypeId: string, 
    config: TestConfiguration
  ): Promise<TestSession> {
    const testId = await this.generateTestId();
    
    const session: TestSession = {
      id: testId,
      userId,
      testTypeId,
      status: 'STARTED',
      isPractice: config.isPractice || false,
      startedAt: new Date(),
      timeLimit: config.timeLimit,
      currentQuestionIndex: 0,
      answers: [],
      score: null,
      configuration: config
    };
    
    // Save to database
    await prisma.tests.create({
      data: {
        id: testId,
        userId,
        testTypeId,
        status: 'STARTED',
        isPractice: session.isPractice,
        startedAt: session.startedAt,
        timeLimit: session.timeLimit,
        questionsOrder: JSON.stringify(config.questionIds || []),
        answers: JSON.stringify([])
      }
    });
    
    this.sessions.set(testId, session);
    return session;
  }
  
  async pauseTest(testId: string): Promise<boolean> {
    const session = this.sessions.get(testId);
    if (!session || session.status !== 'STARTED') {
      return false;
    }
    
    session.status = 'PAUSED';
    session.pausedAt = new Date();
    
    await prisma.tests.update({
      where: { id: testId },
      data: {
        status: 'PAUSED',
        pausedAt: session.pausedAt,
        answers: JSON.stringify(session.answers)
      }
    });
    
    return true;
  }
  
  async resumeTest(testId: string): Promise<boolean> {
    const session = this.sessions.get(testId);
    if (!session || session.status !== 'PAUSED') {
      return false;
    }
    
    session.status = 'STARTED';
    session.pausedAt = null;
    
    await prisma.tests.update({
      where: { id: testId },
      data: {
        status: 'STARTED',
        pausedAt: null
      }
    });
    
    return true;
  }
  
  async submitAnswer(
    testId: string, 
    questionId: string, 
    answer: any
  ): Promise<AnswerSubmissionResult> {
    const session = this.sessions.get(testId);
    if (!session || session.status !== 'STARTED') {
      return { success: false, error: 'Invalid test session' };
    }
    
    // Get question details
    const question = await prisma.questions.findUnique({
      where: { id: questionId }
    });
    
    if (!question) {
      return { success: false, error: 'Question not found' };
    }
    
    // Process answer based on question type
    const answerRecord = await this.processAnswer(question, answer);
    session.answers.push(answerRecord);
    
    // Save progress
    await prisma.tests.update({
      where: { id: testId },
      data: {
        answers: JSON.stringify(session.answers),
        currentQuestionId: questionId
      }
    });
    
    return {
      success: true,
      isCorrect: answerRecord.isCorrect,
      correctAnswer: question.correctAnswer,
      explanation: question.explanation
    };
  }
}
```

### Question Bank Management

```typescript
export class QuestionBankService {
  async getQuestions(
    testTypeId: string, 
    options: QuestionSelectionOptions
  ): Promise<TestQuestion[]> {
    const {
      count = 20,
      difficultyLevel,
      gradeLevel,
      excludeUsed = true,
      randomize = true
    } = options;
    
    const where: any = {
      testTypeId,
      isActive: true
    };
    
    if (difficultyLevel) {
      where.difficultyLevel = difficultyLevel;
    }
    
    if (gradeLevel) {
      where.gradeLevel = gradeLevel;
    }
    
    let questions = await prisma.questions.findMany({
      where,
      take: randomize ? count * 3 : count, // Get more for randomization
      orderBy: randomize ? undefined : { createdAt: 'desc' }
    });
    
    if (randomize) {
      questions = this.shuffleArray(questions).slice(0, count);
    }
    
    return questions.map(q => ({
      id: q.id,
      content: q.content,
      questionType: q.questionType,
      options: q.options,
      correctAnswer: q.correctAnswer,
      explanation: q.explanation,
      difficultyLevel: q.difficultyLevel,
      gradeLevel: q.gradeLevel,
      metadata: q.metadata
    }));
  }
  
  async createQuestion(questionData: CreateQuestionData): Promise<string> {
    const question = await prisma.questions.create({
      data: {
        ...questionData,
        timesAsked: 0,
        timesCorrect: 0,
        isActive: true,
        createdBy: 'admin' // or current user ID
      }
    });
    
    return question.id;
  }
  
  async updateQuestionStatistics(
    questionId: string, 
    wasCorrect: boolean
  ): Promise<void> {
    await prisma.questions.update({
      where: { id: questionId },
      data: {
        timesAsked: { increment: 1 },
        timesCorrect: wasCorrect ? { increment: 1 } : undefined
      }
    });
  }
}
```

## Scoring & Analytics

### Comprehensive Scoring System

```typescript
export class TestScoringService {
  async calculateTestScore(testId: string): Promise<TestResult> {
    const test = await prisma.tests.findUnique({
      where: { id: testId },
      include: {
        test_types: true,
        typing_session: true
      }
    });
    
    if (!test) {
      throw new Error('Test not found');
    }
    
    let result: TestResult;
    
    switch (test.test_types.name) {
      case 'typing-keyboard':
      case 'typing-10key':
        result = await this.scoreTypingTest(test);
        break;
      case 'digital-literacy':
        result = await this.scoreDigitalLiteracyTest(test);
        break;
      case 'basic-math':
        result = await this.scoreMathTest(test);
        break;
      case 'basic-english':
        result = await this.scoreEnglishTest(test);
        break;
      default:
        throw new Error('Unknown test type');
    }
    
    // Save results
    await prisma.test_results.create({
      data: {
        testId,
        userId: test.userId!,
        testTypeId: test.testTypeId,
        score: result.score,
        gradeLevelScore: result.gradeLevelScore,
        timeToComplete: result.timeToComplete,
        accuracy: result.accuracy,
        weightedSpeed: result.weightedSpeed,
        rawSpeed: result.rawSpeed,
        languageScore: result.languageScore,
        comprehensionScore: result.comprehensionScore,
        difficultyLevel: result.difficultyLevel,
        questionsCorrect: result.questionsCorrect,
        questionsTotal: result.questionsTotal,
        detailedResults: result.detailedResults,
        completedAt: new Date()
      }
    });
    
    return result;
  }
  
  private async scoreTypingTest(test: any): Promise<TestResult> {
    const session = test.typing_session;
    if (!session) {
      throw new Error('Typing session not found');
    }
    
    const timeInMinutes = session.timeElapsed / 60;
    const grossWPM = (session.totalCharacters / 5) / timeInMinutes;
    const netWPM = grossWPM - (session.incorrectCharacters / timeInMinutes);
    const accuracy = (session.correctCharacters / session.totalCharacters) * 100;
    const weightedWPM = netWPM * (accuracy / 100);
    
    return {
      score: Math.min(100, Math.max(0, weightedWPM)),
      accuracy: Math.round(accuracy * 10) / 10,
      rawSpeed: Math.round(grossWPM),
      weightedSpeed: Math.round(weightedWPM),
      timeToComplete: session.timeElapsed,
      gradeLevelScore: this.mapWPMToGradeLevel(weightedWPM),
      detailedResults: {
        grossWPM,
        netWPM,
        totalKeystrokes: session.keystrokeLog.length,
        errorRate: session.incorrectCharacters / timeInMinutes,
        keystrokeAnalysis: this.analyzeKeystrokes(session.keystrokeLog)
      }
    };
  }
  
  private async scoreDigitalLiteracyTest(test: any): Promise<TestResult> {
    const answers = JSON.parse(test.answers || '[]');
    const questions = await this.getTestQuestions(test.id);
    
    let correctCount = 0;
    let totalPoints = 0;
    let earnedPoints = 0;
    
    for (const answer of answers) {
      const question = questions.find(q => q.id === answer.questionId);
      if (question) {
        totalPoints += question.points || 1;
        if (this.isAnswerCorrect(answer.answer, question.correctAnswer, question.questionType)) {
          correctCount++;
          earnedPoints += question.points || 1;
        }
      }
    }
    
    const accuracy = (correctCount / answers.length) * 100;
    const score = (earnedPoints / totalPoints) * 100;
    
    return {
      score: Math.round(score),
      accuracy: Math.round(accuracy * 10) / 10,
      questionsCorrect: correctCount,
      questionsTotal: answers.length,
      timeToComplete: this.calculateTestDuration(test),
      gradeLevelScore: this.mapScoreToGradeLevel(score, 'digital-literacy'),
      detailedResults: {
        categoryBreakdown: this.calculateCategoryScores(answers, questions),
        difficultyAnalysis: this.analyzeDifficultyPerformance(answers, questions)
      }
    };
  }
}
```

## Test Configuration

### Configurable Test Parameters

```typescript
interface TestConfiguration {
  testType: string;
  isPractice: boolean;
  timeLimit?: number;        // Minutes
  questionCount?: number;    // Number of questions
  difficultyLevel?: number;  // 1-10 scale
  adaptiveTesting: boolean;  // Enable adaptive difficulty
  allowPause: boolean;       // Can user pause test
  showCorrectAnswers: boolean; // Show answers after completion
  randomizeQuestions: boolean; // Randomize question order
  passingScore?: number;     // Minimum score to pass
  certificateEligible: boolean; // Can generate certificate
  retakePolicy: 'unlimited' | 'limited' | 'none';
  maxRetakes?: number;       // If limited retakes
  cooldownPeriod?: number;   // Hours between retakes
}

export class TestConfigurationService {
  private defaultConfigs: Record<string, TestConfiguration> = {
    'typing-keyboard': {
      testType: 'typing-keyboard',
      isPractice: false,
      timeLimit: 5,
      adaptiveTesting: false,
      allowPause: false,
      showCorrectAnswers: false,
      randomizeQuestions: false,
      passingScore: 25, // 25 WPM minimum
      certificateEligible: true,
      retakePolicy: 'unlimited'
    },
    
    'digital-literacy': {
      testType: 'digital-literacy',
      isPractice: false,
      timeLimit: 45,
      questionCount: 30,
      adaptiveTesting: true,
      allowPause: true,
      showCorrectAnswers: true,
      randomizeQuestions: true,
      passingScore: 70,
      certificateEligible: true,
      retakePolicy: 'limited',
      maxRetakes: 3,
      cooldownPeriod: 24
    },
    
    'basic-math': {
      testType: 'basic-math',
      isPractice: false,
      timeLimit: 60,
      adaptiveTesting: true,
      allowPause: true,
      showCorrectAnswers: true,
      randomizeQuestions: false,
      passingScore: 5.0, // 5th grade minimum
      certificateEligible: true,
      retakePolicy: 'limited',
      maxRetakes: 2,
      cooldownPeriod: 48
    }
  };
  
  async getTestConfiguration(testType: string, isPractice: boolean = false): Promise<TestConfiguration> {
    const config = { ...this.defaultConfigs[testType] };
    
    if (isPractice) {
      // Modify config for practice mode
      config.isPractice = true;
      config.timeLimit = config.timeLimit ? Math.floor(config.timeLimit / 2) : undefined;
      config.questionCount = config.questionCount ? Math.floor(config.questionCount / 2) : 10;
      config.certificateEligible = false;
      config.retakePolicy = 'unlimited';
      config.showCorrectAnswers = true;
    }
    
    return config;
  }
}
```

## Real-time Test Monitoring

### Progress Tracking System

```typescript
export class TestMonitoringService {
  private progressTrackers: Map<string, TestProgressTracker> = new Map();
  
  createProgressTracker(testId: string): TestProgressTracker {
    const tracker = new TestProgressTracker(testId);
    this.progressTrackers.set(testId, tracker);
    return tracker;
  }
  
  async trackTestProgress(testId: string, event: TestEvent): Promise<void> {
    const tracker = this.progressTrackers.get(testId);
    if (!tracker) return;
    
    tracker.recordEvent(event);
    
    // Check for anomalies
    if (this.detectAnomalies(tracker)) {
      await this.flagTestForReview(testId, 'Unusual activity detected');
    }
    
    // Auto-save progress
    if (event.type === 'answer_submitted' || event.type === 'question_viewed') {
      await this.saveTestProgress(testId, tracker.getState());
    }
  }
  
  private detectAnomalies(tracker: TestProgressTracker): boolean {
    const state = tracker.getState();
    
    // Check for unusually fast answers (less than 5 seconds)
    const recentAnswers = state.events
      .filter(e => e.type === 'answer_submitted')
      .slice(-5);
    
    const fastAnswers = recentAnswers.filter(answer => {
      const timeSinceQuestionShown = answer.timestamp - 
        (state.events.find(e => e.type === 'question_viewed' && 
         e.data?.questionId === answer.data?.questionId)?.timestamp || 0);
      return timeSinceQuestionShown < 5000; // Less than 5 seconds
    });
    
    return fastAnswers.length > 2; // More than 2 fast answers
  }
}

class TestProgressTracker {
  private testId: string;
  private events: TestEvent[] = [];
  private startTime: number;
  
  constructor(testId: string) {
    this.testId = testId;
    this.startTime = Date.now();
  }
  
  recordEvent(event: TestEvent): void {
    this.events.push({
      ...event,
      timestamp: Date.now(),
      testId: this.testId
    });
  }
  
  getState(): TestProgressState {
    return {
      testId: this.testId,
      startTime: this.startTime,
      currentTime: Date.now(),
      events: this.events,
      totalQuestions: this.calculateTotalQuestions(),
      answeredQuestions: this.calculateAnsweredQuestions(),
      timeSpent: Date.now() - this.startTime,
      averageTimePerQuestion: this.calculateAverageTime()
    };
  }
  
  private calculateTotalQuestions(): number {
    const questionEvents = this.events.filter(e => e.type === 'question_viewed');
    return new Set(questionEvents.map(e => e.data?.questionId)).size;
  }
  
  private calculateAnsweredQuestions(): number {
    const answerEvents = this.events.filter(e => e.type === 'answer_submitted');
    return new Set(answerEvents.map(e => e.data?.questionId)).size;
  }
  
  private calculateAverageTime(): number {
    const answerEvents = this.events.filter(e => e.type === 'answer_submitted');
    if (answerEvents.length === 0) return 0;
    
    let totalTime = 0;
    for (const answer of answerEvents) {
      const questionShown = this.events.find(e => 
        e.type === 'question_viewed' && 
        e.data?.questionId === answer.data?.questionId
      );
      
      if (questionShown) {
        totalTime += answer.timestamp - questionShown.timestamp;
      }
    }
    
    return totalTime / answerEvents.length;
  }
}
```

## Practice Mode Features

### Practice Test Implementation

```typescript
export class PracticeTestService {
  async createPracticeTest(
    userId: string, 
    testTypeId: string
  ): Promise<PracticeTestResult> {
    // Check if practice tests are enabled
    const settings = await this.getApplicationSettings();
    if (!settings.practiceTestEnabled) {
      return {
        success: false,
        error: 'Practice tests are currently disabled'
      };
    }
    
    // Get practice configuration
    const practiceConfig = await prisma.practice_test_config.findUnique({
      where: { testTypeId }
    });
    
    if (!practiceConfig) {
      return {
        success: false,
        error: 'Practice test not available for this test type'
      };
    }
    
    // Check question bank availability
    const availableQuestions = await prisma.questions.count({
      where: {
        testTypeId,
        isActive: true
      }
    });
    
    if (availableQuestions < practiceConfig.minBankQuestions) {
      return {
        success: false,
        error: 'Insufficient questions available for practice test'
      };
    }
    
    // Create practice test session
    const testConfig: TestConfiguration = {
      testType: testTypeId,
      isPractice: true,
      questionCount: practiceConfig.questionCount,
      timeLimit: practiceConfig.timeLimit,
      adaptiveTesting: false,
      allowPause: true,
      showCorrectAnswers: true,
      randomizeQuestions: true,
      certificateEligible: false,
      retakePolicy: 'unlimited'
    };
    
    const session = await this.testSessionManager.createTestSession(
      userId,
      testTypeId,
      testConfig
    );
    
    return {
      success: true,
      testId: session.id,
      configuration: testConfig,
      questions: await this.getQuestionsForPractice(testTypeId, practiceConfig.questionCount)
    };
  }
  
  async getPracticeTestResults(testId: string): Promise<PracticeTestResults> {
    const test = await prisma.tests.findUnique({
      where: { id: testId },
      include: {
        test_types: true,
        test_results: true
      }
    });
    
    if (!test || !test.isPractice) {
      throw new Error('Practice test not found');
    }
    
    const answers = JSON.parse(test.answers || '[]');
    const questions = await this.getTestQuestions(testId);
    
    // Calculate detailed feedback
    const questionAnalysis = answers.map(answer => {
      const question = questions.find(q => q.id === answer.questionId);
      return {
        questionId: answer.questionId,
        userAnswer: answer.answer,
        correctAnswer: question?.correctAnswer,
        isCorrect: answer.isCorrect,
        explanation: question?.explanation,
        category: question?.category,
        difficultyLevel: question?.difficultyLevel,
        timeSpent: answer.timeSpent || 0
      };
    });
    
    const categoryBreakdown = this.calculateCategoryBreakdown(questionAnalysis);
    const recommendedStudyAreas = this.identifyWeakAreas(categoryBreakdown);
    
    return {
      testId,
      score: test.test_results?.score || 0,
      accuracy: test.test_results?.accuracy || 0,
      timeToComplete: test.test_results?.timeToComplete || 0,
      questionAnalysis,
      categoryBreakdown,
      recommendedStudyAreas,
      canRetakeOfficial: this.canUserTakeOfficialTest(test.userId, test.testTypeId)
    };
  }
}
```

---

*This comprehensive test system documentation covers all aspects of the testing engine, from question management to scoring algorithms and real-time monitoring capabilities.*