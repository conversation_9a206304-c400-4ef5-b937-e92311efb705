# User Management System

## Overview

The Rubicon Programs Testing Application implements a comprehensive user management system designed to handle multiple user types, granular permissions, and sophisticated access control mechanisms. The system supports both self-registration and administrative user creation workflows.

## User Types & Roles

### User Type Hierarchy

```typescript
enum UserType {
  USER = "USER",           // Standard test-taking users
  ADMIN = "ADMIN"          // Administrative users
}

interface UserRole {
  type: UserType;
  isPrimaryAdmin?: boolean; // Special admin designation
  permissions: Permission[];
  restrictions?: Restriction[];
}
```

### Standard Users (`USER`)

**Primary Purpose**: Individuals taking professional skills assessments

**Core Capabilities:**
- Create and manage personal profiles
- Take practice and official tests (with appropriate access)
- View personal test results and history
- Request access to additional test types
- Download certificates and reports
- Update personal information and preferences

**Access Limitations:**
- Cannot view other users' data
- Cannot access administrative functions
- Cannot modify system settings
- Limited to assigned test types

### Administrative Users (`ADMIN`)

**Primary Purpose**: System administrators managing users and tests

**Core Capabilities:**
- All standard user capabilities, plus:
- Create, edit, and delete user accounts
- Grant and revoke test access permissions
- View and manage all user test results
- Create and manage one-time access codes
- Review and approve test access requests
- Configure system settings and preferences
- View comprehensive analytics and reports
- Cancel or delete test sessions
- Reset user passwords and account status

**Administrative Levels:**
- **Standard Admin**: Full administrative access except certain system-critical functions
- **Primary Admin**: Complete system access including user impersonation and critical settings

## User Profile Management

### User Data Model

```typescript
interface UserProfile {
  // Identity Information
  id: string;                    // Unique identifier
  email: string;                 // Primary login credential
  emailVerified: DateTime;       // Email verification status
  
  // Personal Information
  firstName: string;             // Required first name
  lastName: string;              // Required last name
  middleInitial?: string;        // Optional middle initial
  namePrefix?: string;           // Title (Mr., Mrs., Dr., etc.)
  nameSuffix?: string;          // Suffix (Jr., Sr., III, etc.)
  
  // Contact & Demographics
  dateOfBirth: DateTime;         // Required for age verification
  zipCode: string;               // Geographic location
  phoneNumber?: string;          // Optional contact number
  englishFirst?: boolean;        // English as first language
  educationLevel?: string;       // Educational background
  
  // Account Settings
  profileImage?: string;         // Avatar/photo URL
  userType: UserType;            // Permission level
  isPrimaryAdmin: boolean;       // Super admin flag
  twoFactorEnabled: boolean;     // 2FA status
  
  // Account Status
  isDeactivated: boolean;        // Account active/inactive
  requirePasswordChange: boolean; // Force password update
  
  // Metadata
  createdAt: DateTime;           // Account creation
  updatedAt: DateTime;           // Last modification
  primaryAdminDate?: DateTime;   // Primary admin assignment date
}
```

### Profile Validation Rules

```typescript
export const ProfileValidation = {
  email: {
    required: true,
    format: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    maxLength: 255,
    unique: true
  },
  
  firstName: {
    required: true,
    minLength: 1,
    maxLength: 50,
    pattern: /^[a-zA-Z\s\-'\.]+$/
  },
  
  lastName: {
    required: true,
    minLength: 1,
    maxLength: 50,
    pattern: /^[a-zA-Z\s\-'\.]+$/
  },
  
  dateOfBirth: {
    required: true,
    minAge: 13,                  // COPPA compliance
    maxAge: 120,                 // Reasonable age limit
    format: 'YYYY-MM-DD'
  },
  
  zipCode: {
    required: true,
    pattern: /^\d{5}(-\d{4})?$/ // US ZIP code format
  },
  
  phoneNumber: {
    required: false,
    pattern: /^\+?[\d\s\-\(\)\.]+$/,
    minLength: 10,
    maxLength: 20
  }
};
```

## User Registration Process

### Self-Registration Workflow

```mermaid
flowchart TD
    A[User Visits Signup] --> B[Fill Registration Form]
    B --> C{Form Validation}
    C -->|Invalid| B
    C -->|Valid| D[Create User Account]
    D --> E[Send Verification Email]
    E --> F[User Receives Email]
    F --> G[Click Verification Link]
    G --> H{Token Valid?}
    H -->|Invalid/Expired| I[Request New Token]
    H -->|Valid| J[Activate Account]
    J --> K[Redirect to Profile Completion]
    K --> L[Complete Profile Setup]
    L --> M[Access Dashboard]
    I --> E
```

### Registration Implementation

```typescript
export class UserRegistrationService {
  static async registerUser(data: RegistrationData): Promise<RegistrationResult> {
    try {
      // Input validation
      const validatedData = await this.validateRegistrationData(data);
      
      // Check for existing user
      const existingUser = await prisma.users.findUnique({
        where: { email: validatedData.email.toLowerCase() }
      });
      
      if (existingUser) {
        throw new Error('Email address already registered');
      }
      
      // Hash password
      const hashedPassword = await PasswordService.hashPassword(validatedData.password);
      
      // Create user record
      const user = await prisma.users.create({
        data: {
          email: validatedData.email.toLowerCase(),
          password: hashedPassword,
          firstName: validatedData.firstName,
          lastName: validatedData.lastName,
          dateOfBirth: new Date('2000-01-01'), // Placeholder - requires profile completion
          zipCode: validatedData.zipCode,
          userType: 'USER',
          isPrimaryAdmin: false,
          twoFactorEnabled: false,
          isDeactivated: false,
          requirePasswordChange: false,
          emailVerified: null
        }
      });
      
      // Generate email verification token
      const verificationToken = await EmailVerificationService.createVerificationToken(
        user.id, 
        user.email
      );
      
      // Send verification email
      await EmailService.sendVerificationEmail(user.email, verificationToken);
      
      return {
        success: true,
        userId: user.id,
        message: 'Registration successful. Please check your email for verification instructions.'
      };
      
    } catch (error) {
      console.error('Registration error:', error);
      return {
        success: false,
        error: error.message || 'Registration failed'
      };
    }
  }
  
  static async completeProfile(userId: string, profileData: ProfileData): Promise<boolean> {
    try {
      await prisma.users.update({
        where: { id: userId },
        data: {
          dateOfBirth: profileData.dateOfBirth,
          middleInitial: profileData.middleInitial,
          namePrefix: profileData.namePrefix,
          nameSuffix: profileData.nameSuffix,
          phoneNumber: profileData.phoneNumber,
          englishFirst: profileData.englishFirst,
          educationLevel: profileData.educationLevel,
          updatedAt: new Date()
        }
      });
      
      return true;
    } catch (error) {
      console.error('Profile completion error:', error);
      return false;
    }
  }
}
```

## Administrative User Management

### Admin User Creation

```typescript
export class AdminUserService {
  static async createAdminUser(
    adminData: AdminUserData, 
    creatorId: string
  ): Promise<CreateUserResult> {
    try {
      // Verify creator has admin privileges
      const creator = await this.verifyAdminPermissions(creatorId);
      
      // Generate secure password
      const temporaryPassword = this.generateSecurePassword();
      const hashedPassword = await PasswordService.hashPassword(temporaryPassword);
      
      // Create admin user
      const adminUser = await prisma.users.create({
        data: {
          ...adminData,
          password: hashedPassword,
          userType: 'ADMIN',
          isPrimaryAdmin: false,
          requirePasswordChange: true, // Force password change on first login
          emailVerified: new Date(), // Admin-created accounts are pre-verified
          isDeactivated: false
        }
      });
      
      // Log admin action
      await this.logAdminAction(creatorId, 'USER_CREATED', adminUser.id, {
        userType: adminUser.userType,
        email: adminUser.email
      });
      
      // Send welcome email with temporary credentials
      await EmailService.sendWelcomeEmail(
        adminUser.email,
        adminUser.firstName,
        temporaryPassword
      );
      
      return {
        success: true,
        userId: adminUser.id,
        temporaryPassword
      };
      
    } catch (error) {
      console.error('Admin user creation error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  private static generateSecurePassword(): string {
    const charset = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789!@#$%&*';
    let password = '';
    
    // Ensure password meets complexity requirements
    password += 'ABCDEFGHJKLMNPQRSTUVWXYZ'[Math.floor(Math.random() * 23)]; // Uppercase
    password += 'abcdefghjkmnpqrstuvwxyz'[Math.floor(Math.random() * 23)]; // Lowercase
    password += '23456789'[Math.floor(Math.random() * 8)]; // Number
    password += '!@#$%&*'[Math.floor(Math.random() * 7)]; // Special char
    
    // Fill remaining characters
    for (let i = 4; i < 12; i++) {
      password += charset[Math.floor(Math.random() * charset.length)];
    }
    
    // Shuffle password characters
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }
}
```

### User Search & Management

```typescript
export class UserManagementService {
  static async searchUsers(criteria: UserSearchCriteria): Promise<UserSearchResults> {
    const {
      searchTerm,
      userType,
      isActive,
      hasTestAccess,
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = criteria;
    
    const where: any = {};
    
    // Search term filtering
    if (searchTerm) {
      where.OR = [
        { firstName: { contains: searchTerm, mode: 'insensitive' } },
        { lastName: { contains: searchTerm, mode: 'insensitive' } },
        { email: { contains: searchTerm, mode: 'insensitive' } }
      ];
    }
    
    // Filter by user type
    if (userType) {
      where.userType = userType;
    }
    
    // Filter by active status
    if (typeof isActive === 'boolean') {
      where.isDeactivated = !isActive;
    }
    
    // Filter by test access
    if (hasTestAccess) {
      where.user_test_access = {
        some: { isActive: true }
      };
    }
    
    const [users, totalCount] = await Promise.all([
      prisma.users.findMany({
        where,
        include: {
          user_test_access: {
            include: { test_types: true },
            where: { isActive: true }
          },
          test_results: {
            select: { id: true, completedAt: true, score: true }
          }
        },
        orderBy: { [sortBy]: sortOrder },
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.users.count({ where })
    ]);
    
    return {
      users: users.map(user => ({
        ...user,
        testCount: user.test_results.length,
        lastTestDate: user.test_results.length > 0 
          ? user.test_results[0].completedAt 
          : null,
        accessTypes: user.user_test_access.map(access => ({
          testType: access.test_types.displayName,
          accessType: access.accessType
        }))
      })),
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalCount / limit),
        totalCount,
        limit
      }
    };
  }
  
  static async updateUserStatus(
    userId: string, 
    updates: UserStatusUpdate, 
    adminId: string
  ): Promise<boolean> {
    try {
      await prisma.users.update({
        where: { id: userId },
        data: {
          isDeactivated: updates.isDeactivated,
          requirePasswordChange: updates.requirePasswordChange,
          updatedAt: new Date()
        }
      });
      
      // Log admin action
      await this.logAdminAction(adminId, 'USER_UPDATED', userId, updates);
      
      return true;
    } catch (error) {
      console.error('User status update error:', error);
      return false;
    }
  }
}
```

## Test Access Control

### Access Types System

```typescript
enum AccessType {
  NONE = "NONE",                    // No access to test
  PRACTICE_ONLY = "PRACTICE_ONLY",  // Practice tests only
  ONE_TIME = "ONE_TIME",            // Single official test attempt
  UNLIMITED = "UNLIMITED"           // Unlimited official test attempts
}

interface UserTestAccess {
  id: string;
  userId: string;
  testTypeId: string;
  accessType: AccessType;
  grantedBy?: string;              // Admin who granted access
  grantedAt?: DateTime;
  expiresAt?: DateTime;            // Optional expiration date
  isActive: boolean;
  createdAt: DateTime;
  updatedAt: DateTime;
}
```

### Access Management Implementation

```typescript
export class TestAccessService {
  static async grantAccess(
    userId: string,
    testTypeId: string,
    accessType: AccessType,
    adminId: string,
    expiresAt?: Date
  ): Promise<boolean> {
    try {
      // Check if access record exists
      const existingAccess = await prisma.user_test_access.findUnique({
        where: {
          userId_testTypeId: { userId, testTypeId }
        }
      });
      
      if (existingAccess) {
        // Update existing access
        await prisma.user_test_access.update({
          where: { id: existingAccess.id },
          data: {
            accessType,
            grantedBy: adminId,
            grantedAt: new Date(),
            expiresAt,
            isActive: true,
            updatedAt: new Date()
          }
        });
      } else {
        // Create new access record
        await prisma.user_test_access.create({
          data: {
            userId,
            testTypeId,
            accessType,
            grantedBy: adminId,
            grantedAt: new Date(),
            expiresAt,
            isActive: true
          }
        });
      }
      
      // Log admin action
      await AdminActionService.logAction(adminId, 'ACCESS_GRANTED', userId, {
        testTypeId,
        accessType,
        expiresAt
      });
      
      // Notify user of access grant
      await NotificationService.sendAccessGrantedNotification(userId, testTypeId, accessType);
      
      return true;
      
    } catch (error) {
      console.error('Access grant error:', error);
      return false;
    }
  }
  
  static async revokeAccess(
    userId: string,
    testTypeId: string,
    adminId: string,
    reason?: string
  ): Promise<boolean> {
    try {
      await prisma.user_test_access.updateMany({
        where: { userId, testTypeId },
        data: {
          isActive: false,
          updatedAt: new Date()
        }
      });
      
      // Log admin action
      await AdminActionService.logAction(adminId, 'ACCESS_REVOKED', userId, {
        testTypeId,
        reason
      });
      
      return true;
    } catch (error) {
      console.error('Access revoke error:', error);
      return false;
    }
  }
  
  static async checkUserAccess(
    userId: string,
    testTypeId: string
  ): Promise<AccessCheckResult> {
    const access = await prisma.user_test_access.findUnique({
      where: {
        userId_testTypeId: { userId, testTypeId }
      },
      include: { test_types: true }
    });
    
    if (!access || !access.isActive) {
      return {
        hasAccess: false,
        accessType: 'NONE',
        message: 'No access to this test type'
      };
    }
    
    // Check expiration
    if (access.expiresAt && access.expiresAt < new Date()) {
      return {
        hasAccess: false,
        accessType: access.accessType,
        message: 'Access has expired'
      };
    }
    
    // Check one-time access usage
    if (access.accessType === 'ONE_TIME') {
      const completedTests = await prisma.tests.count({
        where: {
          userId,
          testTypeId,
          status: 'COMPLETED',
          isPractice: false
        }
      });
      
      if (completedTests > 0) {
        return {
          hasAccess: false,
          accessType: access.accessType,
          message: 'One-time access has been used'
        };
      }
    }
    
    return {
      hasAccess: true,
      accessType: access.accessType,
      expiresAt: access.expiresAt,
      grantedAt: access.grantedAt
    };
  }
}
```

## One-Time Access Codes

### Code Generation & Management

```typescript
export class OneTimeCodeService {
  static async createCode(
    testTypeId: string,
    adminId: string,
    expiresAt?: Date,
    quantity: number = 1
  ): Promise<OneTimeCodeResult[]> {
    const results: OneTimeCodeResult[] = [];
    
    try {
      for (let i = 0; i < quantity; i++) {
        const code = this.generateUniqueCode();
        
        const codeRecord = await prisma.one_time_codes.create({
          data: {
            code,
            testTypeId,
            createdBy: adminId,
            expiresAt,
            isActive: true
          },
          include: { test_types: true }
        });
        
        results.push({
          code: codeRecord.code,
          testType: codeRecord.test_types.displayName,
          expiresAt: codeRecord.expiresAt
        });
      }
      
      // Log admin action
      await AdminActionService.logAction(adminId, 'CODES_CREATED', null, {
        testTypeId,
        quantity,
        expiresAt
      });
      
      return results;
      
    } catch (error) {
      console.error('Code creation error:', error);
      throw new Error('Failed to create one-time codes');
    }
  }
  
  static async redeemCode(code: string, userId: string): Promise<RedemptionResult> {
    try {
      const codeRecord = await prisma.one_time_codes.findUnique({
        where: { code },
        include: { test_types: true }
      });
      
      if (!codeRecord) {
        return {
          success: false,
          error: 'Invalid code'
        };
      }
      
      if (!codeRecord.isActive) {
        return {
          success: false,
          error: 'Code is no longer active'
        };
      }
      
      if (codeRecord.usedBy) {
        return {
          success: false,
          error: 'Code has already been used'
        };
      }
      
      if (codeRecord.expiresAt && codeRecord.expiresAt < new Date()) {
        return {
          success: false,
          error: 'Code has expired'
        };
      }
      
      // Check rate limiting
      const canRedeem = await this.checkRedemptionRateLimit(userId);
      if (!canRedeem) {
        return {
          success: false,
          error: 'Too many codes redeemed recently. Please wait before trying again.'
        };
      }
      
      // Redeem the code
      await prisma.one_time_codes.update({
        where: { id: codeRecord.id },
        data: {
          usedBy: userId,
          usedAt: new Date()
        }
      });
      
      // Grant one-time access
      await TestAccessService.grantAccess(
        userId,
        codeRecord.testTypeId,
        'ONE_TIME',
        'system' // System-generated access
      );
      
      return {
        success: true,
        testType: codeRecord.test_types.displayName,
        accessGranted: 'ONE_TIME'
      };
      
    } catch (error) {
      console.error('Code redemption error:', error);
      return {
        success: false,
        error: 'Code redemption failed'
      };
    }
  }
  
  private static generateUniqueCode(): string {
    // Generate 8-character alphanumeric code (excluding confusing characters)
    const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
    let code = '';
    
    for (let i = 0; i < 8; i++) {
      code += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    
    return code;
  }
  
  private static async checkRedemptionRateLimit(userId: string): Promise<boolean> {
    const recentRedemptions = await prisma.one_time_codes.count({
      where: {
        usedBy: userId,
        usedAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        }
      }
    });
    
    return recentRedemptions < 3; // Limit: 3 codes per day
  }
}
```

## Test Access Requests

### Request Workflow System

```typescript
export class TestRequestService {
  static async submitRequest(
    userId: string,
    testTypeId: string,
    reason?: string
  ): Promise<RequestSubmissionResult> {
    try {
      // Check for existing request
      const existingRequest = await prisma.test_requests.findUnique({
        where: {
          userId_testTypeId: { userId, testTypeId }
        }
      });
      
      if (existingRequest && existingRequest.status === 'PENDING') {
        return {
          success: false,
          error: 'A request for this test type is already pending'
        };
      }
      
      // Create or update request
      const request = await prisma.test_requests.upsert({
        where: {
          userId_testTypeId: { userId, testTypeId }
        },
        update: {
          status: 'PENDING',
          reason,
          reviewedBy: null,
          reviewedAt: null,
          response: null,
          updatedAt: new Date()
        },
        create: {
          userId,
          testTypeId,
          reason,
          status: 'PENDING'
        },
        include: {
          users: true,
          test_types: true
        }
      });
      
      // Notify administrators
      await NotificationService.notifyAdminsOfNewRequest(request);
      
      return {
        success: true,
        requestId: request.id,
        message: 'Your request has been submitted and is awaiting review'
      };
      
    } catch (error) {
      console.error('Request submission error:', error);
      return {
        success: false,
        error: 'Failed to submit request'
      };
    }
  }
  
  static async reviewRequest(
    requestId: string,
    adminId: string,
    decision: 'APPROVED' | 'DENIED',
    response?: string,
    accessType?: AccessType,
    expiresAt?: Date
  ): Promise<boolean> {
    try {
      const request = await prisma.test_requests.update({
        where: { id: requestId },
        data: {
          status: decision,
          reviewedBy: adminId,
          reviewedAt: new Date(),
          response,
          updatedAt: new Date()
        },
        include: { users: true, test_types: true }
      });
      
      if (decision === 'APPROVED' && accessType) {
        // Grant access
        await TestAccessService.grantAccess(
          request.userId,
          request.testTypeId,
          accessType,
          adminId,
          expiresAt
        );
      }
      
      // Notify user of decision
      await NotificationService.sendRequestDecisionNotification(
        request.users.email,
        request.test_types.displayName,
        decision,
        response
      );
      
      // Log admin action
      await AdminActionService.logAction(adminId, 'REQUEST_REVIEWED', request.userId, {
        requestId,
        decision,
        testTypeId: request.testTypeId,
        accessType
      });
      
      return true;
      
    } catch (error) {
      console.error('Request review error:', error);
      return false;
    }
  }
}
```

## User Impersonation

### Admin Impersonation System

```typescript
export class UserImpersonationService {
  static async startImpersonation(
    adminId: string,
    targetUserId: string
  ): Promise<ImpersonationResult> {
    try {
      // Verify admin has impersonation privileges
      const admin = await prisma.users.findUnique({
        where: { id: adminId }
      });
      
      if (!admin || admin.userType !== 'ADMIN' || !admin.isPrimaryAdmin) {
        return {
          success: false,
          error: 'Insufficient privileges for user impersonation'
        };
      }
      
      // Get target user
      const targetUser = await prisma.users.findUnique({
        where: { id: targetUserId }
      });
      
      if (!targetUser) {
        return {
          success: false,
          error: 'Target user not found'
        };
      }
      
      // Prevent impersonating other admins
      if (targetUser.userType === 'ADMIN') {
        return {
          success: false,
          error: 'Cannot impersonate administrative users'
        };
      }
      
      // Log impersonation start
      await AdminActionService.logAction(adminId, 'IMPERSONATION_STARTED', targetUserId);
      
      return {
        success: true,
        impersonationData: {
          originalAdmin: {
            id: admin.id,
            email: admin.email,
            firstName: admin.firstName,
            lastName: admin.lastName,
            userType: admin.userType
          },
          impersonatedUser: {
            id: targetUser.id,
            email: targetUser.email,
            firstName: targetUser.firstName,
            lastName: targetUser.lastName,
            userType: targetUser.userType
          }
        }
      };
      
    } catch (error) {
      console.error('Impersonation start error:', error);
      return {
        success: false,
        error: 'Failed to start impersonation'
      };
    }
  }
  
  static async endImpersonation(
    adminId: string,
    targetUserId: string
  ): Promise<boolean> {
    try {
      // Log impersonation end
      await AdminActionService.logAction(adminId, 'IMPERSONATION_ENDED', targetUserId);
      
      return true;
    } catch (error) {
      console.error('Impersonation end error:', error);
      return false;
    }
  }
}
```

## User Analytics & Reporting

### User Activity Tracking

```typescript
export class UserAnalyticsService {
  static async getUserActivitySummary(userId: string): Promise<UserActivitySummary> {
    const [user, testHistory, accessHistory] = await Promise.all([
      prisma.users.findUnique({
        where: { id: userId },
        include: {
          user_test_access: {
            include: { test_types: true }
          }
        }
      }),
      
      prisma.tests.findMany({
        where: { userId },
        include: {
          test_types: true,
          test_results: true
        },
        orderBy: { startedAt: 'desc' }
      }),
      
      prisma.user_test_access.findMany({
        where: { userId },
        include: { test_types: true },
        orderBy: { createdAt: 'desc' }
      })
    ]);
    
    const completedTests = testHistory.filter(test => test.status === 'COMPLETED');
    const practiceTests = completedTests.filter(test => test.isPractice);
    const officialTests = completedTests.filter(test => !test.isPractice);
    
    return {
      user: {
        id: user!.id,
        name: `${user!.firstName} ${user!.lastName}`,
        email: user!.email,
        userType: user!.userType,
        createdAt: user!.createdAt,
        lastActivity: testHistory[0]?.startedAt || user!.updatedAt
      },
      
      testStatistics: {
        totalTests: testHistory.length,
        completedTests: completedTests.length,
        practiceTests: practiceTests.length,
        officialTests: officialTests.length,
        averageScore: this.calculateAverageScore(completedTests),
        testsByType: this.groupTestsByType(completedTests)
      },
      
      accessSummary: {
        totalAccessTypes: accessHistory.length,
        activeAccess: accessHistory.filter(access => access.isActive).length,
        accessByType: accessHistory.map(access => ({
          testType: access.test_types.displayName,
          accessType: access.accessType,
          grantedAt: access.grantedAt,
          expiresAt: access.expiresAt
        }))
      },
      
      recentActivity: testHistory.slice(0, 10).map(test => ({
        testType: test.test_types.displayName,
        status: test.status,
        score: test.test_results?.score,
        startedAt: test.startedAt,
        completedAt: test.completedAt
      }))
    };
  }
  
  static async generateUserReport(
    userId: string,
    format: 'summary' | 'detailed'
  ): Promise<UserReport> {
    const activity = await this.getUserActivitySummary(userId);
    
    if (format === 'summary') {
      return this.generateSummaryReport(activity);
    } else {
      return this.generateDetailedReport(activity);
    }
  }
}
```

---

*This comprehensive user management documentation covers all aspects of user lifecycle, permissions, access control, and administrative functions within the Rubicon Programs Testing Application.*