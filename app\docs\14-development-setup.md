# Development Setup Guide

## Prerequisites

Before setting up the Rubicon Programs Testing Application for development, ensure you have the following installed:

### Required Software
- **Node.js** (v18.0.0 or higher)
- **npm** (v8.0.0 or higher) or **Yarn** (v1.22.0 or higher)
- **PostgreSQL** (v13.0 or higher)
- **Git** (v2.30.0 or higher)

### Recommended Tools
- **VS Code** with extensions:
  - TypeScript and JavaScript Language Features
  - Prisma
  - Tailwind CSS IntelliSense
  - ES7+ React/Redux/React-Native snippets
  - GitLens
  - Prettier - Code formatter
  - ESLint

## Initial Setup

### 1. Clone the Repository

```bash
git clone https://github.com/your-org/rubicon-testing-app.git
cd rubicon-testing-app
```

### 2. Install Dependencies

Using npm:
```bash
npm install
```

Using Yarn:
```bash
yarn install
```

### 3. Environment Configuration

Create environment files by copying the templates:

```bash
cp .env.example .env.local
cp .env.example .env.development
```

#### Environment Variables

Edit your `.env.local` file with the following configuration:

```env
# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/rubicon_testing_db?schema=public"

# NextAuth Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-key-here"

# Email Configuration (Development)
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT="587"
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
EMAIL_FROM="<EMAIL>"

# AI Services (Optional)
AI_SERVICE_PROVIDER="openai"
AI_SERVICE_API_KEY="your-api-key-here"
AI_SERVICE_MODEL="gpt-4"
AI_SERVICE_ENABLED="true"

# Security
ENCRYPTION_KEY="your-32-character-encryption-key-here"
TEST_SECURITY_SECRET="your-test-security-secret-here"

# File Storage (Optional)
UPLOAD_DIR="./public/uploads"
MAX_FILE_SIZE="5242880" # 5MB in bytes

# Development Settings
NODE_ENV="development"
DEBUG="true"
LOG_LEVEL="debug"
```

### 4. Database Setup

#### Create PostgreSQL Database

```bash
# Connect to PostgreSQL
psql -U postgres

# Create database and user
CREATE DATABASE rubicon_testing_db;
CREATE USER rubicon_user WITH ENCRYPTED PASSWORD 'your_password_here';
GRANT ALL PRIVILEGES ON DATABASE rubicon_testing_db TO rubicon_user;

# Exit psql
\q
```

#### Generate Prisma Client and Run Migrations

```bash
# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma migrate dev --name init

# Seed the database with initial data
npx prisma db seed
```

### 5. Start Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:3000`

## Project Structure

```
rubicon-testing-app/
├── app/                        # Next.js App Router pages and layouts
│   ├── (auth)/                # Authentication-related pages
│   ├── (dashboard)/           # User dashboard and test interfaces
│   ├── (admin)/               # Administrative interfaces
│   ├── api/                   # API routes
│   ├── globals.css            # Global styles
│   ├── layout.tsx             # Root layout component
│   └── page.tsx               # Home page
├── components/                 # Reusable React components
│   ├── ui/                    # Base UI components
│   ├── layout/                # Layout components
│   └── providers.tsx          # Context providers
├── lib/                       # Utility functions and configurations
│   ├── auth.ts               # Authentication utilities
│   ├── db.ts                 # Database connection
│   ├── types.ts              # TypeScript type definitions
│   └── utils.ts              # General utilities
├── hooks/                     # Custom React hooks
├── prisma/                    # Database schema and migrations
│   ├── schema.prisma         # Database schema definition
│   ├── migrations/           # Database migration files
│   └── seed.ts               # Database seeding script
├── public/                    # Static assets
├── docs/                      # Documentation files
├── components.json           # shadcn/ui configuration
├── next.config.js            # Next.js configuration
├── tailwind.config.js        # Tailwind CSS configuration
├── tsconfig.json             # TypeScript configuration
├── package.json              # Dependencies and scripts
└── README.md                 # Project overview
```

## Development Workflow

### 1. Feature Development Process

```bash
# Create feature branch
git checkout -b feature/new-test-type

# Make your changes
# ... development work ...

# Run tests and linting
npm run test
npm run lint
npm run type-check

# Commit changes
git add .
git commit -m "Add new test type functionality"

# Push branch and create pull request
git push origin feature/new-test-type
```

### 2. Database Changes

When modifying the database schema:

```bash
# 1. Edit prisma/schema.prisma
# 2. Create and run migration
npx prisma migrate dev --name describe_your_changes

# 3. Regenerate Prisma client
npx prisma generate

# 4. Update seed data if necessary
npx prisma db seed
```

### 3. Code Quality Standards

#### TypeScript Configuration
The project uses strict TypeScript settings:

```json
{
  "compilerOptions": {
    "target": "es2017",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

#### ESLint Configuration
```json
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn",
    "prefer-const": "error",
    "no-var": "error"
  }
}
```

#### Prettier Configuration
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

## Testing Strategy

### 1. Unit Testing
The project uses Jest and React Testing Library:

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

Example unit test:
```typescript
// __tests__/lib/auth.test.ts
import { validatePasswordStrength } from '@/lib/password';

describe('Password Validation', () => {
  test('should validate strong password', () => {
    const result = validatePasswordStrength('StrongPass123!');
    expect(result.score).toBeGreaterThan(80);
    expect(result.hasMinLength).toBe(true);
    expect(result.hasUppercase).toBe(true);
    expect(result.hasLowercase).toBe(true);
    expect(result.hasNumber).toBe(true);
    expect(result.hasSpecialChar).toBe(true);
  });
});
```

### 2. Integration Testing
For testing API routes and database interactions:

```typescript
// __tests__/api/auth/signin.test.ts
import { createMocks } from 'node-mocks-http';
import handler from '@/app/api/auth/signin/route';

describe('/api/auth/signin', () => {
  test('should authenticate valid user', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      body: {
        email: '<EMAIL>',
        password: 'validpassword'
      }
    });

    await handler(req, res);
    expect(res._getStatusCode()).toBe(200);
  });
});
```

### 3. E2E Testing (Optional)
For end-to-end testing using Playwright:

```bash
# Install Playwright
npm install -D @playwright/test

# Run E2E tests
npx playwright test
```

## Development Scripts

### Package.json Scripts

```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "lint:fix": "next lint --fix",
    "type-check": "tsc --noEmit",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "db:migrate": "prisma migrate dev",
    "db:generate": "prisma generate",
    "db:seed": "tsx --require dotenv/config prisma/seed.ts",
    "db:reset": "prisma migrate reset",
    "db:studio": "prisma studio",
    "format": "prettier --write .",
    "format:check": "prettier --check ."
  }
}
```

### Development Commands

```bash
# Database management
npm run db:migrate          # Run database migrations
npm run db:generate         # Generate Prisma client
npm run db:seed            # Seed database with test data
npm run db:reset           # Reset database (destructive)
npm run db:studio          # Open Prisma Studio GUI

# Code quality
npm run lint               # Check for linting errors
npm run lint:fix          # Fix auto-fixable linting errors
npm run type-check        # Check TypeScript types
npm run format            # Format code with Prettier
npm run format:check      # Check code formatting

# Testing
npm run test              # Run unit tests
npm run test:watch        # Run tests in watch mode
npm run test:coverage     # Generate test coverage report
```

## Environment Setup

### Development Environment Variables

Create a `.env.development` file for development-specific settings:

```env
# Development Database
DATABASE_URL="postgresql://dev_user:dev_pass@localhost:5432/rubicon_dev?schema=public"

# Development Email (use testing service like MailHog)
EMAIL_SERVER_HOST="localhost"
EMAIL_SERVER_PORT="1025"
EMAIL_FROM="dev@localhost"

# Debug Settings
DEBUG="true"
LOG_LEVEL="debug"
VERBOSE_LOGGING="true"

# Development Features
ENABLE_TEST_ROUTES="true"
MOCK_AI_SERVICE="true"
DISABLE_RATE_LIMITING="true"
```

### Production Environment Variables

For production deployment, ensure these variables are set:

```env
# Production Database (managed service recommended)
DATABASE_URL="***********************************************/rubicon_prod?schema=public"

# Production Auth
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="secure-random-secret-key"

# Production Email
EMAIL_SERVER_HOST="your-smtp-provider.com"
EMAIL_SERVER_PORT="587"
EMAIL_SERVER_USER="your-production-email"
EMAIL_SERVER_PASSWORD="secure-email-password"

# Security
ENCRYPTION_KEY="production-encryption-key-32-chars"
TEST_SECURITY_SECRET="production-test-security-secret"

# Performance
NODE_ENV="production"
LOG_LEVEL="info"
CACHE_ENABLED="true"
```

## Debugging

### VS Code Debug Configuration

Create `.vscode/launch.json`:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Next.js: debug server-side",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/.bin/next",
      "args": ["dev"],
      "console": "integratedTerminal",
      "skipFiles": ["<node_internals>/**"]
    },
    {
      "name": "Next.js: debug client-side",
      "type": "chrome",
      "request": "launch",
      "url": "http://localhost:3000"
    }
  ]
}
```

### Database Debugging

```bash
# View database in Prisma Studio
npm run db:studio

# Connect to database directly
psql $DATABASE_URL

# View recent logs
tail -f logs/application.log
```

### Common Development Issues

#### Issue: Database Connection Failed
```bash
# Check PostgreSQL is running
sudo service postgresql status

# Check database exists
psql -U postgres -l

# Test connection
psql $DATABASE_URL -c "SELECT 1;"
```

#### Issue: Prisma Client Out of Sync
```bash
# Regenerate Prisma client
npx prisma generate

# If schema changed, run migration
npx prisma migrate dev
```

#### Issue: NextAuth Session Problems
```bash
# Clear browser cookies and localStorage
# Verify NEXTAUTH_SECRET is set
echo $NEXTAUTH_SECRET

# Check JWT expiration settings in auth config
```

## Performance Monitoring

### Development Performance Metrics

Enable Next.js build analysis:

```bash
# Install bundle analyzer
npm install --save-dev @next/bundle-analyzer

# Analyze bundle
npm run analyze
```

Add to `next.config.js`:
```javascript
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

module.exports = withBundleAnalyzer({
  // Your Next.js config
});
```

### Database Performance Monitoring

```bash
# Monitor slow queries
psql $DATABASE_URL -c "
  SELECT query, mean_time, calls 
  FROM pg_stat_statements 
  ORDER BY mean_time DESC 
  LIMIT 10;
"

# Check database size
psql $DATABASE_URL -c "
  SELECT pg_size_pretty(pg_database_size('rubicon_testing_db'));
"
```

## Deployment Preparation

### Pre-deployment Checklist

- [ ] All tests passing (`npm run test`)
- [ ] No linting errors (`npm run lint`)
- [ ] TypeScript checks pass (`npm run type-check`)
- [ ] Build succeeds (`npm run build`)
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] Performance optimizations implemented
- [ ] Security headers configured
- [ ] Error monitoring enabled

### Build Optimization

```bash
# Production build
npm run build

# Start production server
npm run start

# Analyze bundle size
ANALYZE=true npm run build
```

---

*This development setup guide provides everything needed to get the Rubicon Programs Testing Application running in a development environment, with comprehensive tooling and best practices.*