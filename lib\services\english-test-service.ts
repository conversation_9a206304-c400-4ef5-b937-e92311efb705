
import { PrismaClient, english_questions, reading_passages } from '@prisma/client';

const prisma = new PrismaClient();

type QuestionResponse = {
  questionId: string;
  isCorrect: boolean;
  gradeLevel: number;
  questionType: string;
};

type QuestionCriteria = {
  gradeLevel: number;
  questionType: string;
  count: number;
};

class EnglishTestService {
  /**
   * Generates a set of adaptive questions for an English test.
   * @param gradeLevel The starting grade level for the test.
   * @param previousResponses A list of previous responses from the user.
   * @returns A list of questions for the next section of the test.
   */
  async generateAdaptiveQuestions(
    gradeLevel: number = 8,
    previousResponses: QuestionResponse[] = []
  ): Promise<english_questions[]> {
    const currentGradeLevel = this.determineCurrentGradeLevel(gradeLevel, previousResponses);
    const questions = await this.selectQuestionsFromBank({
      gradeLevel: currentGradeLevel,
      questionType: 'mixed', // or some other logic to determine question types
      count: 5, // or some other logic to determine the number of questions
    });
    return questions;
  }

  /**
   * Selects questions from the question bank based on specified criteria.
   * @param criteria The criteria for selecting questions.
   * @returns A list of questions from the bank.
   */
  async selectQuestionsFromBank(criteria: QuestionCriteria): Promise<english_questions[]> {
    const where: any = {
      grade_level: criteria.gradeLevel,
      is_active: true
    };

    if (criteria.questionType !== 'mixed') {
      where.question_type = criteria.questionType;
    }

    const questions = await prisma.english_questions.findMany({
      where,
      take: criteria.count,
      // Add orderBy to get random questions
      orderBy: {
        times_used: 'asc',
      },
    });

    return questions;
  }

  /**
   * Calculates the language mastery subscore based on user responses.
   * @param responses A list of user responses.
   * @returns The language mastery score.
   */
  calculateLanguageMasteryScore(responses: QuestionResponse[]): number {
    const languageMasteryResponses = responses.filter(r => r.questionType !== 'READING_COMPREHENSION');
    const correctResponses = languageMasteryResponses.filter(r => r.isCorrect).length;
    return (correctResponses / languageMasteryResponses.length) * 100;
  }

  /**
   * Calculates the reading comprehension subscore based on user responses.
   * @param responses A list of user responses.
   * @returns The reading comprehension score.
   */
  calculateReadingComprehensionScore(responses: QuestionResponse[]): number {
    const readingComprehensionResponses = responses.filter(r => r.questionType === 'READING_COMPREHENSION');
    const correctResponses = readingComprehensionResponses.filter(r => r.isCorrect).length;
    return (correctResponses / readingComprehensionResponses.length) * 100;
  }

  /**
   * Determines the user's grade level based on their scores.
   * @param scores A dictionary of scores (e.g., { languageMastery: 80, readingComprehension: 75 }).
   * @returns The user's determined grade level (5-12).
   */
  determineGradeLevel(scores: { [key: string]: number }): number {
    const averageScore = Object.values(scores).reduce((a, b) => a + b, 0) / Object.values(scores).length;
    if (averageScore >= 90) return 12;
    if (averageScore >= 80) return 11;
    if (averageScore >= 70) return 10;
    if (averageScore >= 60) return 9;
    if (averageScore >= 50) return 8;
    if (averageScore >= 40) return 7;
    if (averageScore >= 30) return 6;
    return 5;
  }

  /**
   * Returns statistics about the current question bank.
   * @returns An object containing statistics about the question bank.
   */
  async getQuestionBankStats(): Promise<{ [key: string]: any }> {
    const totalQuestions = await prisma.english_questions.count({ where: { is_active: true } });

    const questionsByGradeLevel = await prisma.english_questions.groupBy({
      by: ['grade_level'],
      _count: {
        id: true,
      },
      where: { is_active: true },
    });

    const questionsByType = await prisma.english_questions.groupBy({
      by: ['question_type'],
      _count: {
        id: true,
      },
      where: { is_active: true },
    });

    return {
      totalQuestions,
      questionsByGradeLevel: questionsByGradeLevel.reduce((acc, curr) => {
        acc[curr.grade_level] = curr._count.id;
        return acc;
      }, {} as { [key: number]: number }),
      questionsByType: questionsByType.reduce((acc, curr) => {
        acc[curr.question_type] = curr._count.id;
        return acc;
      }, {} as { [key: string]: number }),
    };
  }

  /**
   * Determines the current grade level for the user based on their previous responses.
   * @param initialGradeLevel The initial grade level of the test.
   * @param previousResponses A list of previous responses from the user.
   * @returns The next grade level to present to the user.
   */
  private determineCurrentGradeLevel(initialGradeLevel: number, previousResponses: QuestionResponse[]): number {
    if (previousResponses.length === 0) {
      return initialGradeLevel;
    }

    const lastGradeLevel = previousResponses[previousResponses.length - 1].gradeLevel;
    const lastSetOfResponses = previousResponses.filter(r => r.gradeLevel === lastGradeLevel);

    if (lastSetOfResponses.length < 3) {
      return lastGradeLevel;
    }

    const correctCount = lastSetOfResponses.filter(r => r.isCorrect).length;
    const accuracy = correctCount / lastSetOfResponses.length;

    if (accuracy >= 0.8) { // Move up
      return Math.min(lastGradeLevel + 2, 12);
    } else if (accuracy < 0.6) { // Move down
      return Math.max(lastGradeLevel - 1, 5);
    } else { // Stay at the same level
      return lastGradeLevel;
    }
  }
}

export default new EnglishTestService();

