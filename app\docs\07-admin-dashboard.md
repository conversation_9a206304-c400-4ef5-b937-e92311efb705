# Admin Dashboard Documentation

## Overview

The Admin Dashboard provides comprehensive administrative capabilities for managing users, tests, access permissions, and system settings. Built with a focus on usability and efficiency, it enables administrators to effectively oversee the entire testing platform.

## Dashboard Access & Navigation

### Admin Authentication Requirements
- **User Type**: Must be `ADMIN` 
- **Route Protection**: Middleware enforces admin-only access
- **Session Validation**: Real-time permission checking

### Dashboard URL Structure
```
/admin                     - Main dashboard overview
/admin/users              - User management interface
/admin/tests              - Test management and configuration
/admin/one-time-codes     - Access code generation and management
/admin/settings           - System settings and configuration
/admin/math               - Math test question management
```

### Navigation Implementation
```typescript
// Admin route protection
export default async function AdminDashboardPage() {
  let user;
  try {
    user = await requireAdmin();
  } catch {
    redirect('/auth/signin');
  }
  
  // Admin dashboard content
}
```

## Dashboard Overview Page

### Key Metrics Display

The main dashboard provides at-a-glance system statistics:

```typescript
interface DashboardMetrics {
  pendingRequests: number;        // Test access requests awaiting review
  totalUsers: number;             // Total registered users (excluding admins)
  totalTests: number;             // All test sessions conducted
  recentTests: TestWithDetails[]; // Latest 10 test sessions
  oneTimeCodes: ActiveCodes[];    // Active one-time access codes
}
```

### Quick Stats Cards
- **Pending Requests**: Urgent items requiring admin attention
- **Total Users**: Current user base size
- **Total Tests**: Platform usage volume  
- **Recent Activity**: Latest test completions with status indicators

### Status Color Coding System
```typescript
const getStatusColor = (status: string) => {
  switch (status) {
    case 'COMPLETED':
      return 'bg-green-100 text-green-800';
    case 'STARTED':
      return 'bg-blue-100 text-blue-800';
    case 'PAUSED':
      return 'bg-yellow-100 text-yellow-800';
    case 'CANCELLED':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};
```

## User Management Interface

### User Search & Filtering
```typescript
interface UserSearchCriteria {
  searchTerm?: string;           // Name or email search
  userType?: 'USER' | 'ADMIN';  // Filter by user type
  isActive?: boolean;            // Active/deactivated status
  hasTestAccess?: boolean;       // Users with any test access
  dateRange?: DateRange;         // Registration date range
  testActivity?: ActivityFilter;  // Recent test activity
}
```

### User List Display
Each user record shows:
- **Basic Info**: Name, email, registration date
- **Status Indicators**: Active/deactivated, email verified
- **Test Access Summary**: Current permissions per test type
- **Activity Metrics**: Total tests taken, last test date
- **Quick Actions**: Edit, deactivate, reset password, grant access

### Bulk Operations
- **Bulk Access Grants**: Grant test access to multiple users
- **Bulk Communications**: Send notifications to user groups
- **Bulk Status Changes**: Activate/deactivate multiple accounts
- **Export Operations**: Download user data in various formats

### User Detail Management
```typescript
interface UserDetailActions {
  // Profile Management
  editProfile: boolean;
  resetPassword: boolean;
  toggleActivation: boolean;
  requirePasswordChange: boolean;
  
  // Access Control
  grantTestAccess: boolean;
  revokeTestAccess: boolean;
  setAccessExpiration: boolean;
  viewTestHistory: boolean;
  
  // Advanced Actions (Primary Admin only)
  impersonateUser: boolean;
  deleteUser: boolean;
  makeAdmin: boolean;
  transferData: boolean;
}
```

## Test Management Features

### Test Session Monitoring
Real-time overview of active test sessions:
- **Active Tests**: Currently in-progress sessions
- **Paused Tests**: Suspended sessions requiring attention
- **Recent Completions**: Latest finished tests with scores
- **Flagged Tests**: Sessions marked for review due to anomalies

### Test Configuration Management
```typescript
interface TestConfiguration {
  testType: string;
  isActive: boolean;               // Enable/disable test type
  practiceMode: {
    enabled: boolean;
    questionLimit: number;
    timeLimit?: number;
  };
  officialMode: {
    questionCount: number;
    timeLimit?: number;
    passingScore: number;
    retakePolicy: RetakePolicy;
    cooldownHours?: number;
  };
  adaptiveSettings?: {
    enabled: boolean;
    startingDifficulty: number;
    advanceThreshold: number;
    regressThreshold: number;
  };
}
```

### Question Bank Management
- **Question Creation**: Add new questions with rich text editor
- **Content Review**: Moderate AI-generated questions
- **Difficulty Calibration**: Adjust question difficulty ratings
- **Usage Analytics**: Track question performance statistics
- **Bulk Import**: Import questions from CSV/Excel files

## Access Control Management

### Test Access Permissions
```typescript
enum AccessType {
  NONE = "NONE",                    // No access
  PRACTICE_ONLY = "PRACTICE_ONLY",  // Practice tests only
  ONE_TIME = "ONE_TIME",            // Single official attempt
  UNLIMITED = "UNLIMITED"           // Unlimited attempts
}

interface AccessGrantForm {
  userId: string;
  testTypeId: string;
  accessType: AccessType;
  expirationDate?: Date;           // Optional expiration
  notifyUser: boolean;             // Send notification email
  reason?: string;                 // Administrative note
}
```

### One-Time Code System
**Code Generation Interface:**
- **Batch Creation**: Generate multiple codes at once
- **Test Type Assignment**: Specify which test the code unlocks
- **Expiration Settings**: Set optional expiration dates
- **Usage Tracking**: Monitor code redemption rates

**Code Management:**
- **Active Codes**: Currently valid codes with usage status
- **Code History**: Previously generated and used codes
- **Bulk Deactivation**: Disable multiple codes simultaneously
- **Export Functionality**: Download code lists for distribution

### Access Request Workflow
```typescript
interface AccessRequestReview {
  requestId: string;
  decision: 'APPROVED' | 'DENIED';
  accessType?: AccessType;         // If approved
  expirationDate?: Date;           // If approved with expiration
  adminResponse?: string;          // Message to user
  notifyUser: boolean;             // Send decision email
}
```

## System Analytics Dashboard

### Usage Metrics
- **Daily Active Users**: Login and test activity trends
- **Test Completion Rates**: Success rates by test type
- **Average Scores**: Performance trends over time
- **Geographic Distribution**: User locations by ZIP code
- **Peak Usage Times**: Activity patterns by hour/day

### Performance Analytics
```typescript
interface SystemMetrics {
  userGrowth: TimeSeriesData[];      // New registrations over time
  testVolume: TestVolumeData[];      // Test attempts by type and date
  completionRates: CompletionStats;  // Success/failure rates
  averageScores: ScoreAnalytics[];   // Score trends and distributions
  systemHealth: HealthMetrics;       // Response times, error rates
}
```

### Reporting Features
- **Automated Reports**: Scheduled email reports for administrators
- **Custom Date Ranges**: Flexible time period selection
- **Export Options**: PDF, CSV, Excel format downloads
- **Trend Analysis**: Month-over-month and year-over-year comparisons

## System Settings Management

### Application Configuration
```typescript
interface ApplicationSettings {
  // Feature Flags
  twoFactorEnabled: boolean;
  testPausingEnabled: boolean;
  pdfDownloadEnabled: boolean;
  practiceTestEnabled: boolean;
  
  // AI Service Configuration
  aiServiceProvider: string;        // "openai" | "abacus" | "custom"
  aiServiceEnabled: boolean;
  aiServiceModel: string;
  
  // Branding & Customization
  customSignatureEnabled: boolean;
  signatureName?: string;
  signatureTitle?: string;
  signatureImage?: string;
  
  // System Limits
  maxRetakeAttempts: number;
  sessionTimeoutMinutes: number;
  fileUploadSizeMB: number;
}
```

### Email Template Management
- **Welcome Emails**: New user registration messages
- **Verification Emails**: Email address confirmation
- **Password Reset**: Security-related communications
- **Test Notifications**: Access granted/results available
- **Admin Alerts**: System notifications for administrators

### Security Settings
- **Password Policies**: Complexity requirements and expiration
- **Rate Limiting**: API and login attempt restrictions
- **Session Management**: Timeout and concurrent session limits
- **Audit Log Retention**: How long to keep administrative action logs

## Notification & Communication System

### Admin Alert Types
```typescript
interface AdminNotification {
  type: 'URGENT' | 'IMPORTANT' | 'INFO';
  category: 'SECURITY' | 'SYSTEM' | 'USER' | 'TEST';
  message: string;
  timestamp: Date;
  actionRequired: boolean;
  relatedEntityId?: string;
}
```

### Communication Templates
- **Bulk User Notifications**: Send announcements to user groups
- **Test Reminders**: Scheduled reminders for incomplete tests
- **System Maintenance**: Downtime and update notifications
- **Performance Alerts**: Automated system health warnings

## Administrative Actions & Audit Trail

### Action Logging System
```typescript
enum AdminActionType {
  USER_CREATED = 'USER_CREATED',
  USER_UPDATED = 'USER_UPDATED',
  USER_DELETED = 'USER_DELETED',
  ACCESS_GRANTED = 'ACCESS_GRANTED',
  ACCESS_REVOKED = 'ACCESS_REVOKED',
  TEST_CANCELLED = 'TEST_CANCELLED',
  SETTINGS_UPDATED = 'SETTINGS_UPDATED',
  CODES_GENERATED = 'CODES_GENERATED',
  QUESTION_MODIFIED = 'QUESTION_MODIFIED'
}

interface AdminAction {
  id: string;
  adminUserId: string;            // Who performed the action
  action: AdminActionType;        // What was done
  targetId?: string;              // What was affected
  details: any;                   // Action-specific data
  timestamp: Date;                // When it occurred
  ipAddress?: string;             // Security tracking
}
```

### Audit Trail Features
- **Comprehensive Logging**: All administrative actions tracked
- **Search & Filter**: Find specific actions by date, admin, type
- **Export Capabilities**: Download audit logs for compliance
- **Real-time Monitoring**: Live feed of administrative activities

## Data Export & Reporting

### Export Formats
- **User Data**: CSV, Excel, JSON formats
- **Test Results**: Detailed and summary reports
- **System Analytics**: Charts and raw data
- **Audit Logs**: Comprehensive action histories

### Report Scheduling
```typescript
interface ScheduledReport {
  name: string;
  reportType: 'USERS' | 'TESTS' | 'ANALYTICS' | 'AUDIT';
  schedule: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  recipients: string[];           // Admin email addresses
  format: 'PDF' | 'CSV' | 'EXCEL';
  filters?: ReportFilters;        // Optional data filtering
  lastRun?: Date;
  nextRun: Date;
}
```

## Mobile Responsiveness

### Responsive Design Features
- **Mobile-First Dashboard**: Optimized for tablet and phone access
- **Touch-Friendly Controls**: Large buttons and swipe gestures
- **Collapsible Navigation**: Efficient use of screen space
- **Progressive Disclosure**: Show relevant information based on screen size

### Mobile-Specific Features
- **Quick Actions**: Essential admin functions accessible with minimal taps
- **Push Notifications**: Real-time alerts for urgent administrative needs
- **Offline Capabilities**: Basic functionality when connectivity is limited

## Integration Capabilities

### Webhook Support
```typescript
interface WebhookConfiguration {
  endpoint: string;               // External system URL
  events: WebhookEvent[];         // Which events to send
  headers?: Record<string, string>; // Custom headers
  authentication?: {
    type: 'BEARER' | 'BASIC' | 'API_KEY';
    credentials: string;
  };
  retryConfig: {
    maxRetries: number;
    backoffMs: number;
  };
}
```

### API Integration
- **External LMS**: Learning Management System integration
- **HR Systems**: Employee record synchronization
- **Analytics Platforms**: Data export to business intelligence tools
- **Notification Services**: SMS and email service integrations

## Performance Optimization

### Dashboard Performance Features
- **Lazy Loading**: Load components as needed
- **Data Pagination**: Efficient handling of large datasets
- **Caching Strategy**: Redis-based caching for frequently accessed data
- **Background Processing**: Async handling of heavy operations

### Monitoring & Alerts
- **Response Time Monitoring**: Track dashboard performance
- **Error Rate Tracking**: Monitor and alert on system issues
- **Usage Analytics**: Understand admin workflow patterns
- **Resource Utilization**: Database and server performance metrics

---

*This comprehensive admin dashboard documentation provides complete understanding of administrative capabilities, workflows, and system management features within the Rubicon Programs Testing Application.*