import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { requireAuth } from '@/lib/auth';

export async function POST(_req: Request, { params }: { params: { id: string } }) {
  try {
    const user = await requireAuth();
    const userId = user.id;
    const id = params.id;

    const test = await prisma.mathTest.findUnique({ where: { id } });
    if (!test) {
      return NextResponse.json({ error: 'Test not found' }, { status: 404 });
    }
    if (test.userId !== userId) {
      return NextResponse.json({ error: 'Not authorized to modify this test' }, { status: 403 });
    }
    if (test.status === 'COMPLETED' || test.completedAt) {
      return NextResponse.json({ error: 'Cannot pause a completed test' }, { status: 400 });
    }
    if (test.status === 'CANCELLED') {
      return NextResponse.json({ error: 'Cannot pause a cancelled test' }, { status: 400 });
    }
    if (test.status === 'PAUSED' || test.pausedAt) {
      return NextResponse.json({ error: 'Test is already paused' }, { status: 400 });
    }

    const now = new Date();
    const updated = await prisma.mathTest.update({
      where: { id },
      data: { pausedAt: now, status: 'PAUSED' },
    });

    console.log(`[analytics] MATH_TEST_PAUSED testId=${id} userId=${userId}`);

    return NextResponse.json({
      id: updated.id,
      status: updated.status,
      pausedAt: updated.pausedAt,
    });
  } catch (error) {
    console.error('Error pausing test:', error);
    return NextResponse.json({ error: 'Something went wrong.' }, { status: 500 });
  }
}
