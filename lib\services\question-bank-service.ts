
import { PrismaClient } from '@prisma/client';
import { EnglishQuestion, WrongAnswer, EnglishQuestionType, QuestionWithAnswers } from '../types';

const prisma = new PrismaClient();

/**
 * @class QuestionBankService
 * @description Manages the question bank for English tests.
 */
export class QuestionBankService {

  /**
   * @method addQuestion
   * @description Adds a new question to the question bank.
   * @param {EnglishQuestion} question - The question to add.
   * @param {WrongAnswer[]} wrongAnswers - An array of wrong answers for the question.
   * @returns {Promise<EnglishQuestion>}
   */
  async addQuestion(question: EnglishQuestion, wrongAnswers: WrongAnswer[]): Promise<EnglishQuestion> {
    // Basic validation
    if (!question.question_text || !question.correct_answer) {
      throw new Error('Question text and correct answer are required.');
    }
    if (wrongAnswers.length < 2) {
      throw new Error('At least two wrong answers are required.');
    }

    const newQuestion = await prisma.english_questions.create({
      data: {
        ...question,
        wrong_answers: {
          create: wrongAnswers,
        },
      },
    });
    return newQuestion;
  }

  /**
   * @method getQuestionWithWrongAnswers
   * @description Retrieves a question with a specified number of wrong answers.
   * @param {string} questionId - The ID of the question to retrieve.
   * @param {number} count - The number of wrong answers to retrieve.
   * @returns {Promise<QuestionWithAnswers | null>}
   */
  async getQuestionWithWrongAnswers(questionId: string, count: number): Promise<QuestionWithAnswers | null> {
    const question = await prisma.english_questions.findUnique({
      where: { id: questionId },
      include: {
        wrong_answers: {
          take: count,
        },
      },
    });
    return question;
  }

  /**
   * @method buildMultipleChoiceOptions
   * @description Builds a set of multiple choice options for a question.
   * @param {QuestionWithAnswers} question - The question to build options for.
   * @param {number} wrongAnswerCount - The number of wrong answers to include.
   * @param {boolean} includeNoneOfTheAbove - Whether to include the "none of the above" option.
   * @returns {string[]}
   */
  buildMultipleChoiceOptions(question: QuestionWithAnswers, wrongAnswerCount: number, includeNoneOfTheAbove: boolean): string[] {
    let options = question.wrong_answers.map(a => a.wrong_answer);
    let correctAnswerIncluded = true;

    if (includeNoneOfTheAbove) {
      if (Math.random() < 0.2) { // 20% chance to exclude the correct answer
        correctAnswerIncluded = false;
      } else {
        options.push(question.correct_answer);
      }
      options.push('None of the above');
    } else {
      options.push(question.correct_answer);
    }

    // Shuffle the options
    for (let i = options.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [options[i], options[j]] = [options[j], options[i]];
    }

    return options;
  }

  /**
   * @method updateQuestionStatistics
   * @description Updates the usage statistics for a question.
   * @param {string} questionId - The ID of the question to update.
   * @param {boolean} wasCorrect - Whether the question was answered correctly.
   * @returns {Promise<void>}
   */
  async updateQuestionStatistics(questionId: string, wasCorrect: boolean): Promise<void> {
    await prisma.english_questions.update({
      where: { id: questionId },
      data: {
        times_used: { increment: 1 },
        times_correct: wasCorrect ? { increment: 1 } : {},
      },
    });
  }

  /**
   * @method getQuestionBankUtilization
   * @description Returns the percentage of questions to use from the question bank.
   * @param {EnglishQuestionType} testType - The type of test.
   * @returns {number}
   */
  getQuestionBankUtilization(testType: EnglishQuestionType): number {
    // This can be customized based on the test type
    return 0.9; // Use 90% of questions from the bank
  }

  /**
   * @method pruneInactiveQuestions
   * @description Removes questions that are performing poorly.
   * @returns {Promise<void>}
   */
  async pruneInactiveQuestions(): Promise<void> {
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

    await prisma.english_questions.deleteMany({
      where: {
        is_active: true,
        times_used: { gt: 100 },
        // @ts-ignore
        times_correct: { not: 0 }, // Avoid division by zero
        // @ts-ignore
        AND: [
          {
            // Expression for correctness ratio
            // This is a placeholder and may need to be adjusted based on your Prisma version
            // and database capabilities. A raw query might be necessary here.
            // For now, we'll just delete questions that haven't been used in a year.
            updated_at: { lt: oneYearAgo },
          },
        ],
      },
    });
  }

  /**
   * @method selectQuestions
   * @description Selects a set of questions for a test based on a selection algorithm.
   * @param {number} count - The number of questions to select.
   * @param {EnglishQuestionType[]} questionTypes - The types of questions to select.
   * @returns {Promise<EnglishQuestion[]>}
   */
  async selectQuestions(count: number, questionTypes: EnglishQuestionType[]): Promise<EnglishQuestion[]> {
    // This is a basic implementation. A more advanced algorithm would consider
    // difficulty distribution, user's past performance, etc.
    const questions = await prisma.english_questions.findMany({
      where: {
        is_active: true,
        question_type: { in: questionTypes },
      },
      orderBy: {
        // Prioritize questions that have been used less
        times_used: 'asc',
      },
      take: count,
    });

    return questions;
  }
}

