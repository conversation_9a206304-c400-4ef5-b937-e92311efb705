import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { requireAdmin } from '@/lib/auth';

export async function POST(req: NextRequest) {
  if (!requireAdmin()) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body = await req.json();
    const { questionText, answer, gradeLevel } = body;

    if (!questionText || !answer || gradeLevel === undefined) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const question = await prisma.mathQuestion.create({
      data: {
        questionText,
        answer,
        gradeLevel,
      },
    });

    return NextResponse.json(question, { status: 201 });
  } catch (error) {
    console.error('Error creating math question:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function GET(req: NextRequest) {
    if (!requireAdmin()) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
  
    try {
      const questions = await prisma.mathQuestion.findMany();
      return NextResponse.json(questions);
    } catch (error) {
      console.error('Error fetching math questions:', error);
      return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }
  }