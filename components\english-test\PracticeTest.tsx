import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, XCircle, ArrowRight, Home } from 'lucide-react';
import EnglishQuestionDisplay from './EnglishQuestionDisplay';
import ReadingPassageDisplay from './ReadingPassageDisplay';

interface PracticeTestProps {
  gradeLevel: string;
  onComplete?: () => void;
  onReturnToDashboard?: () => void;
}

interface Question {
  id: string;
  type: 'multiple-choice' | 'spelling' | 'grammar' | 'reading-comprehension';
  questionText: string;
  options?: string[];
  correctAnswer: string;
  explanation: string;
  gradeLevel: string;
  passageId?: string;
}

interface Passage {
  id: string;
  title: string;
  content: string;
}

interface Feedback {
  correct: boolean;
  correctAnswer: string;
  explanation: string;
  userAnswer: string;
}

interface Progress {
  current: number;
  total: number;
  percentage: number;
}

const PracticeTest: React.FC<PracticeTestProps> = ({ 
  gradeLevel, 
  onComplete, 
  onReturnToDashboard 
}) => {
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null);
  const [currentPassage, setCurrentPassage] = useState<Passage | null>(null);
  const [progress, setProgress] = useState<Progress>({ current: 0, total: 0, percentage: 0 });
  const [feedback, setFeedback] = useState<Feedback | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [completed, setCompleted] = useState(false);
  const [showFeedback, setShowFeedback] = useState(false);
  const [userAnswer, setUserAnswer] = useState<string>('');

  // Initialize practice session
  useEffect(() => {
    initializePracticeSession();
  }, [gradeLevel]);

  const initializePracticeSession = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/tests/english/practice', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ gradeLevel }),
      });

      if (!response.ok) {
        throw new Error('Failed to create practice session');
      }

      const data = await response.json();
      setSessionId(data.sessionId);
      setCurrentQuestion(data.currentQuestion);
      setProgress(data.progress);
      
      // Set passage if it's a reading comprehension question
      if (data.currentQuestion?.type === 'reading-comprehension' && data.passages) {
        const passage = data.passages.find((p: Passage) => 
          p.id === data.currentQuestion.passageId
        );
        if (passage) setCurrentPassage(passage);
      }
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const submitAnswer = async (answer: string) => {
    if (!sessionId || !currentQuestion) return;

    setLoading(true);
    setUserAnswer(answer);
    
    try {
      const response = await fetch('/api/tests/english/practice', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          questionId: currentQuestion.id,
          answer,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit answer');
      }

      const data = await response.json();
      
      // Show feedback
      setFeedback(data.feedback);
      setShowFeedback(true);
      setProgress(data.progress);
      
      // Check if test is completed
      if (data.completed) {
        setCompleted(true);
        setTimeout(() => {
          onComplete?.();
        }, 3000); // Auto-complete after 3 seconds
      } else {
        // Prepare next question
        setTimeout(() => {
          setCurrentQuestion(data.nextQuestion);
          setCurrentPassage(data.passage || null);
          setShowFeedback(false);
          setFeedback(null);
          setUserAnswer('');
        }, 3000); // Show feedback for 3 seconds
      }
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const endSession = async () => {
    if (!sessionId) return;
    
    try {
      await fetch(`/api/tests/english/practice?sessionId=${sessionId}`, {
        method: 'DELETE',
      });
      onReturnToDashboard?.();
    } catch (err) {
      console.error('Error ending session:', err);
      onReturnToDashboard?.();
    }
  };

  if (loading && !sessionId) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Starting your practice test...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert className="m-4">
        <XCircle className="h-4 w-4" />
        <AlertDescription>
          {error}
          <Button 
            onClick={initializePracticeSession} 
            className="ml-4"
            size="sm"
          >
            Try Again
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  if (completed) {
    return (
      <Card className="max-w-2xl mx-auto m-4">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2">
            <CheckCircle className="h-6 w-6 text-green-600" />
            Practice Complete!
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p>Great job completing the practice test!</p>
          <p className="text-sm text-gray-600">
            You answered {progress.current} questions. 
            Remember, this was just practice - your responses weren't saved.
          </p>
          <div className="flex gap-4 justify-center">
            <Button onClick={initializePracticeSession} variant="outline">
              Practice Again
            </Button>
            <Button onClick={onReturnToDashboard} className="flex items-center gap-2">
              <Home className="h-4 w-4" />
              Return to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-4 space-y-6">
      {/* Progress Header */}
      <Card>
        <CardContent className="p-4">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg font-semibold">Practice Test - Grade {gradeLevel}</h2>
            <Button onClick={endSession} variant="outline" size="sm">
              End Practice
            </Button>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-gray-600">
              <span>Question {progress.current} of {progress.total}</span>
              <span>{progress.percentage}% Complete</span>
            </div>
            <Progress value={progress.percentage} className="w-full" />
          </div>
        </CardContent>
      </Card>

      {/* Feedback Display */}
      {showFeedback && feedback && (
        <Alert className={feedback.correct ? 'border-green-500' : 'border-red-500'}>
          <div className="flex items-center gap-2">
            {feedback.correct ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <XCircle className="h-5 w-5 text-red-600" />
            )}
            <AlertDescription className="flex-1">
              <div className="space-y-2">
                <p className="font-medium">
                  {feedback.correct ? 'Correct!' : 'Incorrect'}
                </p>
                {!feedback.correct && (
                  <p className="text-sm">
                    Your answer: <span className="font-mono">{feedback.userAnswer}</span>
                  </p>
                )}
                <p className="text-sm">
                  Correct answer: <span className="font-mono">{feedback.correctAnswer}</span>
                </p>
                <p className="text-sm italic">{feedback.explanation}</p>
              </div>
            </AlertDescription>
          </div>
        </Alert>
      )}

      {/* Question Display */}
      {currentQuestion && !showFeedback && (
        <div className="space-y-4">
          {/* Reading Passage */}
          {currentQuestion.type === 'reading-comprehension' && currentPassage && (
            <ReadingPassageDisplay
              passage={currentPassage.content}
              passageQuestions={[]} // Not tracking passage questions in practice mode
            />
          )}

          {/* Question */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">
                Question {progress.current}
                <span className="ml-2 text-sm font-normal text-gray-600 capitalize">
                  ({currentQuestion.type.replace('-', ' ')})
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <EnglishQuestionDisplayWithSubmit
                question={currentQuestion}
                onSubmit={submitAnswer}
                disabled={loading}
              />
            </CardContent>
          </Card>
        </div>
      )}

      {/* Loading overlay for answer submission */}
      {loading && showFeedback && (
        <div className="text-center p-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-sm text-gray-600">Loading next question...</p>
        </div>
      )}
    </div>
  );
};

// Enhanced question display component with submission handling
interface EnglishQuestionDisplayWithSubmitProps {
  question: Question;
  onSubmit: (answer: string) => void;
  disabled: boolean;
}

const EnglishQuestionDisplayWithSubmit: React.FC<EnglishQuestionDisplayWithSubmitProps> = ({
  question,
  onSubmit,
  disabled
}) => {
  const [selectedAnswer, setSelectedAnswer] = useState<string>('');
  const [textAnswer, setTextAnswer] = useState<string>('');

  const handleSubmit = () => {
    const answer = question.type === 'spelling' ? textAnswer : selectedAnswer;
    if (answer.trim()) {
      onSubmit(answer.trim());
    }
  };

  const canSubmit = question.type === 'spelling' 
    ? textAnswer.trim().length > 0 
    : selectedAnswer.length > 0;

  return (
    <div className="space-y-4">
      <div className="text-lg">{question.questionText}</div>
      
      {question.type === 'multiple-choice' || question.type === 'grammar' || question.type === 'reading-comprehension' ? (
        <div className="space-y-2">
          {question.options?.map((option, index) => (
            <label key={index} className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="answer"
                value={option}
                checked={selectedAnswer === option}
                onChange={(e) => setSelectedAnswer(e.target.value)}
                disabled={disabled}
                className="text-blue-600"
              />
              <span>{option}</span>
            </label>
          ))}
        </div>
      ) : (
        <div className="space-y-2">
          <input
            type="text"
            value={textAnswer}
            onChange={(e) => setTextAnswer(e.target.value)}
            placeholder="Type your answer here..."
            disabled={disabled}
            className="w-full p-2 border rounded-md"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && canSubmit && !disabled) {
                handleSubmit();
              }
            }}
          />
        </div>
      )}
      
      <Button 
        onClick={handleSubmit}
        disabled={!canSubmit || disabled}
        className="w-full flex items-center justify-center gap-2"
      >
        {disabled ? (
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
        ) : (
          <ArrowRight className="h-4 w-4" />
        )}
        Submit Answer
      </Button>
    </div>
  );
};

export default PracticeTest;
