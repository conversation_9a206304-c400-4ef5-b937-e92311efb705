/*
  Warnings:

  - You are about to drop the `MathTestAttempt` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `MathTestToMathQuestion` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "MathTestAttempt" DROP CONSTRAINT "MathTestAttempt_mathQuestionId_fkey";

-- DropForeignKey
ALTER TABLE "MathTestAttempt" DROP CONSTRAINT "MathTestAttempt_mathTestId_fkey";

-- DropForeignKey
ALTER TABLE "MathTestToMathQuestion" DROP CONSTRAINT "MathTestToMathQuestion_mathQuestionId_fkey";

-- DropForeignKey
ALTER TABLE "MathTestToMathQuestion" DROP CONSTRAINT "MathTestToMathQuestion_mathTestId_fkey";

-- DropTable
DROP TABLE "MathTestAttempt";

-- DropTable
DROP TABLE "MathTestToMathQuestion";

-- CreateTable
CREATE TABLE "MathTestQuestion" (
    "id" TEXT NOT NULL,
    "mathTestId" TEXT NOT NULL,
    "mathQuestionId" TEXT NOT NULL,
    "userAnswer" TEXT,
    "isCorrect" BOOLEAN,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "MathTestQuestion_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "MathTestQuestion" ADD CONSTRAINT "MathTestQuestion_mathTestId_fkey" FOREIGN KEY ("mathTestId") REFERENCES "MathTest"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MathTestQuestion" ADD CONSTRAINT "MathTestQuestion_mathQuestionId_fkey" FOREIGN KEY ("mathQuestionId") REFERENCES "MathQuestion"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
