
import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { requireAuth } from '@/lib/auth';
import { generateAIQuestion } from '@/lib/ai-service';

export async function POST(req: Request) {
  try {
    const user = await requireAuth();
    const userId = user.id;

    // 1. Create a new MathTest
    const test = await prisma.mathTest.create({
      data: {
        userId,
        isPractice: false,
        status: 'STARTED',
        currentGradeLevel: 5, // As per prompt, user starts at 5th grade level
      },
    });

    // 2. Get the first question (6th grade)
    let question;
    const gradeLevel = 6;
    const existingQuestions = await prisma.mathQuestion.findMany({
      where: { gradeLevel },
    });

    const useBank = existingQuestions.length > 0 && Math.random() < 0.8;

    if (useBank) {
      question = existingQuestions[Math.floor(Math.random() * existingQuestions.length)];
    } else {
      try {
        const aiResponse = await generateAIQuestion(gradeLevel);
        question = await prisma.mathQuestion.create({
          data: {
            questionText: aiResponse.questionText,
            answer: aiResponse.answer,
            gradeLevel,
          },
        });
      } catch (aiError) {
        console.error('AI question generation failed:', aiError);
        // Fallback to existing questions if AI fails
        if (existingQuestions.length > 0) {
          question = existingQuestions[Math.floor(Math.random() * existingQuestions.length)];
        } else {
          // Last resort: create a simple fallback question
          question = await prisma.mathQuestion.create({
            data: {
              questionText: `What is ${Math.floor(Math.random() * 20) + 1} + ${Math.floor(Math.random() * 20) + 1}?`,
              answer: ((Math.floor(Math.random() * 20) + 1) + (Math.floor(Math.random() * 20) + 1)).toString(),
              gradeLevel,
            },
          });
        }
      }
    }
    
    // Associate question with test
    await prisma.mathTestQuestion.create({
        data: {
            mathTestId: test.id,
            mathQuestionId: question.id
        }
    });


    return NextResponse.json({ testId: test.id, question });
  } catch (error) {
    console.error("Error starting test:", error);
    return NextResponse.json({ error: 'Something went wrong.' }, { status: 500 });
  }
}
