# WARP.md

This file provides guidance to WARP (warp.dev) when working with code in this repository.

## Overview

This is a **Next.js 14** web application called "Rubicon Programs Testing Application" - a professional skills testing platform that assesses typing, digital literacy, mathematics, and English proficiency. The application uses the App Router with TypeScript and is designed for educational institutions and professional development programs.

## Architecture

- **Frontend**: Next.js 14 with TypeScript, Tailwind CSS, Radix UI components
- **Backend**: Next.js API routes with Prisma ORM
- **Database**: PostgreSQL with comprehensive schema for users, tests, questions, and results
- **Authentication**: NextAuth.js with credentials provider and session management
- **UI Components**: Custom component library in `/components/ui/` using Radix UI primitives
- **State Management**: React hooks with some Zustand for global state

## Key Development Commands

### Setup and Installation
```bash
# Install dependencies (using Yarn)
yarn install

# Database operations
npx prisma generate          # Generate Prisma client
npx prisma migrate dev       # Run database migrations  
npx prisma db seed           # Seed database with test data
```

### Development
```bash
# Start development server
yarn dev                     # Runs on http://localhost:3000

# Build and production
yarn build                   # Create production build
yarn start                   # Start production server

# Code quality
yarn lint                    # Run ESLint

# Database management
npx prisma studio           # Open database browser
npx prisma migrate reset    # Reset database (dev only)
```

### Testing Commands
```bash
# No test framework currently configured
# Consider adding Jest or Vitest for unit tests
```

## Core Application Structure

### User Types & Access Control
- **USER**: Standard test-takers with role-based access to specific test types
- **ADMIN**: Full administrative access, can manage users, tests, and system settings
- **Primary Admin**: Special admin role with additional privileges

### Test Architecture
The application supports four main test categories:

1. **Typing Tests**:
   - Keyboarding (WPM-based with passage text)
   - 10-Key (KPH-based with numeric sequences, enhanced vertical display)
   - Real-time statistics calculation with accuracy tracking

2. **Math Tests**:
   - Adaptive difficulty system (5th-12th grade levels)
   - Practice and full test modes
   - Grade-level scoring system

3. **Digital Literacy Tests**:
   - Computer proficiency assessment
   - Software operation knowledge

4. **English Tests**:
   - Reading comprehension
   - Grammar and vocabulary assessment

### Key Database Models
- `users`: User profiles with detailed personal information
- `tests`: Individual test instances with status tracking
- `test_types`: Test configurations and metadata
- `questions`: Question bank with multiple question types
- `test_results`: Comprehensive result storage
- `user_test_access`: Fine-grained access control per user/test type

### Authentication Flow
- Email/password credentials via NextAuth.js
- JWT tokens with user type and admin status
- Admin impersonation capabilities
- Email verification system with password reset functionality

## Development Patterns

### Component Architecture
- Page components in `/app/` directory (App Router)
- Reusable UI components in `/components/ui/`
- Layout components in `/components/layout/`
- Test-specific components organized by test type

### API Route Patterns
- RESTful API design in `/app/api/`
- Authentication middleware using NextAuth session checks
- Database operations via Prisma client
- Comprehensive error handling and validation

### State Management
- Server state via Prisma queries and mutations
- Client state using React hooks (useState, useEffect)
- Form state management with controlled components
- Real-time updates during test sessions

### Styling Conventions
- Tailwind CSS utility classes
- Component-specific styling in TSX files
- Responsive design patterns (mobile-first)
- Custom design system with consistent spacing and colors

## Important Development Notes

### Test Session Management
- Tests support pause/resume functionality
- Real-time progress tracking with keystroke logging
- Auto-save mechanisms for incomplete tests
- Session restoration capabilities

### Security Considerations
- All admin routes require authentication checks
- User data encryption for sensitive information
- SQL injection prevention via Prisma
- XSS protection through proper input sanitization

### Performance Optimizations
- Dynamic imports for test components
- Image optimization through Next.js Image component
- Database query optimization with proper indexing
- Client-side caching for frequently accessed data

### Environmental Configuration
- Requires PostgreSQL database connection (`DATABASE_URL`)
- NextAuth configuration (`NEXTAUTH_SECRET`)
- Optional AI service integration for question generation

## Testing and Quality Assurance

### Manual Testing Workflows
- Test each test type in both practice and official modes
- Verify admin functionality (user management, test configuration)
- Check responsive design across different screen sizes
- Validate authentication flows and access control

### Code Quality
- TypeScript strict mode enabled
- ESLint configuration for Next.js
- Prettier formatting (manual)
- Database schema validation via Prisma

### Browser Support
- Designed for modern desktop browsers
- Mobile devices detected and restricted for typing tests
- Keyboard-specific functionality (Num Lock detection for 10-key tests)

## Deployment Considerations

- Production build via `yarn build`
- Database migrations must be run in production
- Environment variables must be configured
- Static asset optimization enabled
- Consider CDN for image assets in `/public/images/`
