interface QuestionResponse {
  questionId: string;
  isCorrect: boolean;
  gradeLevel: number;
  questionType: 'VOCABULARY' | 'SPELLING' | 'GRAMMAR' | 'PUNCTUATION' | 'READING_COMPREHENSION';
  timeSpent: number;
  answer: string;
  difficulty: number;
}

interface ScoreBreakdown {
  languageMastery: {
    score: number;
    weight: number;
    subscores: {
      vocabulary: number;
      spelling: number;
      grammar: number;
      punctuation: number;
    };
  };
  readingComprehension: {
    score: number;
    weight: number;
  };
  bonuses: {
    adaptive: number;
    time: number;
  };
  overall: number;
}

interface GradeLevelResult {
  achievedLevel: number;
  confidence: number;
  proficiencyDescription: string;
  standardsAlignment: string[];
}

interface DetailedFeedback {
  strengths: string[];
  improvements: string[];
  studyRecommendations: string[];
  nextSteps: string[];
}

interface ScoringResult {
  scores: ScoreBreakdown;
  gradeLevel: GradeLevelResult;
  feedback: DetailedFeedback;
  performance: {
    questionsCorrect: number;
    questionsTotal: number;
    averageTimePerQuestion: number;
    accuracyByType: Record<string, number>;
    accuracyByGrade: Record<number, number>;
  };
}

export class EnglishScoringEngine {
  private readonly LANGUAGE_MASTERY_WEIGHT = 0.60;
  private readonly READING_COMPREHENSION_WEIGHT = 0.40;
  private readonly MAX_ADAPTIVE_BONUS = 0.05;
  private readonly MAX_TIME_BONUS = 0.03;

  // Common Core grade level standards mapping
  private readonly GRADE_LEVEL_STANDARDS = {
    5: [
      'Determine theme from details in text',
      'Compare and contrast story elements',
      'Use context to determine word meaning',
      'Recognize figurative language'
    ],
    6: [
      'Analyze how sentences/paragraphs fit overall structure',
      'Determine author\'s point of view',
      'Use Greek/Latin affixes and roots',
      'Gather relevant information from sources'
    ],
    7: [
      'Analyze development of theme/central idea',
      'Compare and contrast fiction and nonfiction',
      'Analyze word choice impact on meaning',
      'Evaluate arguments and claims in text'
    ],
    8: [
      'Analyze connections among individuals/events/ideas',
      'Determine author\'s purpose and analyze rhetoric',
      'Verify preliminary determination of word meaning',
      'Evaluate advantages/disadvantages of media'
    ],
    9: [
      'Analyze complex characters and their development',
      'Determine central ideas and analyze development',
      'Analyze cumulative impact of word choices',
      'Assess reasoning validity and evidence sufficiency'
    ],
    10: [
      'Analyze how author transforms themes/topics',
      'Delineate and evaluate argument reasoning',
      'Analyze nuances in word meanings',
      'Synthesize information from multiple sources'
    ],
    11: [
      'Analyze multiple interpretations of story/drama',
      'Evaluate effectiveness of structure in argument',
      'Analyze author\'s choices in text structure',
      'Integrate and evaluate multiple sources'
    ],
    12: [
      'Demonstrate knowledge of foundational works',
      'Analyze and evaluate effectiveness of structure',
      'Demonstrate understanding of figurative language',
      'Synthesize information to address questions'
    ]
  };

  /**
   * Calculate comprehensive English test score
   */
  calculateScore(responses: QuestionResponse[], testMetadata: {
    startingGradeLevel: number;
    highestGradeReached: number;
    totalTimeSpent: number;
    targetTime: number;
  }): ScoringResult {
    const scoreBreakdown = this.calculateScoreBreakdown(responses, testMetadata);
    const gradeLevel = this.determineGradeLevel(responses, scoreBreakdown);
    const feedback = this.generateDetailedFeedback(responses, scoreBreakdown, gradeLevel);
    const performance = this.calculatePerformanceMetrics(responses);

    return {
      scores: scoreBreakdown,
      gradeLevel,
      feedback,
      performance
    };
  }

  private calculateScoreBreakdown(responses: QuestionResponse[], metadata: any): ScoreBreakdown {
    // Calculate language mastery subscores
    const vocabularyScore = this.calculateSubscore(responses, 'VOCABULARY');
    const spellingScore = this.calculateSubscore(responses, 'SPELLING');
    const grammarScore = this.calculateSubscore(responses, 'GRAMMAR');
    const punctuationScore = this.calculateSubscore(responses, 'PUNCTUATION');

    // Weight language mastery components (vocabulary gets higher weight)
    const languageMasteryScore = 
      vocabularyScore * 0.35 +
      grammarScore * 0.30 +
      spellingScore * 0.20 +
      punctuationScore * 0.15;

    // Calculate reading comprehension score
    const readingComprehensionScore = this.calculateSubscore(responses, 'READING_COMPREHENSION');

    // Calculate bonuses
    const adaptiveBonus = this.calculateAdaptiveBonus(metadata.startingGradeLevel, metadata.highestGradeReached);
    const timeBonus = this.calculateTimeBonus(metadata.totalTimeSpent, metadata.targetTime);

    // Calculate weighted overall score
    const baseScore = 
      languageMasteryScore * this.LANGUAGE_MASTERY_WEIGHT +
      readingComprehensionScore * this.READING_COMPREHENSION_WEIGHT;

    const overallScore = Math.min(100, baseScore + adaptiveBonus + timeBonus);

    return {
      languageMastery: {
        score: languageMasteryScore,
        weight: this.LANGUAGE_MASTERY_WEIGHT,
        subscores: {
          vocabulary: vocabularyScore,
          spelling: spellingScore,
          grammar: grammarScore,
          punctuation: punctuationScore
        }
      },
      readingComprehension: {
        score: readingComprehensionScore,
        weight: this.READING_COMPREHENSION_WEIGHT
      },
      bonuses: {
        adaptive: adaptiveBonus,
        time: timeBonus
      },
      overall: overallScore
    };
  }

  private calculateSubscore(responses: QuestionResponse[], questionType: string): number {
    const typeResponses = responses.filter(r => r.questionType === questionType);
    if (typeResponses.length === 0) return 0;

    let totalWeightedScore = 0;
    let totalWeight = 0;

    typeResponses.forEach(response => {
      // Weight by difficulty and grade level
      const difficultyWeight = Math.max(1, response.difficulty || 1);
      const gradeLevelWeight = Math.max(1, response.gradeLevel - 4); // Grade 5 = weight 1, Grade 12 = weight 8
      
      const weight = difficultyWeight * gradeLevelWeight;
      totalWeight += weight;
      
      if (response.isCorrect) {
        totalWeightedScore += weight;
      }
    });

    return totalWeight > 0 ? (totalWeightedScore / totalWeight) * 100 : 0;
  }

  private calculateAdaptiveBonus(startingGrade: number, highestGrade: number): number {
    const gradeProgression = Math.max(0, highestGrade - startingGrade);
    // Award bonus for each grade level advanced beyond starting point
    return Math.min(this.MAX_ADAPTIVE_BONUS * 100, gradeProgression * 1.5);
  }

  private calculateTimeBonus(actualTime: number, targetTime: number): number {
    if (actualTime <= 0 || targetTime <= 0) return 0;
    
    const efficiency = targetTime / actualTime;
    if (efficiency >= 1.5) {
      // Completed 50% faster than target
      return this.MAX_TIME_BONUS * 100;
    } else if (efficiency >= 1.2) {
      // Completed 20% faster than target
      return (this.MAX_TIME_BONUS * 100) * 0.6;
    } else if (efficiency >= 1.0) {
      // Completed at or slightly faster than target
      return (this.MAX_TIME_BONUS * 100) * 0.3;
    }
    
    return 0;
  }

  private determineGradeLevel(responses: QuestionResponse[], scoreBreakdown: ScoreBreakdown): GradeLevelResult {
    const overallScore = scoreBreakdown.overall;
    
    // Analyze performance at different grade levels
    const gradePerformance = this.analyzeGradePerformance(responses);
    
    let achievedLevel = 5; // Minimum grade level
    let confidence = 0.5; // Low confidence by default
    
    // Find highest grade level where student performed well
    for (let grade = 12; grade >= 5; grade--) {
      const performance = gradePerformance[grade];
      if (performance && performance.totalQuestions >= 3) {
        const accuracy = (performance.correct / performance.totalQuestions) * 100;
        
        if (accuracy >= 90) {
          achievedLevel = grade;
          confidence = 0.95;
          break;
        } else if (accuracy >= 80) {
          achievedLevel = Math.max(5, grade - 0.5);
          confidence = 0.85;
          break;
        } else if (accuracy >= 70) {
          achievedLevel = Math.max(5, grade - 1);
          confidence = 0.75;
          break;
        }
      }
    }

    // Fallback to score-based mapping if grade-specific data insufficient
    if (confidence < 0.7) {
      if (overallScore >= 90) achievedLevel = Math.max(achievedLevel, 10);
      else if (overallScore >= 80) achievedLevel = Math.max(achievedLevel, 8);
      else if (overallScore >= 70) achievedLevel = Math.max(achievedLevel, 6);
      else if (overallScore >= 60) achievedLevel = Math.max(achievedLevel, 5);
      
      confidence = Math.min(0.8, overallScore / 100);
    }

    return {
      achievedLevel: Math.round(achievedLevel),
      confidence,
      proficiencyDescription: this.getProficiencyDescription(achievedLevel, overallScore),
      standardsAlignment: this.getStandardsAlignment(Math.round(achievedLevel))
    };
  }

  private analyzeGradePerformance(responses: QuestionResponse[]): Record<number, { correct: number; totalQuestions: number }> {
    const gradePerformance: Record<number, { correct: number; totalQuestions: number }> = {};
    
    responses.forEach(response => {
      const grade = response.gradeLevel;
      if (!gradePerformance[grade]) {
        gradePerformance[grade] = { correct: 0, totalQuestions: 0 };
      }
      gradePerformance[grade].totalQuestions++;
      if (response.isCorrect) {
        gradePerformance[grade].correct++;
      }
    });
    
    return gradePerformance;
  }

  private getProficiencyDescription(gradeLevel: number, overallScore: number): string {
    const grade = Math.round(gradeLevel);
    
    if (overallScore >= 90) {
      return `Advanced proficiency at ${this.getGradeName(grade)} level - demonstrates mastery of language arts concepts`;
    } else if (overallScore >= 80) {
      return `Proficient at ${this.getGradeName(grade)} level - shows solid understanding of key concepts`;
    } else if (overallScore >= 70) {
      return `Approaching proficiency at ${this.getGradeName(grade)} level - developing understanding with some gaps`;
    } else {
      return `Below proficiency at ${this.getGradeName(grade)} level - needs focused instruction in foundational skills`;
    }
  }

  private getGradeName(grade: number): string {
    const gradeNames: Record<number, string> = {
      5: '5th grade',
      6: '6th grade', 
      7: '7th grade',
      8: '8th grade',
      9: '9th grade',
      10: '10th grade',
      11: '11th grade',
      12: '12th grade'
    };
    return gradeNames[grade] || `grade ${grade}`;
  }

  private getStandardsAlignment(gradeLevel: number): string[] {
    return this.GRADE_LEVEL_STANDARDS[gradeLevel as keyof typeof this.GRADE_LEVEL_STANDARDS] || [];
  }

  private generateDetailedFeedback(
    responses: QuestionResponse[], 
    scoreBreakdown: ScoreBreakdown, 
    gradeLevel: GradeLevelResult
  ): DetailedFeedback {
    const strengths: string[] = [];
    const improvements: string[] = [];
    const studyRecommendations: string[] = [];
    const nextSteps: string[] = [];

    // Analyze strengths
    const { subscores } = scoreBreakdown.languageMastery;
    const rcScore = scoreBreakdown.readingComprehension.score;

    if (subscores.vocabulary >= 80) {
      strengths.push('Strong vocabulary knowledge and word recognition skills');
    }
    if (subscores.grammar >= 80) {
      strengths.push('Solid understanding of grammatical structures and rules');
    }
    if (subscores.spelling >= 80) {
      strengths.push('Good spelling accuracy and phonetic awareness');
    }
    if (subscores.punctuation >= 80) {
      strengths.push('Effective use of punctuation and writing mechanics');
    }
    if (rcScore >= 80) {
      strengths.push('Strong reading comprehension and text analysis skills');
    }

    // Identify areas for improvement
    if (subscores.vocabulary < 70) {
      improvements.push('Vocabulary development and word meaning comprehension');
      studyRecommendations.push('Use vocabulary workbooks, flashcards, and read diverse texts');
      studyRecommendations.push('Practice using context clues to determine word meanings');
    }
    if (subscores.grammar < 70) {
      improvements.push('Grammar rules and sentence structure');
      studyRecommendations.push('Complete grammar exercises focusing on parts of speech and sentence types');
      studyRecommendations.push('Practice identifying and correcting common grammatical errors');
    }
    if (subscores.spelling < 70) {
      improvements.push('Spelling accuracy and word formation patterns');
      studyRecommendations.push('Study common spelling patterns and word roots');
      studyRecommendations.push('Practice with spelling lists and dictation exercises');
    }
    if (subscores.punctuation < 70) {
      improvements.push('Punctuation usage and writing mechanics');
      studyRecommendations.push('Review punctuation rules and practice with editing exercises');
      studyRecommendations.push('Focus on comma usage, quotation marks, and end punctuation');
    }
    if (rcScore < 70) {
      improvements.push('Reading comprehension and text analysis');
      studyRecommendations.push('Practice active reading strategies like summarizing and questioning');
      studyRecommendations.push('Work with various text types including fiction, nonfiction, and poetry');
    }

    // Generate next steps based on achieved grade level
    const achievedGrade = gradeLevel.achievedLevel;
    if (achievedGrade < 8) {
      nextSteps.push('Focus on building foundational reading and writing skills');
      nextSteps.push('Practice with grade-appropriate texts and vocabulary');
    } else if (achievedGrade >= 8 && achievedGrade < 10) {
      nextSteps.push('Challenge yourself with more complex texts and writing assignments');
      nextSteps.push('Work on critical analysis and inference skills');
    } else {
      nextSteps.push('Engage with advanced literature and analytical writing');
      nextSteps.push('Prepare for college-level reading and writing demands');
    }

    // Add bonus-based feedback
    if (scoreBreakdown.bonuses.adaptive > 2) {
      strengths.push('Excellent adaptive learning - successfully tackled higher difficulty questions');
    }
    if (scoreBreakdown.bonuses.time > 1) {
      strengths.push('Efficient test-taking with good time management skills');
    }

    return {
      strengths,
      improvements,
      studyRecommendations,
      nextSteps
    };
  }

  private calculatePerformanceMetrics(responses: QuestionResponse[]) {
    const totalQuestions = responses.length;
    const correctAnswers = responses.filter(r => r.isCorrect).length;
    const totalTime = responses.reduce((sum, r) => sum + r.timeSpent, 0);

    // Calculate accuracy by question type
    const accuracyByType: Record<string, number> = {};
    const questionTypes = ['VOCABULARY', 'SPELLING', 'GRAMMAR', 'PUNCTUATION', 'READING_COMPREHENSION'];
    
    questionTypes.forEach(type => {
      const typeResponses = responses.filter(r => r.questionType === type);
      if (typeResponses.length > 0) {
        const correct = typeResponses.filter(r => r.isCorrect).length;
        accuracyByType[type] = (correct / typeResponses.length) * 100;
      }
    });

    // Calculate accuracy by grade level
    const accuracyByGrade: Record<number, number> = {};
    for (let grade = 5; grade <= 12; grade++) {
      const gradeResponses = responses.filter(r => r.gradeLevel === grade);
      if (gradeResponses.length > 0) {
        const correct = gradeResponses.filter(r => r.isCorrect).length;
        accuracyByGrade[grade] = (correct / gradeResponses.length) * 100;
      }
    }

    return {
      questionsCorrect: correctAnswers,
      questionsTotal: totalQuestions,
      averageTimePerQuestion: totalQuestions > 0 ? totalTime / totalQuestions : 0,
      accuracyByType,
      accuracyByGrade
    };
  }
}

// Export singleton instance
export const englishScoringEngine = new EnglishScoringEngine();

// Export types for use in other modules
export type { QuestionResponse, ScoreBreakdown, GradeLevelResult, DetailedFeedback, ScoringResult };
