# Basic English Test Development Plan

## Overview
This plan provides step-by-step instructions for developing the Basic English practice and full test environments, including the question bank system. The plan is structured to allow parallel execution where possible, with clear dependencies marked.

## Phase 1: Database Schema & Models (Foundation)

### Step 1.1: Extend Database Schema for English Tests
**Dependencies:** None  
**Can run in parallel with:** Steps 1.2, 1.3

**AI Agent Prompt:**
```
Based on the existing Prisma schema in the Rubicon Testing Application, extend the database schema to support Basic English tests. Add the following:

1. Create a new table `english_questions` with fields:
   - id (uuid, primary key)
   - question_type (enum: VOCABULARY, SPELLING, GRAMMAR, PUNCTUATION, READING_COMPREHENSION)
   - grade_level (integer 5-12)
   - question_text (text)
   - correct_answer (text)
   - explanation (text)
   - difficulty_score (float 0-1)
   - times_used (integer, default 0)
   - times_correct (integer, default 0)
   - created_at, updated_at timestamps
   - is_active (boolean, default true)

2. Create table `english_wrong_answers`:
   - id (uuid, primary key)
   - question_id (foreign key to english_questions)
   - wrong_answer (text)
   - plausibility_score (float 0-1)
   - times_shown (integer, default 0)
   - times_selected (integer, default 0)

3. Create table `reading_passages`:
   - id (uuid, primary key)
   - title (varchar 255)
   - passage_text (text)
   - grade_level (integer 5-12)
   - word_count (integer)
   - complexity_score (float)
   - genre (enum: FICTION, NON_FICTION, POETRY, TECHNICAL)
   - times_used (integer, default 0)
   - is_active (boolean, default true)

4. Create table `passage_questions`:
   - id (uuid, primary key)
   - passage_id (foreign key to reading_passages)
   - question_id (foreign key to english_questions)
   - question_order (integer)

5. Create table `english_test_results`:
   - id (uuid, primary key)
   - test_id (foreign key to tests)
   - language_mastery_score (float)
   - reading_comprehension_score (float)
   - overall_score (float)
   - vocabulary_score (float)
   - spelling_score (float)
   - grammar_score (float)
   - punctuation_score (float)
   - grade_level_achieved (integer)

Add appropriate indexes and relationships. Generate the Prisma migration.
```

### Step 1.2: Create English Test Configuration Schema
**Dependencies:** None  
**Can run in parallel with:** Steps 1.1, 1.3

**AI Agent Prompt:**
```
Extend the app_settings table structure to support English test configuration. Add these settings:

1. Create table `english_test_config`:
   - id (uuid, primary key)
   - min_questions_language_mastery (integer, default 20)
   - min_questions_reading_comp (integer, default 15)
   - passages_per_test (integer, default 3)
   - questions_per_passage_min (integer, default 3)
   - questions_per_passage_max (integer, default 5)
   - adaptive_testing_enabled (boolean, default true)
   - show_explanations_practice (boolean, default true)
   - time_limit_minutes (integer, default 60)
   - question_bank_threshold (integer, default 100)
   - ai_generation_ratio (float, default 0.5)
   - include_none_of_above (boolean, default true)
   - none_of_above_frequency (float, default 0.2)

2. Add English test specific settings to practice_test_config table
```

### Step 1.3: Create Question Generation Tracking Schema
**Dependencies:** None  
**Can run in parallel with:** Steps 1.1, 1.2

**AI Agent Prompt:**
```
Create database schema for tracking AI question generation:

1. Create table `ai_generation_queue`:
   - id (uuid, primary key)
   - test_type (varchar)
   - question_type (varchar)
   - grade_level (integer)
   - status (enum: PENDING, PROCESSING, COMPLETED, FAILED)
   - priority (integer)
   - created_at, updated_at
   - completed_at
   - error_message (text, nullable)

2. Create table `question_bank_stats`:
   - id (uuid, primary key)
   - test_type (varchar)
   - question_type (varchar)
   - grade_level (integer)
   - total_questions (integer)
   - active_questions (integer)
   - last_generated_at (timestamp)
   - generation_needed (boolean)
```

## Phase 2: Core Models & Types (Foundation)

### Step 2.1: Create TypeScript Types for English Tests
**Dependencies:** Steps 1.1, 1.2, 1.3  
**Can run in parallel with:** Step 2.2

**AI Agent Prompt:**
```
Create TypeScript type definitions in lib/types.ts for the English test system:

1. Create interfaces for:
   - EnglishQuestion (matching database schema)
   - WrongAnswer (matching database schema)
   - ReadingPassage (matching database schema)
   - EnglishTestConfig
   - LanguageMasteryScore (vocabulary, spelling, grammar, punctuation subscores)
   - ReadingComprehensionScore
   - EnglishTestResult (combined scores and grade level)

2. Create enums for:
   - EnglishQuestionType (VOCABULARY, SPELLING, GRAMMAR, PUNCTUATION, READING_COMPREHENSION)
   - PassageGenre (FICTION, NON_FICTION, POETRY, TECHNICAL)
   - EnglishGradeLevel (GRADE_5 through GRADE_12)

3. Create types for:
   - QuestionWithAnswers (includes correct answer and array of wrong answers)
   - PassageWithQuestions (reading passage with associated questions)
   - EnglishTestSession (extends base TestSession with English-specific fields)

Include proper JSDoc documentation for all types.
```

### Step 2.2: Create English Test Service Layer
**Dependencies:** Steps 1.1, 1.2, 1.3  
**Can run in parallel with:** Step 2.1

**AI Agent Prompt:**
```
Create a new file lib/services/english-test-service.ts with the following functionality:

1. Create EnglishTestService class with methods:
   - generateAdaptiveQuestions(gradeLevel, previousResponses): Implements adaptive algorithm
   - selectQuestionsFromBank(criteria): Selects questions based on grade level and type
   - calculateLanguageMasteryScore(responses): Calculates subscore
   - calculateReadingComprehensionScore(responses): Calculates subscore
   - determineGradeLevel(scores): Maps scores to grade level 5-12
   - getQuestionBankStats(): Returns current question bank statistics

2. Implement adaptive testing logic:
   - Start at user-specified grade level or grade 8 default
   - Adjust difficulty based on performance
   - Minimum 3 questions per grade level before moving
   - Maximum 2 grade levels jump at once

3. Implement scoring algorithm:
   - Weight different question types appropriately
   - Calculate percentile rankings
   - Map to grade level equivalencies

Follow existing service patterns in the codebase.
```

## Phase 3: Question Bank System

### Step 3.1: Create Question Bank Manager
**Dependencies:** Steps 2.1, 2.2  
**Can run in parallel with:** Step 3.2

**AI Agent Prompt:**
```
Create lib/services/question-bank-service.ts with comprehensive question bank management:

1. Implement QuestionBankService class with methods:
   - addQuestion(question, wrongAnswers): Adds new question with validation
   - getQuestionWithWrongAnswers(questionId, count): Returns question with specified number of wrong answers
   - buildMultipleChoiceOptions(question, wrongAnswerCount): Creates answer set with "none of the above" logic
   - updateQuestionStatistics(questionId, wasCorrect): Updates usage stats
   - getQuestionBankUtilization(testType): Returns percentage to use from bank (0-90%)
   - pruneInactiveQuestions(): Removes poorly performing questions

2. Implement "none of the above" logic:
   - Include as 5th option based on configuration frequency
   - When included, 20% chance correct answer is excluded
   - Ensure at least 2x wrong answers available

3. Implement question selection algorithm:
   - Prefer questions not recently used
   - Balance question types
   - Consider difficulty distribution
```

### Step 3.2: Create AI Question Generator Service
**Dependencies:** Steps 2.1, 2.2  
**Can run in parallel with:** Step 3.1

**AI Agent Prompt:**
```
Create lib/services/english-question-generator.ts for AI-powered question generation:

1. Implement EnglishQuestionGenerator class:
   - generateVocabularyQuestion(gradeLevel): Creates vocabulary questions
   - generateSpellingQuestion(gradeLevel): Creates spelling questions
   - generateGrammarQuestion(gradeLevel): Creates grammar questions
   - generatePunctuationQuestion(gradeLevel): Creates punctuation questions
   - generateReadingPassage(gradeLevel, genre): Creates reading passages
   - generateComprehensionQuestions(passage, count): Creates questions for passage

2. For each question type, generate:
   - Question text
   - Correct answer
   - At least 6 plausible wrong answers
   - Explanation for correct answer
   - Difficulty score

3. Implement passage generation:
   - Age-appropriate content
   - Specified word count ranges per grade
   - Various genres
   - Flesch-Kincaid scoring

4. Include prompt templates for each question type following Common Core standards for grades 5-12.

Use the existing AI service integration pattern from the codebase.
```

### Step 3.3: Create Question Bank Population Job
**Dependencies:** Steps 3.1, 3.2  
**Can run in parallel with:** None

**AI Agent Prompt:**
```
Create lib/jobs/populate-english-questions.ts as a background job:

1. Implement question bank population logic:
   - Check current question bank stats
   - Identify gaps by grade level and question type
   - Generate questions to reach minimum thresholds
   - Batch process to avoid API limits

2. Create balanced distribution:
   - 25% vocabulary questions
   - 20% spelling questions  
   - 25% grammar questions
   - 10% punctuation questions
   - 20% reading comprehension

3. For each grade level (5-12):
   - Generate at least 25 questions per type
   - Generate at least 10 reading passages
   - Generate 3-5 questions per passage

4. Implement job scheduling:
   - Run daily at low-traffic times
   - Respect API rate limits
   - Log generation statistics
```

## Phase 4: UI Components

### Step 4.1: Create English Test Components
**Dependencies:** Steps 2.1, 2.2  
**Can run in parallel with:** Step 4.2

**AI Agent Prompt:**
```
Create React components for English tests in components/english-test/:

1. Create EnglishQuestionDisplay component:
   - Renders different question types appropriately
   - Multiple choice with radio buttons
   - Text input for spelling questions
   - Syntax highlighting for grammar/punctuation

2. Create ReadingPassageDisplay component:
   - Scrollable passage view
   - Reading timer
   - Hide/show passage toggle for questions
   - Progress indicator for passage questions

3. Create EnglishTestProgress component:
   - Shows language mastery vs reading comprehension progress
   - Current grade level indicator
   - Question type breakdown
   - Time remaining

4. Create EnglishResultsDisplay component:
   - Dual score display (language/reading)
   - Grade level achievement badge
   - Subscore breakdown chart
   - Areas for improvement

Follow existing component patterns using Radix UI and Tailwind CSS.
```

### Step 4.2: Create Admin Settings Tab for English Tests
**Dependencies:** Steps 1.2, 2.1  
**Can run in parallel with:** Step 4.1

**AI Agent Prompt:**
```
Extend the admin settings page (app/admin/settings/page.tsx) with an English Tests tab:

1. Add new tab "English Tests" to existing tabs component

2. Create settings form with sections:
   - Question Counts (min questions per category)
   - Reading Passages (passages per test, questions per passage)
   - Adaptive Testing (enable/disable, sensitivity settings)
   - Time Limits (overall and per section)
   - Question Bank (threshold, AI generation ratio)
   - "None of the Above" Settings (frequency, inclusion rules)

3. Implement form validation:
   - Minimum values enforcement
   - Logical relationships (e.g., min < max)
   - Save bank statistics display:
   - Current questions by type and grade
   - Generation queue status
   - Last generation timestamp

Use existing settings patterns and form components.
```

## Phase 5: Test Flow Implementation

### Step 5.1: Implement English Test Session Management
**Dependencies:** Steps 3.1, 3.2, 4.1  
**Can run in parallel with:** Step 5.2

**AI Agent Prompt:**
```
Create app/api/tests/english/route.ts and related endpoints:

1. Implement test session creation:
   - Initialize adaptive testing parameters
   - Select initial questions based on starting grade
   - Mix questions from bank and AI generation per configuration
   - Create passage-question groupings

2. Implement answer submission handling:
   - Validate answer format by question type
   - Update adaptive algorithm state
   - Track time per question
   - Prevent passage access during questions

3. Implement session state management:
   - Store current grade level
   - Track questions answered by type
   - Manage passage viewing state
   - Calculate running scores

4. Implement test completion:
   - Calculate final scores
   - Determine grade level achievement
   - Generate detailed results
   - Update user test history
```

### Step 5.2: Implement Practice Test Logic
**Dependencies:** Steps 3.1, 3.2, 4.1  
**Can run in parallel with:** Step 5.1

**AI Agent Prompt:**
```
Create practice test implementation in app/api/tests/english/practice/route.ts:

1. Implement practice test generation:
   - 50% of full test question count
   - Fixed grade level (no adaptation)
   - Mix of all question types
   - 1-2 reading passages only

2. Implement no-storage practice mode:
   - Generate questions on demand
   - Don't save responses
   - Provide immediate feedback
   - Show explanations after each question

3. Create practice test UI flow:
   - Simplified progress tracking
   - Instant feedback mode
   - Skip test results storage
   - Return to dashboard after completion

Follow the pattern: practice tests fully mimic actual tests but shorter and no data persistence.
```

### Step 5.3: Implement Admin Impersonation Restrictions
**Dependencies:** Step 5.1  
**Can run in parallel with:** None

**AI Agent Prompt:**
```
Update the test-taking flow to enforce admin restrictions:

1. In app/api/tests/start/route.ts:
   - Check if current session is admin (not impersonating)
   - Return error "Administrators cannot take tests directly"
   - Check for impersonation token in session

2. Update components/test-interface:
   - Show "Testing as: [Username]" banner when impersonating
   - Add "Return to Admin" button in header
   - Maintain impersonation state through test

3. In lib/auth.ts:
   - Add isImpersonating() helper
   - Add returnToAdmin() function
   - Preserve test progress when switching

4. Update middleware.ts:
   - Allow test routes only for users or impersonating admins
   - Maintain impersonation session data
```

## Phase 6: Scoring & Results

### Step 6.1: Implement English Scoring Engine
**Dependencies:** Steps 2.2, 5.1  
**Can run in parallel with:** Step 6.2

**AI Agent Prompt:**
```
Create lib/scoring/english-scoring-engine.ts:

1. Implement comprehensive scoring:
   - Calculate language mastery subscore (vocabulary, spelling, grammar, punctuation)
   - Calculate reading comprehension score
   - Weight subscores for overall score
   - Map to grade level (5-12)

2. Scoring algorithm:
   - Language mastery: 60% weight
   - Reading comprehension: 40% weight
   - Adaptive bonus: +5% for completing higher grade levels
   - Time bonus: up to +3% for efficient completion

3. Grade level determination:
   - 90%+ at grade level = that grade proficiency
   - 80-89% = 0.5 grade below
   - 70-79% = 1 grade below
   - Map to Common Core standards

4. Generate detailed feedback:
   - Strength areas
   - Improvement areas
   - Recommended study topics
```

### Step 6.2: Create English Test Results API
**Dependencies:** Steps 2.2, 5.1  
**Can run in parallel with:** Step 6.1

**AI Agent Prompt:**
```
Create app/api/tests/english/results/[testId]/route.ts:

1. Implement results calculation endpoint:
   - Retrieve completed test data
   - Calculate all scores using scoring engine
   - Store in english_test_results table
   - Generate grade level certificate eligibility

2. Return comprehensive results:
   - Overall grade level achieved
   - Language mastery breakdown
   - Reading comprehension score
   - Question-level analysis
   - Time performance metrics

3. Implement results visualization data:
   - Score comparisons to averages
   - Progress over multiple attempts
   - Subscore radar chart data
   - Percentile rankings

4. Certificate generation eligibility:
   - Minimum 70% overall score
   - Completed full test (not practice)
   - Include grade level achieved
```

## Phase 7: Integration & Testing

### Step 7.1: Update Dashboard Integration
**Dependencies:** All previous steps  
**Can run in parallel with:** Step 7.2

**AI Agent Prompt:**
```
Update user and admin dashboards to include English tests:

1. In app/dashboard/page.tsx:
   - Add English test card showing access status
   - Display last English test result if exists
   - Show grade level achievement badge
   - Add "Start English Test" button

2. In app/admin/page.tsx:
   - Add English test statistics widget
   - Show question bank health metrics
   - Display recent English test completions
   - Add quick link to English test settings

3. Update test selection modal:
   - Add English test option
   - Show estimated time (60 minutes)
   - Display grade level testing note
   - Indicate practice availability

4. Update test history displays:
   - Show dual scores for English tests
   - Display grade level achieved
   - Add filtering by test type
```

### Step 7.2: Create English Test Unit Tests
**Dependencies:** All previous steps  
**Can run in parallel with:** Step 7.1

**AI Agent Prompt:**
```
Create comprehensive test suite in __tests__/english/:

1. Question bank tests:
   - Question selection algorithm
   - Wrong answer distribution
   - "None of the above" logic
   - Bank utilization calculation

2. Adaptive algorithm tests:
   - Grade level progression
   - Difficulty adjustment
   - Edge cases (min/max grades)

3. Scoring engine tests:
   - Subscore calculations
   - Grade level mapping
   - Edge cases and bounds

4. API endpoint tests:
   - Session creation
   - Answer submission
   - Results calculation
   - Admin restrictions

5. Component tests:
   - Question display rendering
   - Passage display functionality
   - Results visualization

Follow existing test patterns using Jest and React Testing Library.
```

## Phase 8: Documentation & Deployment

### Step 8.1: Create English Test Documentation
**Dependencies:** All previous steps  
**Can run in parallel with:** Step 8.2

**AI Agent Prompt:**
```
Create documentation in app/docs/18-english-test-system.md:

1. Document the English test system:
   - Test structure and components
   - Scoring methodology
   - Grade level mapping
   - Question types and examples

2. Document question bank:
   - AI generation process
   - Bank management
   - Selection algorithms
   - Quality control

3. Admin guide:
   - Settings configuration
   - Question bank monitoring
   - Result interpretation
   - Troubleshooting

4. User guide:
   - Test preparation tips
   - Understanding scores
   - Practice vs full tests
   - Certificate requirements

Follow the documentation style of existing docs in app/docs/.
```

### Step 8.2: Create Database Migrations & Deployment Scripts
**Dependencies:** All previous steps  
**Can run in parallel with:** Step 8.1

**AI Agent Prompt:**
```
Create deployment assets:

1. Generate Prisma migrations:
   - All new tables from Phase 1
   - Seed data for initial settings
   - Sample questions for each grade level

2. Create deployment checklist:
   - Environment variables needed
   - AI service configuration
   - Initial question generation
   - Performance considerations

3. Create monitoring alerts:
   - Question bank health
   - AI generation failures  
   - Unusual scoring patterns
   - Test completion rates

4. Update deployment documentation:
   - New endpoints
   - Database changes
   - Configuration requirements
```

## Execution Order & Dependencies

### Parallel Execution Groups:

**Group 1 (Foundation - Can all run in parallel):**
- Step 1.1: Database Schema
- Step 1.2: Configuration Schema  
- Step 1.3: Generation Tracking Schema

**Group 2 (Models - Depends on Group 1):**
- Step 2.1: TypeScript Types
- Step 2.2: Service Layer

**Group 3 (Services - Depends on Group 2):**
- Step 3.1: Question Bank Manager
- Step 3.2: AI Generator Service

**Group 4 (Depends on Group 3):**
- Step 3.3: Population Job

**Group 5 (UI - Depends on Groups 2):**
- Step 4.1: Test Components
- Step 4.2: Admin Settings

**Group 6 (Implementation - Depends on Groups 3, 4):**
- Step 5.1: Session Management
- Step 5.2: Practice Tests

**Group 7 (Depends on Group 6):**
- Step 5.3: Admin Restrictions
- Step 6.1: Scoring Engine
- Step 6.2: Results API

**Group 8 (Final - Depends on all):**
- Step 7.1: Dashboard Integration
- Step 7.2: Unit Tests
- Step 8.1: Documentation
- Step 8.2: Deployment

This plan ensures efficient parallel development while maintaining proper dependencies.

## Key Requirements Summary

### Testing Functionality
- No admin account may take practice or actual tests
- Admins can impersonate users and take tests with their permissions
- Easy return to admin account when impersonating
- No pausing or storage for practice tests
- Practice tests are shorter simulated versions

### Question Bank System
- AI generates questions and saves to bank
- Dynamic usage: 0% (empty bank) to 90% (100+ questions) from bank
- Remaining percentage generated fresh by AI
- Multiple choice includes "none of the above" option
- Store 2x wrong answers needed for variety

### Basic English Test Structure
- Tests English proficiency from 5th to 12th grade
- Two main components:
  - Language Mastery (vocabulary, spelling, grammar, punctuation)
  - Reading Comprehension
- Separate scores for each component plus combined score
- Reading passages with 3-5 questions each
- Questions shown without passage visible
- Adaptive testing adjusts difficulty based on performance

### Admin Settings
- New "English Tests" tab in App Settings
- Configure question counts
- Passage settings
- Time limits
- Question bank thresholds
- "None of the above" frequency