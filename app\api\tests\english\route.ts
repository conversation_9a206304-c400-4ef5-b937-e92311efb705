import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import EnglishTestService from '@/lib/services/english-test-service';
import { createEnglishQuestionGenerator } from '@/lib/services/english-question-generator';

interface EnglishTestSession {
  testId: string;
  userId: string;
  currentGradeLevel: number;
  startingGradeLevel: number;
  questionsAnswered: {
    questionId: string;
    isCorrect: boolean;
    gradeLevel: number;
    questionType: string;
    timeSpent: number;
    answer: string;
  }[];
  currentPassageId?: string;
  passageQuestions: string[];
  adaptiveState: {
    correctByGrade: Record<number, number>;
    totalByGrade: Record<number, number>;
    consecutiveCorrect: number;
    consecutiveIncorrect: number;
  };
  runningScores: {
    languageMastery: number;
    readingComprehension: number;
    overall: number;
  };
  isPassageViewingLocked: boolean;
  startedAt: Date;
  lastActivityAt: Date;
}

// POST /api/tests/english - Create new English test session
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { isPractice = false, startingGradeLevel = 8 } = body;

    // Validate user has access to English tests
    const userAccess = await prisma.user_test_access.findFirst({
      where: {
        userId: session.user.id,
        testTypeId: 'basic-english',
        isActive: true,
      },
    });

    if (!userAccess && !isPractice) {
      return NextResponse.json({ error: 'No access to English tests' }, { status: 403 });
    }

    // Get English test configuration
    const config = await prisma.english_test_config.findFirst();
    if (!config) {
      return NextResponse.json({ error: 'English test configuration not found' }, { status: 500 });
    }

    // Create test session in database
    const testType = await prisma.test_types.findUnique({
      where: { name: 'basic-english' }
    });

    if (!testType) {
      return NextResponse.json({ error: 'Test type not found' }, { status: 500 });
    }

    const test = await prisma.tests.create({
      data: {
        userId: session.user.id,
        testTypeId: testType.id,
        status: 'STARTED',
        isPractice,
        timeLimit: config.time_limit_minutes,
        questionsOrder: [],
        answers: {},
        additionalData: {
          englishTestSession: {
            currentGradeLevel: startingGradeLevel,
            startingGradeLevel: startingGradeLevel,
            questionsAnswered: [],
            adaptiveState: {
              correctByGrade: {},
              totalByGrade: {},
              consecutiveCorrect: 0,
              consecutiveIncorrect: 0,
            },
            runningScores: {
              languageMastery: 0,
              readingComprehension: 0,
              overall: 0,
            },
            isPassageViewingLocked: false,
            passageQuestions: [],
          }
        }
      },
    });

    // Generate initial questions based on starting grade level
    const initialQuestions = await EnglishTestService.generateAdaptiveQuestions(
      startingGradeLevel,
      []
    );

    // Mix in AI-generated questions if ratio is set
    let mixedQuestions = initialQuestions;
    if (config.ai_generation_ratio > 0) {
      const aiQuestionCount = Math.ceil(initialQuestions.length * config.ai_generation_ratio);
      try {
        const generator = await createEnglishQuestionGenerator();
        const aiQuestions = [];
        
        for (let i = 0; i < aiQuestionCount; i++) {
          const aiQuestion = await generator.generateVocabularyQuestion(startingGradeLevel);
          // Convert AI question to database format and add to bank
          const dbQuestion = await prisma.english_questions.create({
            data: {
              question_type: 'VOCABULARY',
              grade_level: startingGradeLevel,
              question_text: aiQuestion.questionText,
              correct_answer: aiQuestion.answer,
              explanation: aiQuestion.explanation,
              difficulty_score: aiQuestion.difficultyScore,
              created_at: new Date(),
              updated_at: new Date(),
              is_active: true,
            }
          });
          aiQuestions.push(dbQuestion);
        }
        
        // Mix AI questions with bank questions
        mixedQuestions = [...initialQuestions.slice(0, -aiQuestionCount), ...aiQuestions];
      } catch (error) {
        console.warn('Failed to generate AI questions, using bank questions only:', error);
      }
    }

    // Update test with question order
    await prisma.tests.update({
      where: { id: test.id },
      data: {
        questionsOrder: mixedQuestions.map(q => q.id),
        currentQuestionId: mixedQuestions[0]?.id || null,
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        testId: test.id,
        testType: 'basic-english',
        isPractice,
        startedAt: test.startedAt,
        timeLimit: config.time_limit_minutes,
        configuration: {
          adaptiveTestingEnabled: config.adaptive_testing_enabled,
          showExplanations: isPractice && config.show_explanations_practice,
          includeNoneOfAbove: config.include_none_of_above,
        },
        currentGradeLevel: startingGradeLevel,
        questionsGenerated: mixedQuestions.length,
      }
    });

  } catch (error) {
    console.error('Error creating English test session:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET /api/tests/english - Get current question or test state
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const testId = searchParams.get('testId');

    if (!testId) {
      return NextResponse.json({ error: 'Test ID is required' }, { status: 400 });
    }

    // Get test session
    const test = await prisma.tests.findFirst({
      where: {
        id: testId,
        userId: session.user.id,
        status: { in: ['STARTED', 'PAUSED'] },
      },
      include: {
        test_types: true,
      },
    });

    if (!test) {
      return NextResponse.json({ error: 'Test session not found' }, { status: 404 });
    }

    const sessionData = test.additionalData as { englishTestSession: EnglishTestSession };
    const englishSession = sessionData?.englishTestSession;

    if (!englishSession) {
      return NextResponse.json({ error: 'Invalid test session data' }, { status: 400 });
    }

    // Get current question
    const currentQuestionId = test.currentQuestionId;
    let currentQuestion = null;

    if (currentQuestionId) {
      currentQuestion = await prisma.english_questions.findUnique({
        where: { id: currentQuestionId },
        include: {
          wrong_answers: true,
        },
      });

      // If this is a reading comprehension question, get the passage
      if (currentQuestion?.question_type === 'READING_COMPREHENSION') {
        const passageQuestion = await prisma.passage_questions.findFirst({
          where: { question_id: currentQuestionId },
          include: {
            passage: true,
          },
        });

        if (passageQuestion) {
          currentQuestion = {
            ...currentQuestion,
            passage: passageQuestion.passage,
          };
        }
      }
    }

    // Calculate progress
    const totalQuestionsNeeded = Math.max(
      20, // minimum from config
      englishSession.questionsAnswered.length + 10
    );

    return NextResponse.json({
      success: true,
      data: {
        testId: test.id,
        status: test.status,
        currentGradeLevel: englishSession.currentGradeLevel,
        progress: {
          questionsAnswered: englishSession.questionsAnswered.length,
          estimatedTotal: totalQuestionsNeeded,
          languageMasteryCount: englishSession.questionsAnswered.filter(
            q => q.questionType !== 'READING_COMPREHENSION'
          ).length,
          readingComprehensionCount: englishSession.questionsAnswered.filter(
            q => q.questionType === 'READING_COMPREHENSION'
          ).length,
        },
        currentQuestion: currentQuestion ? {
          id: currentQuestion.id,
          questionText: currentQuestion.question_text,
          questionType: currentQuestion.question_type,
          gradeLevel: currentQuestion.grade_level,
          options: currentQuestion.wrong_answers?.map(wa => wa.wrong_answer) || [],
          passage: currentQuestion.passage || null,
          allowPassageReference: !englishSession.isPassageViewingLocked,
        } : null,
        runningScores: englishSession.runningScores,
        timeRemaining: test.timeLimit ? 
          Math.max(0, test.timeLimit * 60 - Math.floor((Date.now() - test.startedAt.getTime()) / 1000)) : 
          null,
      }
    });

  } catch (error) {
    console.error('Error getting English test state:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
