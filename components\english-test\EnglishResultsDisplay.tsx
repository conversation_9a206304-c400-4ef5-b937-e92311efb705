import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, LineChart, Line } from 'recharts';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>L<PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Award, Clock, TrendingUp, Target, Download } from 'lucide-react';

interface ComprehensiveResults {
  test_id: string;
  user_id: string;
  overall_grade_level: string;
  overall_score: number;
  language_mastery: {
    vocabulary: number;
    grammar: number;
    spelling: number;
    overall: number;
  };
  reading_comprehension: {
    literal_comprehension: number;
    inferential_comprehension: number;
    evaluative_comprehension: number;
    overall: number;
  };
  time_metrics: {
    total_duration: number;
    average_time_per_question: number;
    time_efficiency_score: number;
    questions_rushed: number;
    questions_overtime: number;
  };
  visualization_data: {
    radar_chart: {
      categories: string[];
      user_scores: number[];
      grade_averages: number[];
    };
    progress_over_time: {
      date: string;
      overall_score: number;
      grade_level: string;
    }[];
    subscore_comparisons: {
      [category: string]: {
        user_score: number;
        grade_average: number;
        national_average: number;
        percentile_rank: number;
      };
    };
  };
  certificate_eligibility: {
    is_eligible: boolean;
    grade_level_achieved: string;
    overall_score: number;
    minimum_score_met: boolean;
    is_full_test: boolean;
    certificate_type: string | null;
    issued_date: string | null;
  };
  completion_date: string;
}

interface EnglishResultsDisplayProps {
  testId: string;
  onDownloadCertificate?: () => void;
  onRetryTest?: () => void;
}

const EnglishResultsDisplay: React.FC<EnglishResultsDisplayProps> = ({ 
  testId, 
  onDownloadCertificate, 
  onRetryTest 
}) => {
  const [results, setResults] = useState<ComprehensiveResults | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchResults();
  }, [testId]);

  const fetchResults = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/tests/english/results/${testId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch test results');
      }
      
      const data = await response.json();
      setResults(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getScoreColor = (score: number) => {
    if (score >= 85) return 'text-green-600';
    if (score >= 70) return 'text-blue-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getBadgeVariant = (score: number) => {
    if (score >= 85) return 'default';
    if (score >= 70) return 'secondary';
    return 'outline';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Calculating your results...</p>
        </div>
      </div>
    );
  }

  if (error || !results) {
    return (
      <Card className="m-4">
        <CardContent className="p-6 text-center">
          <p className="text-red-600 mb-4">{error || 'Failed to load results'}</p>
          <Button onClick={fetchResults} variant="outline">
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Prepare radar chart data
  const radarData = results.visualization_data.radar_chart.categories.map((category, index) => ({
    category: category.replace(' Reading', '').replace('_', ' '),
    userScore: results.visualization_data.radar_chart.user_scores[index],
    gradeAverage: results.visualization_data.radar_chart.grade_averages[index]
  }));

  // Prepare comparison data
  const comparisonData = Object.entries(results.visualization_data.subscore_comparisons).map(([name, data]) => ({
    name: name.replace('_', ' '),
    'Your Score': data.user_score,
    'Grade Average': data.grade_average,
    'National Average': data.national_average
  }));

  return (
    <div className="max-w-6xl mx-auto p-4 space-y-6">
      {/* Header with Overall Results */}
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2">
            <Award className="h-6 w-6" />
            English Test Results
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div>
              <p className="text-sm text-gray-600">Overall Score</p>
              <p className={`text-3xl font-bold ${getScoreColor(results.overall_score)}`}>
                {results.overall_score}%
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Grade Level Achieved</p>
              <Badge variant={getBadgeVariant(results.overall_score)} className="text-lg px-3 py-1">
                {results.overall_grade_level.replace('-', ' ').toUpperCase()}
              </Badge>
            </div>
            <div>
              <p className="text-sm text-gray-600">Percentile Rank</p>
              <p className="text-3xl font-bold text-blue-600">
                {results.visualization_data.subscore_comparisons['Language Mastery']?.percentile_rank || 0}
              </p>
              <p className="text-xs text-gray-500">th percentile</p>
            </div>
          </div>
          
          {/* Certificate Eligibility */}
          {results.certificate_eligibility.is_eligible && (
            <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg text-center">
              <Award className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <p className="text-green-800 font-semibold">Certificate Eligible!</p>
              <p className="text-green-700 text-sm mb-3">
                {results.certificate_eligibility.certificate_type}
              </p>
              <Button onClick={onDownloadCertificate} className="bg-green-600 hover:bg-green-700">
                <Download className="h-4 w-4 mr-2" />
                Download Certificate
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Detailed Results Tabs */}
      <Tabs defaultValue="scores" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="scores">Scores</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="analysis">Analysis</TabsTrigger>
          <TabsTrigger value="progress">Progress</TabsTrigger>
        </TabsList>
        
        <TabsContent value="scores" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Language Mastery Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Language Mastery</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span>Vocabulary</span>
                    <span className={`font-semibold ${getScoreColor(results.language_mastery.vocabulary)}`}>
                      {results.language_mastery.vocabulary}%
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Grammar</span>
                    <span className={`font-semibold ${getScoreColor(results.language_mastery.grammar)}`}>
                      {results.language_mastery.grammar}%
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Spelling</span>
                    <span className={`font-semibold ${getScoreColor(results.language_mastery.spelling)}`}>
                      {results.language_mastery.spelling}%
                    </span>
                  </div>
                  <hr />
                  <div className="flex justify-between items-center font-semibold">
                    <span>Overall</span>
                    <span className={getScoreColor(results.language_mastery.overall)}>
                      {results.language_mastery.overall}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Reading Comprehension Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Reading Comprehension</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span>Literal</span>
                    <span className={`font-semibold ${getScoreColor(results.reading_comprehension.literal_comprehension)}`}>
                      {results.reading_comprehension.literal_comprehension}%
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Inferential</span>
                    <span className={`font-semibold ${getScoreColor(results.reading_comprehension.inferential_comprehension)}`}>
                      {results.reading_comprehension.inferential_comprehension}%
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Evaluative</span>
                    <span className={`font-semibold ${getScoreColor(results.reading_comprehension.evaluative_comprehension)}`}>
                      {results.reading_comprehension.evaluative_comprehension}%
                    </span>
                  </div>
                  <hr />
                  <div className="flex justify-between items-center font-semibold">
                    <span>Overall</span>
                    <span className={getScoreColor(results.reading_comprehension.overall)}>
                      {results.reading_comprehension.overall}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Score Comparisons */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Score Comparisons</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={comparisonData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="Your Score" fill="#3b82f6" />
                  <Bar dataKey="Grade Average" fill="#10b981" />
                  <Bar dataKey="National Average" fill="#f59e0b" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Time Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Time Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Total Duration</span>
                    <span>{formatTime(results.time_metrics.total_duration)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Avg. Time per Question</span>
                    <span>{formatTime(Math.round(results.time_metrics.average_time_per_question))}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Time Efficiency Score</span>
                    <span className={getScoreColor(results.time_metrics.time_efficiency_score)}>
                      {results.time_metrics.time_efficiency_score}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Questions Rushed</span>
                    <span className="text-yellow-600">{results.time_metrics.questions_rushed}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Questions Overtime</span>
                    <span className="text-red-600">{results.time_metrics.questions_overtime}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Radar Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Skill Profile</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RadarChart data={radarData}>
                    <PolarGrid />
                    <PolarAngleAxis dataKey="category" className="text-xs" />
                    <PolarRadiusAxis angle={90} domain={[0, 100]} className="text-xs" />
                    <Radar name="Your Score" dataKey="userScore" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.3} />
                    <Radar name="Grade Average" dataKey="gradeAverage" stroke="#10b981" fill="transparent" strokeDasharray="5 5" />
                    <Legend />
                  </RadarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analysis" className="space-y-4">
          {/* Areas for Improvement */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Target className="h-5 w-5" />
                Areas for Improvement
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(results.visualization_data.subscore_comparisons)
                  .filter(([_, data]) => data.user_score < data.grade_average)
                  .map(([category, data]) => (
                    <div key={category} className="p-3 border rounded-lg">
                      <h4 className="font-semibold text-sm">{category}</h4>
                      <p className="text-xs text-gray-600 mt-1">
                        Your score: {data.user_score}% (Grade avg: {data.grade_average}%)
                      </p>
                      <p className="text-xs text-blue-600 mt-1">
                        Focus on improving this area to reach grade level expectations.
                      </p>
                    </div>
                  ))
                }
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="progress" className="space-y-4">
          {/* Progress Over Time */}
          {results.visualization_data.progress_over_time.length > 1 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Progress Over Time
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={results.visualization_data.progress_over_time}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis domain={[0, 100]} />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="overall_score" stroke="#3b82f6" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <div className="flex justify-center gap-4">
        <Button onClick={onRetryTest} variant="outline">
          Take Test Again
        </Button>
        <Button onClick={() => window.print()} variant="outline">
          Print Results
        </Button>
      </div>
    </div>
  );
};

export default EnglishResultsDisplay;

