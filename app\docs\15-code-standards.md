# Code Standards & Style Guide

## Overview

The Rubicon Programs Testing Application follows strict coding standards to ensure maintainability, readability, and consistency across the codebase. This document outlines the conventions, patterns, and practices used throughout the application.

## General Principles

### Code Philosophy
- **Clarity over Cleverness**: Write code that is easy to understand
- **Consistency**: Follow established patterns and conventions
- **Type Safety**: Leverage TypeScript's type system for robustness
- **Performance**: Optimize for user experience and scalability
- **Accessibility**: Ensure inclusive design and WCAG compliance
- **Security**: Follow secure coding practices

### Development Practices
- **Test-Driven Development**: Write tests before implementation
- **Progressive Enhancement**: Build from basic functionality upward
- **Mobile-First Design**: Start with mobile layouts and scale up
- **Semantic HTML**: Use meaningful HTML elements
- **Component Composition**: Favor composition over inheritance

## TypeScript Standards

### Type Definitions
```typescript
// Use explicit types for public APIs
export interface UserProfile {
  readonly id: string;
  readonly email: string;
  firstName: string;
  lastName: string;
  dateOfBirth: Date;
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

// Use enums for discrete values
export enum TestStatus {
  NOT_STARTED = 'NOT_STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  PAUSED = 'PAUSED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

// Use union types for specific string literals
export type AccessType = 'NONE' | 'PRACTICE_ONLY' | 'ONE_TIME' | 'UNLIMITED';

// Use generic types for reusable components
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, unknown>;
  };
  pagination?: PaginationInfo;
}
```

### Function Declarations
```typescript
// Use function declarations for top-level functions
export function calculateTypingScore(session: TypingSession): TypingResult {
  // Implementation
}

// Use arrow functions for callbacks and inline functions
const userIds = users.map(user => user.id);

// Use async/await instead of Promises
export async function createUser(userData: CreateUserData): Promise<User> {
  try {
    const hashedPassword = await hashPassword(userData.password);
    const user = await prisma.users.create({
      data: {
        ...userData,
        password: hashedPassword
      }
    });
    return user;
  } catch (error) {
    throw new UserCreationError('Failed to create user', { cause: error });
  }
}
```

### Error Handling
```typescript
// Create custom error classes
export class ValidationError extends Error {
  constructor(
    message: string,
    public readonly field: string,
    public readonly value: unknown
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class DatabaseError extends Error {
  constructor(
    message: string,
    public readonly operation: string,
    public readonly table?: string
  ) {
    super(message);
    this.name = 'DatabaseError';
  }
}

// Use Result pattern for operations that may fail
export type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E };

export async function validateUser(userData: UserData): Promise<Result<User, ValidationError[]>> {
  const errors: ValidationError[] = [];
  
  if (!userData.email) {
    errors.push(new ValidationError('Email is required', 'email', userData.email));
  }
  
  if (errors.length > 0) {
    return { success: false, error: errors };
  }
  
  return { success: true, data: userData as User };
}
```

## React Component Standards

### Component Structure
```typescript
// Component file structure template
import React from 'react';
import { useState, useEffect } from 'react';
import { NextPage } from 'next';

// External libraries
import { z } from 'zod';

// Internal imports - organized by category
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

// Types and interfaces
interface TestResultsProps {
  testId: string;
  userId: string;
  showDetails?: boolean;
  className?: string;
}

interface TestResultsState {
  loading: boolean;
  results: TestResults | null;
  error: string | null;
}

// Main component
export function TestResults({ 
  testId, 
  userId, 
  showDetails = false, 
  className 
}: TestResultsProps) {
  // State management
  const [state, setState] = useState<TestResultsState>({
    loading: true,
    results: null,
    error: null
  });
  
  // Hooks
  const { toast } = useToast();
  
  // Effects
  useEffect(() => {
    fetchTestResults();
  }, [testId]);
  
  // Event handlers
  const handleDownloadCertificate = async () => {
    try {
      // Implementation
    } catch (error) {
      toast({
        title: "Download Failed",
        description: "Unable to download certificate. Please try again.",
        variant: "destructive"
      });
    }
  };
  
  // Helper functions
  const fetchTestResults = async () => {
    // Implementation
  };
  
  // Early returns for loading and error states
  if (state.loading) {
    return <div>Loading test results...</div>;
  }
  
  if (state.error) {
    return <div>Error: {state.error}</div>;
  }
  
  // Main render
  return (
    <Card className={cn("test-results", className)}>
      <CardHeader>
        <CardTitle>Test Results</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Component content */}
      </CardContent>
    </Card>
  );
}

// Display name for debugging
TestResults.displayName = 'TestResults';

// Default export
export default TestResults;
```

### Component Patterns

#### Compound Components
```typescript
// Parent component with context
const TestContext = createContext<TestContextType | null>(null);

export function Test({ children }: { children: React.ReactNode }) {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  
  const value = {
    currentQuestion,
    setCurrentQuestion,
    // other context values
  };
  
  return (
    <TestContext.Provider value={value}>
      <div className="test-container">
        {children}
      </div>
    </TestContext.Provider>
  );
}

// Child components
export function TestQuestion({ children }: { children: React.ReactNode }) {
  const context = useContext(TestContext);
  if (!context) throw new Error('TestQuestion must be used within Test');
  
  return (
    <div className="test-question">
      {children}
    </div>
  );
}

export function TestProgress() {
  const { currentQuestion, totalQuestions } = useContext(TestContext)!;
  
  return (
    <div className="test-progress">
      Question {currentQuestion + 1} of {totalQuestions}
    </div>
  );
}

// Usage
<Test>
  <TestProgress />
  <TestQuestion>
    <p>What is 2 + 2?</p>
  </TestQuestion>
</Test>
```

#### Custom Hooks Pattern
```typescript
// Custom hook for test state management
export function useTestSession(testId: string) {
  const [session, setSession] = useState<TestSession | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const startTest = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/tests/${testId}/start`, {
        method: 'POST'
      });
      
      if (!response.ok) {
        throw new Error('Failed to start test');
      }
      
      const session = await response.json();
      setSession(session);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [testId]);
  
  const submitAnswer = useCallback(async (answer: string) => {
    if (!session) return;
    
    try {
      const response = await fetch(`/api/tests/${session.id}/answer`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ answer })
      });
      
      return await response.json();
    } catch (err) {
      setError('Failed to submit answer');
      return null;
    }
  }, [session]);
  
  return {
    session,
    loading,
    error,
    startTest,
    submitAnswer
  };
}

// Usage in component
export function TestInterface({ testId }: { testId: string }) {
  const { session, loading, error, startTest, submitAnswer } = useTestSession(testId);
  
  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!session) {
    return <Button onClick={startTest}>Start Test</Button>;
  }
  
  return <div>Test in progress...</div>;
}
```

## API Route Standards

### Route Structure
```typescript
// app/api/tests/[testId]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { z } from 'zod';

import { authOptions } from '@/lib/auth-config';
import { prisma } from '@/lib/db';
import { ApiError, handleApiError } from '@/lib/api-utils';

// Request validation schemas
const getTestParamsSchema = z.object({
  testId: z.string().uuid('Invalid test ID format')
});

const updateTestBodySchema = z.object({
  status: z.enum(['PAUSED', 'RESUMED', 'CANCELLED']).optional(),
  currentQuestionIndex: z.number().int().min(0).optional()
});

// GET handler
export async function GET(
  request: NextRequest,
  { params }: { params: { testId: string } }
) {
  try {
    // Validate parameters
    const { testId } = getTestParamsSchema.parse(params);
    
    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: { code: 'UNAUTHORIZED', message: 'Authentication required' } },
        { status: 401 }
      );
    }
    
    // Fetch test data
    const test = await prisma.tests.findUnique({
      where: { id: testId },
      include: {
        test_types: true,
        test_results: true
      }
    });
    
    if (!test) {
      return NextResponse.json(
        { success: false, error: { code: 'NOT_FOUND', message: 'Test not found' } },
        { status: 404 }
      );
    }
    
    // Authorization check
    if (test.userId !== session.user.id && session.user.userType !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: { code: 'FORBIDDEN', message: 'Access denied' } },
        { status: 403 }
      );
    }
    
    // Return successful response
    return NextResponse.json({
      success: true,
      data: {
        id: test.id,
        testType: test.test_types.displayName,
        status: test.status,
        startedAt: test.startedAt,
        completedAt: test.completedAt,
        score: test.test_results?.score || null
      }
    });
    
  } catch (error) {
    return handleApiError(error);
  }
}

// PUT handler
export async function PUT(
  request: NextRequest,
  { params }: { params: { testId: string } }
) {
  try {
    // Validate parameters and body
    const { testId } = getTestParamsSchema.parse(params);
    const body = await request.json();
    const updates = updateTestBodySchema.parse(body);
    
    // Authentication and authorization
    const session = await getServerSession(authOptions);
    if (!session) {
      throw new ApiError('UNAUTHORIZED', 'Authentication required', 401);
    }
    
    // Update test
    const updatedTest = await prisma.tests.update({
      where: { 
        id: testId,
        userId: session.user.id // Ensure user owns the test
      },
      data: {
        ...updates,
        updatedAt: new Date()
      }
    });
    
    return NextResponse.json({
      success: true,
      data: updatedTest
    });
    
  } catch (error) {
    return handleApiError(error);
  }
}
```

### Error Handling Utilities
```typescript
// lib/api-utils.ts
export class ApiError extends Error {
  constructor(
    public readonly code: string,
    message: string,
    public readonly statusCode: number = 500,
    public readonly details?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export function handleApiError(error: unknown): NextResponse {
  console.error('API Error:', error);
  
  if (error instanceof ApiError) {
    return NextResponse.json(
      {
        success: false,
        error: {
          code: error.code,
          message: error.message,
          details: error.details
        }
      },
      { status: error.statusCode }
    );
  }
  
  if (error instanceof z.ZodError) {
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid request data',
          details: error.errors.reduce((acc, err) => {
            acc[err.path.join('.')] = err.message;
            return acc;
          }, {} as Record<string, string>)
        }
      },
      { status: 400 }
    );
  }
  
  // Generic error response
  return NextResponse.json(
    {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal server error occurred'
      }
    },
    { status: 500 }
  );
}
```

## Database Standards

### Prisma Schema Conventions
```prisma
// schema.prisma

// Use snake_case for table and column names
model users {
  id                    String   @id @default(uuid())
  email                 String   @unique @db.VarChar(255)
  email_verified        DateTime? @map("emailVerified")
  first_name           String   @map("firstName") @db.VarChar(100)
  last_name            String   @map("lastName") @db.VarChar(100)
  password             String?  @db.VarChar(255)
  user_type            String   @default("USER") @map("userType") @db.VarChar(20)
  is_primary_admin     Boolean  @default(false) @map("isPrimaryAdmin")
  is_deactivated       Boolean  @default(false) @map("isDeactivated")
  require_password_change Boolean @default(false) @map("requirePasswordChange")
  created_at           DateTime @default(now()) @map("createdAt")
  updated_at           DateTime @updatedAt @map("updatedAt")
  
  // Relations
  tests                tests[]
  test_results        test_results[]
  user_test_access    user_test_access[]
  
  // Indexes
  @@index([email])
  @@index([user_type])
  @@index([created_at])
  @@map("users")
}

// Use descriptive names for junction tables
model user_test_access {
  id            String      @id @default(uuid())
  user_id      String      @map("userId")
  test_type_id String      @map("testTypeId")
  access_type  String      @map("accessType") @db.VarChar(50)
  granted_by   String?     @map("grantedBy")
  granted_at   DateTime?   @map("grantedAt")
  expires_at   DateTime?   @map("expiresAt")
  is_active    Boolean     @default(true) @map("isActive")
  created_at   DateTime    @default(now()) @map("createdAt")
  updated_at   DateTime    @updatedAt @map("updatedAt")
  
  // Relations
  users        users       @relation(fields: [user_id], references: [id], onDelete: Cascade)
  test_types   test_types  @relation(fields: [test_type_id], references: [id])
  
  // Constraints
  @@unique([user_id, test_type_id])
  @@index([user_id])
  @@index([test_type_id])
  @@index([access_type])
  @@map("user_test_access")
}
```

### Database Query Patterns
```typescript
// lib/database/users.ts

// Use typed query builders
export async function getUserWithTestAccess(userId: string): Promise<UserWithAccess | null> {
  return prisma.users.findUnique({
    where: { id: userId },
    include: {
      user_test_access: {
        where: { is_active: true },
        include: {
          test_types: {
            select: {
              id: true,
              name: true,
              display_name: true
            }
          }
        }
      }
    }
  });
}

// Use transaction for related operations
export async function createUserWithAccess(
  userData: CreateUserData,
  testAccess: TestAccessData[]
): Promise<users> {
  return prisma.$transaction(async (tx) => {
    // Create user
    const user = await tx.users.create({
      data: {
        email: userData.email.toLowerCase(),
        first_name: userData.firstName,
        last_name: userData.lastName,
        password: await hashPassword(userData.password)
      }
    });
    
    // Create test access records
    if (testAccess.length > 0) {
      await tx.user_test_access.createMany({
        data: testAccess.map(access => ({
          user_id: user.id,
          test_type_id: access.testTypeId,
          access_type: access.accessType,
          granted_by: access.grantedBy
        }))
      });
    }
    
    return user;
  });
}

// Use proper error handling
export async function updateUserProfile(
  userId: string,
  updates: Partial<UserProfile>
): Promise<Result<users, DatabaseError>> {
  try {
    const user = await prisma.users.update({
      where: { id: userId },
      data: {
        ...updates,
        updated_at: new Date()
      }
    });
    
    return { success: true, data: user };
  } catch (error) {
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === 'P2002') {
        return {
          success: false,
          error: new DatabaseError('Email address already exists', 'UPDATE', 'users')
        };
      }
    }
    
    return {
      success: false,
      error: new DatabaseError('Failed to update user profile', 'UPDATE', 'users')
    };
  }
}
```

## CSS & Styling Standards

### Tailwind CSS Guidelines
```typescript
// Use cn utility for conditional classes
import { cn } from '@/lib/utils';

export function Button({ 
  variant = 'default', 
  size = 'default',
  disabled = false,
  className,
  ...props 
}: ButtonProps) {
  return (
    <button
      className={cn(
        // Base styles
        'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
        'disabled:pointer-events-none disabled:opacity-50',
        
        // Variant styles
        {
          'bg-primary text-primary-foreground hover:bg-primary/90': variant === 'default',
          'bg-destructive text-destructive-foreground hover:bg-destructive/90': variant === 'destructive',
          'border border-input bg-background hover:bg-accent hover:text-accent-foreground': variant === 'outline',
          'bg-secondary text-secondary-foreground hover:bg-secondary/80': variant === 'secondary',
          'hover:bg-accent hover:text-accent-foreground': variant === 'ghost',
          'text-primary underline-offset-4 hover:underline': variant === 'link'
        },
        
        // Size styles
        {
          'h-10 px-4 py-2': size === 'default',
          'h-9 rounded-md px-3': size === 'sm',
          'h-11 rounded-md px-8': size === 'lg',
          'h-10 w-10': size === 'icon'
        },
        
        // Custom classes
        className
      )}
      disabled={disabled}
      {...props}
    />
  );
}
```

### Component Styling Patterns
```css
/* Use semantic class names */
.test-timer {
  @apply flex items-center gap-2 text-sm font-medium;
}

.test-timer--warning {
  @apply text-amber-600 dark:text-amber-400;
}

.test-timer--critical {
  @apply text-red-600 dark:text-red-400 animate-pulse;
}

/* Use CSS custom properties for theming */
.score-gauge {
  --gauge-color: theme(colors.blue.500);
  --gauge-background: theme(colors.gray.200);
  --gauge-size: 120px;
}

.score-gauge[data-score="high"] {
  --gauge-color: theme(colors.green.500);
}

.score-gauge[data-score="low"] {
  --gauge-color: theme(colors.red.500);
}
```

## Testing Standards

### Unit Testing Patterns
```typescript
// __tests__/lib/password.test.ts
import { describe, test, expect } from '@jest/globals';
import { validatePasswordStrength, hashPassword, verifyPassword } from '@/lib/password';

describe('Password Utilities', () => {
  describe('validatePasswordStrength', () => {
    test('should return strong score for complex password', () => {
      const result = validatePasswordStrength('StrongP@ssw0rd123');
      
      expect(result.score).toBeGreaterThan(80);
      expect(result.hasMinLength).toBe(true);
      expect(result.hasUppercase).toBe(true);
      expect(result.hasLowercase).toBe(true);
      expect(result.hasNumber).toBe(true);
      expect(result.hasSpecialChar).toBe(true);
    });
    
    test('should return weak score for simple password', () => {
      const result = validatePasswordStrength('password');
      
      expect(result.score).toBeLessThan(40);
      expect(result.hasUppercase).toBe(false);
      expect(result.hasNumber).toBe(false);
      expect(result.hasSpecialChar).toBe(false);
    });
  });
  
  describe('hashPassword and verifyPassword', () => {
    test('should hash and verify password correctly', async () => {
      const password = 'TestPassword123!';
      
      const hashed = await hashPassword(password);
      expect(hashed).not.toBe(password);
      expect(hashed).toMatch(/^\$2[aby]\$\d+\$/);
      
      const isValid = await verifyPassword(password, hashed);
      expect(isValid).toBe(true);
      
      const isInvalid = await verifyPassword('WrongPassword', hashed);
      expect(isInvalid).toBe(false);
    });
  });
});
```

### Component Testing
```typescript
// __tests__/components/test-timer.test.tsx
import { render, screen, act } from '@testing-library/react';
import { TestTimer } from '@/components/test-timer';

// Mock timers
jest.useFakeTimers();

describe('TestTimer', () => {
  test('should display initial time correctly', () => {
    const startTime = new Date('2024-01-01T10:00:00Z');
    
    render(<TestTimer startTime={startTime} duration={5} />);
    
    expect(screen.getByText('05:00')).toBeInTheDocument();
  });
  
  test('should countdown and show warning at 1 minute', () => {
    const startTime = new Date(Date.now() - 4 * 60 * 1000); // 4 minutes ago
    const onTimeUp = jest.fn();
    
    render(
      <TestTimer 
        startTime={startTime} 
        duration={5} 
        onTimeUp={onTimeUp}
        showWarnings 
      />
    );
    
    expect(screen.getByText('01:00')).toBeInTheDocument();
    expect(screen.getByText('01:00')).toHaveClass('test-timer--warning');
  });
  
  test('should call onTimeUp when time expires', () => {
    const startTime = new Date(Date.now() - 5 * 60 * 1000); // 5 minutes ago
    const onTimeUp = jest.fn();
    
    render(<TestTimer startTime={startTime} duration={5} onTimeUp={onTimeUp} />);
    
    act(() => {
      jest.advanceTimersByTime(1000);
    });
    
    expect(onTimeUp).toHaveBeenCalled();
  });
});
```

## Documentation Standards

### Code Comments
```typescript
/**
 * Calculates the weighted words-per-minute score for a typing test.
 * 
 * @param session - The typing test session data
 * @returns The calculated typing results including WPM, accuracy, and weighted score
 * 
 * @example
 * ```typescript
 * const session = {
 *   timeElapsed: 300, // 5 minutes in seconds
 *   totalCharacters: 1200,
 *   correctCharacters: 1150,
 *   incorrectCharacters: 50
 * };
 * 
 * const result = calculateTypingScore(session);
 * console.log(result.weightedWPM); // e.g., 45.6
 * ```
 */
export function calculateTypingScore(session: TypingSession): TypingResult {
  const timeInMinutes = session.timeElapsed / 60;
  
  // Calculate gross WPM (total characters / 5 / minutes)
  // Division by 5 converts characters to "words" (standard typing measurement)
  const grossWPM = (session.totalCharacters / 5) / timeInMinutes;
  
  // Calculate net WPM (gross WPM minus errors per minute)
  const netWPM = grossWPM - (session.incorrectCharacters / timeInMinutes);
  
  // Calculate accuracy percentage
  const accuracy = (session.correctCharacters / session.totalCharacters) * 100;
  
  // Calculate weighted score (net WPM adjusted for accuracy)
  const weightedWPM = netWPM * (accuracy / 100);
  
  return {
    grossWPM: Math.round(grossWPM),
    netWPM: Math.round(netWPM),
    accuracy: Math.round(accuracy * 10) / 10, // Round to 1 decimal
    weightedWPM: Math.round(weightedWPM),
    score: Math.min(100, Math.max(0, weightedWPM)) // Clamp to 0-100
  };
}
```

### README Templates
```markdown
# Component Name

Brief description of what the component does.

## Usage

```typescript
import { ComponentName } from '@/components/component-name';

export function Example() {
  return (
    <ComponentName
      prop1="value"
      prop2={42}
      onAction={handleAction}
    />
  );
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `prop1` | `string` | `undefined` | Description of prop1 |
| `prop2` | `number` | `0` | Description of prop2 |
| `onAction` | `() => void` | `undefined` | Callback function |

## Examples

### Basic Usage
Description and code example.

### Advanced Usage
Description and code example.

## Accessibility

- Describe keyboard navigation
- List ARIA attributes used
- Mention screen reader considerations

## Testing

```bash
npm test components/component-name
```
```

---

*This comprehensive code standards guide ensures consistent, maintainable, and high-quality code throughout the Rubicon Programs Testing Application.*