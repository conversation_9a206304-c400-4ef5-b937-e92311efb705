# Architecture & Design

## System Architecture Overview

The Rubicon Programs Testing Application follows a modern **full-stack web application architecture** built on Next.js with a comprehensive backend-as-a-service approach. The system is designed for scalability, security, and maintainability.

## Technology Stack

### Frontend Technologies
```
Next.js 14.2.28          - React meta-framework with server-side rendering
React 18.2.0             - UI component library and state management
TypeScript 5.8.3         - Type-safe JavaScript development
Tailwind CSS 3.3.3       - Utility-first CSS framework
Radix UI                 - Accessible component primitives
Framer Motion 10.18.0    - Animation and gesture library
```

### Backend Technologies
```
Next.js API Routes       - Server-side API endpoints
NextAuth.js 4.24.11      - Authentication and session management
Prisma 6.7.0             - Database ORM and query builder
PostgreSQL               - Primary database system
bcryptjs 2.4.3           - Password hashing and security
```

### Data & Analytics
```
React Query 5.0.0        - Server state management and caching
Chart.js 4.4.9           - Data visualization charts
Plotly.js 2.35.3         - Interactive scientific charts
Lodash 4.17.21           - Utility functions and data manipulation
```

### Development Tools
```
ESLint 8.57.0            - Code linting and quality
Prettier 3.6.2           - Code formatting
PostCSS 8.4.30           - CSS processing
tsx 4.20.3               - TypeScript execution for scripts
```

## Application Architecture

### Multi-Layer Architecture

```
┌─────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                   │
│  ┌─────────────────┐  ┌─────────────────┐              │
│  │   User Dashboard│  │ Admin Dashboard │              │
│  │   - Test Taking │  │ - User Mgmt     │              │
│  │   - Progress    │  │ - Test Config   │              │
│  │   - Results     │  │ - Analytics     │              │
│  └─────────────────┘  └─────────────────┘              │
└─────────────────────────────────────────────────────────┘
│
┌─────────────────────────────────────────────────────────┐
│                    APPLICATION LAYER                    │
│  ┌─────────────────┐  ┌─────────────────┐              │
│  │  Authentication │  │   Test Engine   │              │
│  │  - NextAuth.js  │  │ - Question Mgmt │              │
│  │  - JWT Tokens   │  │ - Scoring Logic │              │
│  │  - Sessions     │  │ - Timer Control │              │
│  └─────────────────┘  └─────────────────┘              │
└─────────────────────────────────────────────────────────┘
│
┌─────────────────────────────────────────────────────────┐
│                      SERVICE LAYER                      │
│  ┌─────────────────┐  ┌─────────────────┐              │
│  │   API Routes    │  │   AI Services   │              │
│  │  - REST APIs    │  │ - Question Gen  │              │
│  │  - Data Queries │  │ - Adaptive Test │              │
│  │  - File Upload  │  │ - Analytics     │              │
│  └─────────────────┘  └─────────────────┘              │
└─────────────────────────────────────────────────────────┘
│
┌─────────────────────────────────────────────────────────┐
│                       DATA LAYER                        │
│  ┌─────────────────┐  ┌─────────────────┐              │
│  │   PostgreSQL    │  │   File Storage  │              │
│  │  - User Data    │  │ - Images        │              │
│  │  - Test Results │  │ - Documents     │              │
│  │  - Questions    │  │ - Exports       │              │
│  └─────────────────┘  └─────────────────┘              │
└─────────────────────────────────────────────────────────┘
```

## Component Architecture

### Frontend Architecture Pattern

The application follows a **component-based architecture** with clear separation of concerns:

```
/app
├── (auth)/              # Authentication routes and components
├── (dashboard)/         # User dashboard and test interfaces
├── (admin)/             # Administrative interfaces
├── api/                 # Backend API routes
├── components/          # Reusable UI components
│   ├── ui/              # Base UI components (Radix UI based)
│   ├── layout/          # Layout components
│   └── providers/       # Context providers and wrappers
├── lib/                 # Utility functions and configurations
└── hooks/               # Custom React hooks
```

### Key Architectural Patterns

#### 1. Server-Side Rendering (SSR)
- **Next.js App Router**: Modern file-based routing system
- **Server Components**: React server components for improved performance
- **Dynamic Imports**: Code splitting for optimal loading
- **Static Generation**: Pre-built pages for better performance

#### 2. State Management Strategy
```typescript
// Global State: React Query for server state
const { data: tests } = useQuery({
  queryKey: ['tests', userId],
  queryFn: () => fetchUserTests(userId)
});

// Local State: React useState/useReducer for UI state
const [currentQuestion, setCurrentQuestion] = useState(0);

// Form State: Formik/React Hook Form for complex forms
const { register, handleSubmit } = useForm<TestAnswers>();
```

#### 3. Component Composition
```typescript
// Higher-Order Component Pattern
export function withAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>
) {
  return function AuthenticatedComponent(props: P) {
    // Authentication logic
    return <WrappedComponent {...props} />;
  };
}

// Compound Component Pattern
<TestInterface>
  <TestInterface.Header />
  <TestInterface.Question />
  <TestInterface.Navigation />
</TestInterface>
```

## Database Architecture

### Entity Relationship Model

The database follows a **normalized relational model** with clear entity relationships:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│      USERS      │    │      TESTS      │    │  TEST_RESULTS   │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │ id (PK)   │──┼────┼─ │ userId    │  │    │  │ testId    │──┼─┐
│  │ email     │  │    │  │ testTypeId│──┼─┐  │  │ score     │  │ │
│  │ firstName │  │    │  │ status    │  │ │  │  │ gradeLevel│  │ │
│  │ userType  │  │    │  │ startedAt │  │ │  │  │ accuracy  │  │ │
│  └───────────┘  │    │  └───────────┘  │ │  │  └───────────┘  │ │
└─────────────────┘    └─────────────────┘ │  └─────────────────┘ │
                                           │                      │
┌─────────────────┐    ┌─────────────────┐ │  ┌─────────────────┐ │
│   TEST_TYPES    │    │   QUESTIONS     │ │  │ USER_TEST_ACCESS│ │
│  ┌───────────┐  │    │  ┌───────────┐  │ │  │  ┌───────────┐  │ │
│  │ id (PK)   │──┼────┼─ │ testTypeId│  │ │  │  │ userId    │──┼─┘
│  │ name      │  │    │  │ content   │  │ │  │  │ testTypeId│──┼───┘
│  │ displayNam│  │    │  │ type      │  │ │  │  │ accessType│  │
│  │ isActive  │  │    │  │ difficulty│  │ │  │  │ expiresAt │  │
│  └───────────┘  │    │  └───────────┘  │ │  │  └───────────┘  │
└─────────────────┘    └─────────────────┘ │  └─────────────────┘
                                           │
                       ┌─────────────────┐ │
                       │ ONE_TIME_CODES  │ │
                       │  ┌───────────┐  │ │
                       │  │ testTypeId│──┼─┘
                       │  │ code      │  │
                       │  │ usedBy    │  │
                       │  │ expiresAt │  │
                       │  └───────────┘  │
                       └─────────────────┘
```

### Data Access Patterns

#### 1. Repository Pattern with Prisma
```typescript
// User repository operations
export class UserRepository {
  async findWithAccess(userId: string) {
    return prisma.users.findUnique({
      where: { id: userId },
      include: {
        user_test_access: {
          include: { test_types: true }
        }
      }
    });
  }
}
```

#### 2. Query Optimization
- **Eager Loading**: Include related data in single queries
- **Selective Loading**: Fetch only required fields
- **Connection Pooling**: Efficient database connections
- **Indexing Strategy**: Optimized database indexes

## Security Architecture

### Authentication Flow
```
User Request → NextAuth Middleware → JWT Validation → Route Protection
     ↓               ↓                    ↓               ↓
Session Check → Token Refresh → User Status Check → Access Control
```

### Security Layers

#### 1. Authentication Security
- **JWT Tokens**: Secure session management
- **Password Hashing**: bcrypt with salt rounds
- **Email Verification**: Required before account activation
- **Session Expiration**: Automatic timeout and refresh

#### 2. Authorization Levels
```typescript
enum UserType {
  USER = "USER",
  ADMIN = "ADMIN"
}

enum AccessType {
  NONE = "NONE",
  PRACTICE_ONLY = "PRACTICE_ONLY", 
  ONE_TIME = "ONE_TIME",
  UNLIMITED = "UNLIMITED"
}
```

#### 3. Data Protection
- **Input Validation**: Server-side validation for all inputs
- **SQL Injection Prevention**: Prisma ORM parameterized queries
- **XSS Protection**: Content Security Policy headers
- **CSRF Protection**: Built-in Next.js protections

## Performance Architecture

### Optimization Strategies

#### 1. Frontend Performance
```typescript
// Code Splitting
const AdminDashboard = dynamic(() => import('./admin-dashboard'), {
  loading: () => <LoadingSpinner />
});

// Image Optimization
import Image from 'next/image';
<Image src="/logo.png" width={200} height={100} alt="Logo" />

// Caching Strategy
export const revalidate = 3600; // ISR - 1 hour cache
```

#### 2. Backend Performance
- **Database Indexing**: Strategic indexes on frequently queried fields
- **Query Optimization**: Efficient Prisma queries with projections
- **Connection Pooling**: PostgreSQL connection management
- **Response Caching**: API route caching for static data

#### 3. Real-time Features
```typescript
// Test Session Management
export interface TypingTestSession {
  keystrokeLog: KeystrokeEvent[];
  realTimeStats: {
    wpm: number;
    accuracy: number;
    position: number;
  };
}

// Auto-save Functionality
const autoSave = useCallback(debounce((data) => {
  saveTestProgress(data);
}, 2000), []);
```

## Scalability Considerations

### Horizontal Scaling
- **Stateless API Design**: No server-side session storage
- **Database Read Replicas**: Separate read/write operations
- **CDN Integration**: Static asset distribution
- **Load Balancing**: Multiple application instances

### Vertical Scaling
- **Database Optimization**: Efficient queries and indexing
- **Memory Management**: Optimized component rendering
- **Background Processing**: Async operations for heavy tasks
- **Resource Monitoring**: Performance metrics and alerts

## Design Patterns

### 1. Factory Pattern
```typescript
// Test creation factory
export class TestFactory {
  static createTest(type: TestType, config: TestConfig): Test {
    switch (type) {
      case 'typing-keyboard':
        return new KeyboardTypingTest(config);
      case 'basic-math':
        return new MathTest(config);
      // ...
    }
  }
}
```

### 2. Observer Pattern
```typescript
// Test progress tracking
export class TestProgressTracker {
  private observers: TestObserver[] = [];
  
  notify(event: TestEvent) {
    this.observers.forEach(observer => observer.update(event));
  }
}
```

### 3. Strategy Pattern
```typescript
// Scoring strategies for different test types
interface ScoringStrategy {
  calculateScore(answers: Answer[]): TestResult;
}

export class TypingScoring implements ScoringStrategy {
  calculateScore(keystrokes: Keystroke[]): TypingResult {
    // Typing-specific scoring logic
  }
}
```

## Error Handling & Logging

### Error Management Strategy
```typescript
// Global error boundary
export class GlobalErrorBoundary extends React.Component {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Global error:', error, errorInfo);
    // Send to error tracking service
  }
}

// API error handling
export async function apiCall<T>(request: () => Promise<T>): Promise<T> {
  try {
    return await request();
  } catch (error) {
    console.error('API Error:', error);
    throw new APIError('Request failed', error);
  }
}
```

### Logging Strategy
- **Application Logs**: User actions and system events
- **Error Logs**: Exception tracking and debugging
- **Performance Logs**: Response times and resource usage
- **Audit Logs**: Administrative actions and security events

---

*This architecture document provides the technical foundation for understanding the system's design principles, patterns, and implementation strategies.*