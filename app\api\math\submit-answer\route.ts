
import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { requireAuth } from '@/lib/auth';

// This is a placeholder for the actual AI question generation
async function generateNewQuestion(gradeLevel: number) {
  // In a real scenario, this would call the AI service
  // For now, it creates a placeholder question in the database
  console.log(`Generating new question for grade level ${gradeLevel}`);
  return await prisma.mathQuestion.create({
    data: {
      questionText: `New AI-generated question for grade ${gradeLevel}. What is ${gradeLevel} + ${gradeLevel}?`,
      answer: (gradeLevel * 2).toString(),
      gradeLevel: gradeLevel,
    },
  });
}


export async function POST(req: Request) {
  try {
    const user = await requireAuth();

    const { testId, questionId, userAnswer } = await req.json();

    const test = await prisma.mathTest.findUnique({
      where: { id: testId },
      include: { mathTestQuestions: true },
    });

    if (!test || test.userId !== user.id) {
      return NextResponse.json({ error: 'Test not found' }, { status: 404 });
    }

    if (test.status === 'COMPLETED') {
        return NextResponse.json({ error: 'Test is already completed.' }, { status: 400 });
    }

    if (test.status === 'PAUSED' || test.pausedAt) {
      return NextResponse.json({ error: 'Test is paused. Resume before submitting answers.' }, { status: 400 });
    }

    const question = await prisma.mathQuestion.findUnique({
      where: { id: questionId },
    });

    if (!question) {
      return NextResponse.json({ error: 'Question not found' }, { status: 404 });
    }

    const isCorrect = question.answer.trim().toLowerCase() === userAnswer.trim().toLowerCase();

    await prisma.mathTestQuestion.create({
        data: {
            mathTestId: test.id,
            mathQuestionId: question.id,
            userAnswer,
            isCorrect,
        }
    });

    const gradeLevel = question.gradeLevel;
    const correctAnswersByGrade = JSON.parse(test.correctAnswersByGrade as string || '\{\}');
    const incorrectAnswersByGrade = JSON.parse(test.incorrectAnswersByGrade as string || '\{\}');

    if (isCorrect) {
      correctAnswersByGrade[gradeLevel] = (correctAnswersByGrade[gradeLevel] || 0) + 1;
    } else {
      incorrectAnswersByGrade[gradeLevel] = (incorrectAnswersByGrade[gradeLevel] || 0) + 1;
    }
    
    // Check for test completion
    if (incorrectAnswersByGrade[gradeLevel] >= 3) {
      const finalScore = Math.max(5, gradeLevel - 1);
      await prisma.mathTest.update({
        where: { id: testId },
        data: {
            status: 'COMPLETED',
            finalScore,
            correctAnswersByGrade: JSON.stringify(correctAnswersByGrade),
            incorrectAnswersByGrade: JSON.stringify(incorrectAnswersByGrade),
            completedAt: new Date(),
        },
      });
      return NextResponse.json({ status: 'completed', finalScore });
    }

    if (correctAnswersByGrade[12] >= 2) {
      await prisma.mathTest.update({
        where: { id: testId },
        data: {
            status: 'COMPLETED',
            finalScore: 12,
            correctAnswersByGrade: JSON.stringify(correctAnswersByGrade),
            incorrectAnswersByGrade: JSON.stringify(incorrectAnswersByGrade),
            completedAt: new Date(),
        },
      });
      return NextResponse.json({ status: 'completed', finalScore: 12 });
    }

    // Determine next grade level
    let nextGradeLevel = test.currentGradeLevel;
    if (isCorrect) {
        if (correctAnswersByGrade[gradeLevel] >= 2 && gradeLevel < 12) {
            nextGradeLevel = gradeLevel + 1;
        }
    } else {
        if (gradeLevel > 6) {
            nextGradeLevel = gradeLevel - 1;
        } else {
            nextGradeLevel = 6;
        }
    }

    // Find next question
    const answeredQuestionIds = test.mathTestQuestions.map((q: any) => q.mathQuestionId);
    
    let nextQuestion;
    const potentialQuestions = await prisma.mathQuestion.findMany({
        where: {
            gradeLevel: nextGradeLevel,
            id: { notIn: answeredQuestionIds },
        }
    });

    if (potentialQuestions.length > 0 && Math.random() < 0.8) {
        nextQuestion = potentialQuestions[Math.floor(Math.random() * potentialQuestions.length)];
    } else {
        nextQuestion = await generateNewQuestion(nextGradeLevel);
    }
    
    await prisma.mathTest.update({
        where: { id: testId },
        data: {
            currentGradeLevel: nextGradeLevel,
            correctAnswersByGrade: JSON.stringify(correctAnswersByGrade),
            incorrectAnswersByGrade: JSON.stringify(incorrectAnswersByGrade),
        }
    });

    return NextResponse.json({ status: 'inprogress', question: nextQuestion });

  } catch (error) {
    console.error("Error submitting answer:", error);
    return NextResponse.json({ error: 'Something went wrong' }, { status: 500 });
  }
}
