import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { requireAuth } from '@/lib/auth';

export async function GET(_req: Request, { params }: { params: { id: string } }) {
  try {
    const user = await requireAuth();
    const userId = user.id;
    const id = params.id;

    const test = await prisma.mathTest.findUnique({ where: { id } });
    if (!test) {
      return NextResponse.json({ error: 'Test not found' }, { status: 404 });
    }
    if (test.userId !== userId) {
      return NextResponse.json({ error: 'Not authorized to view this test' }, { status: 403 });
    }

    const now = new Date();
    const startedAt = test.startedAt as unknown as Date;
    const completedAt = (test.completedAt ?? undefined) as unknown as Date | undefined;
    const pausedAt = (test.pausedAt ?? undefined) as unknown as Date | undefined;

    const isPaused = Boolean(pausedAt) || test.status === 'PAUSED';

    const endForActive = isPaused ? (pausedAt ?? now) : (completedAt ?? now);
    const elapsedMs = Math.max(0, endForActive.getTime() - startedAt.getTime());

    const pausedCurrentMs = isPaused && pausedAt ? Math.max(0, now.getTime() - pausedAt.getTime()) : 0;

    return NextResponse.json({
      id: test.id,
      status: test.status,
      startedAt: test.startedAt,
      pausedAt: test.pausedAt,
      completedAt: test.completedAt,
      isPractice: test.isPractice,
      currentGradeLevel: test.currentGradeLevel,
      time: {
        elapsedMs,
        elapsedSeconds: Math.floor(elapsedMs / 1000),
        pausedCurrentMs,
        pausedCurrentSeconds: Math.floor(pausedCurrentMs / 1000),
        isPaused,
      }
    });
  } catch (error) {
    console.error('Error fetching status:', error);
    return NextResponse.json({ error: 'Something went wrong.' }, { status: 500 });
  }
}
