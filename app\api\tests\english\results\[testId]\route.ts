import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// Types for test results
interface TestAnswer {
  id: string;
  question_id: string;
  user_answer: string;
  correct_answer: string;
  is_correct: boolean;
  time_spent: number;
  question_type: 'multiple-choice' | 'spelling' | 'grammar' | 'reading-comprehension';
  difficulty_level: number;
  grade_level: string;
}

interface TestSession {
  id: string;
  user_id: string;
  test_type: 'full' | 'practice';
  start_time: string;
  end_time: string;
  current_grade_level: string;
  total_questions: number;
  is_completed: boolean;
  adaptive_parameters: any;
}

interface QuestionAnalysis {
  question_id: string;
  question_type: string;
  difficulty_level: number;
  grade_level: string;
  is_correct: boolean;
  time_spent: number;
  user_answer: string;
  correct_answer: string;
  efficiency_score: number; // Time vs expected time for this difficulty
}

interface LanguageMasteryBreakdown {
  vocabulary: number;
  grammar: number;
  spelling: number;
  overall: number;
}

interface ReadingComprehensionBreakdown {
  literal_comprehension: number;
  inferential_comprehension: number;
  evaluative_comprehension: number;
  overall: number;
}

interface TimeMetrics {
  total_duration: number; // seconds
  average_time_per_question: number;
  time_efficiency_score: number; // 0-100, higher is better
  questions_rushed: number; // answered too quickly
  questions_overtime: number; // took too long
}

interface ScoreComparison {
  user_score: number;
  grade_average: number;
  national_average: number;
  percentile_rank: number;
}

interface VisualizationData {
  radar_chart: {
    categories: string[];
    user_scores: number[];
    grade_averages: number[];
  };
  progress_over_time: {
    date: string;
    overall_score: number;
    grade_level: string;
  }[];
  subscore_comparisons: {
    [category: string]: ScoreComparison;
  };
}

interface CertificateEligibility {
  is_eligible: boolean;
  grade_level_achieved: string;
  overall_score: number;
  minimum_score_met: boolean;
  is_full_test: boolean;
  certificate_type: string | null;
  issued_date: string | null;
}

interface ComprehensiveResults {
  test_id: string;
  user_id: string;
  overall_grade_level: string;
  overall_score: number;
  language_mastery: LanguageMasteryBreakdown;
  reading_comprehension: ReadingComprehensionBreakdown;
  question_analysis: QuestionAnalysis[];
  time_metrics: TimeMetrics;
  visualization_data: VisualizationData;
  certificate_eligibility: CertificateEligibility;
  completion_date: string;
}

// Mock data for averages and percentiles (in real implementation, this would come from database)
const GRADE_AVERAGES = {
  'grade-3': { overall: 75, vocabulary: 78, grammar: 73, spelling: 76, reading: 74 },
  'grade-4': { overall: 78, vocabulary: 80, grammar: 76, spelling: 78, reading: 77 },
  'grade-5': { overall: 80, vocabulary: 82, grammar: 78, spelling: 80, reading: 79 },
  'grade-6': { overall: 82, vocabulary: 84, grammar: 80, spelling: 82, reading: 81 },
  'grade-7': { overall: 84, vocabulary: 86, grammar: 82, spelling: 84, reading: 83 },
  'grade-8': { overall: 86, vocabulary: 88, grammar: 84, spelling: 86, reading: 85 },
};

const NATIONAL_AVERAGES = {
  overall: 79,
  vocabulary: 81,
  grammar: 77,
  spelling: 80,
  reading: 78
};

// Expected time per question by difficulty (in seconds)
const EXPECTED_TIME_BY_DIFFICULTY = {
  1: 45,  // Easy
  2: 60,  // Medium
  3: 90,  // Hard
  4: 120, // Very Hard
  5: 150  // Expert
};

class EnglishTestScoringEngine {
  
  static calculateLanguageMastery(answers: TestAnswer[]): LanguageMasteryBreakdown {
    const vocabularyQuestions = answers.filter(a => 
      a.question_type === 'multiple-choice' && a.grade_level
    );
    const grammarQuestions = answers.filter(a => a.question_type === 'grammar');
    const spellingQuestions = answers.filter(a => a.question_type === 'spelling');
    
    const vocabulary = this.calculateCategoryScore(vocabularyQuestions);
    const grammar = this.calculateCategoryScore(grammarQuestions);
    const spelling = this.calculateCategoryScore(spellingQuestions);
    
    const overall = (vocabulary + grammar + spelling) / 3;
    
    return { vocabulary, grammar, spelling, overall };
  }
  
  static calculateReadingComprehension(answers: TestAnswer[]): ReadingComprehensionBreakdown {
    const readingQuestions = answers.filter(a => a.question_type === 'reading-comprehension');
    
    // Classify reading questions by comprehension type (would be in question metadata)
    const literal = readingQuestions.filter((_, i) => i % 3 === 0);
    const inferential = readingQuestions.filter((_, i) => i % 3 === 1);
    const evaluative = readingQuestions.filter((_, i) => i % 3 === 2);
    
    const literal_comprehension = this.calculateCategoryScore(literal);
    const inferential_comprehension = this.calculateCategoryScore(inferential);
    const evaluative_comprehension = this.calculateCategoryScore(evaluative);
    
    const overall = this.calculateCategoryScore(readingQuestions);
    
    return {
      literal_comprehension,
      inferential_comprehension,
      evaluative_comprehension,
      overall
    };
  }
  
  static calculateCategoryScore(questions: TestAnswer[]): number {
    if (questions.length === 0) return 0;
    
    let totalScore = 0;
    let totalWeight = 0;
    
    questions.forEach(q => {
      const weight = this.getDifficultyWeight(q.difficulty_level);
      const score = q.is_correct ? 100 : 0;
      totalScore += score * weight;
      totalWeight += weight;
    });
    
    return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
  }
  
  static getDifficultyWeight(difficulty: number): number {
    const weights = { 1: 0.8, 2: 1.0, 3: 1.2, 4: 1.4, 5: 1.6 };
    return weights[difficulty as keyof typeof weights] || 1.0;
  }
  
  static calculateOverallGradeLevel(answers: TestAnswer[]): string {
    // Adaptive algorithm to determine grade level based on performance
    const correctAnswers = answers.filter(a => a.is_correct);
    const totalQuestions = answers.length;
    
    if (totalQuestions === 0) return 'grade-3';
    
    const accuracyRate = correctAnswers.length / totalQuestions;
    const avgDifficulty = answers.reduce((sum, a) => sum + a.difficulty_level, 0) / totalQuestions;
    const avgGradeLevel = this.extractGradeLevelNumber(answers);
    
    // Simple grade level calculation (would be more sophisticated in real implementation)
    let gradeLevel = Math.round(avgGradeLevel);
    
    // Adjust based on accuracy
    if (accuracyRate >= 0.85) gradeLevel += 1;
    else if (accuracyRate < 0.65) gradeLevel -= 1;
    
    // Clamp to valid range
    gradeLevel = Math.max(3, Math.min(8, gradeLevel));
    
    return `grade-${gradeLevel}`;
  }
  
  static extractGradeLevelNumber(answers: TestAnswer[]): number {
    const gradeLevels = answers.map(a => {
      const match = a.grade_level.match(/grade-(\d+)/);
      return match ? parseInt(match[1]) : 5;
    });
    
    return gradeLevels.reduce((sum, g) => sum + g, 0) / gradeLevels.length;
  }
  
  static calculateTimeMetrics(answers: TestAnswer[]): TimeMetrics {
    const totalDuration = answers.reduce((sum, a) => sum + a.time_spent, 0);
    const averageTimePerQuestion = totalDuration / answers.length;
    
    let timeEfficiencyScore = 0;
    let questionsRushed = 0;
    let questionsOvertime = 0;
    
    answers.forEach(answer => {
      const expectedTime = EXPECTED_TIME_BY_DIFFICULTY[answer.difficulty_level as keyof typeof EXPECTED_TIME_BY_DIFFICULTY];
      const efficiency = Math.min(100, (expectedTime / answer.time_spent) * 100);
      timeEfficiencyScore += efficiency;
      
      if (answer.time_spent < expectedTime * 0.5) questionsRushed++;
      if (answer.time_spent > expectedTime * 1.5) questionsOvertime++;
    });
    
    timeEfficiencyScore = timeEfficiencyScore / answers.length;
    
    return {
      total_duration: totalDuration,
      average_time_per_question: averageTimePerQuestion,
      time_efficiency_score: Math.round(timeEfficiencyScore),
      questions_rushed: questionsRushed,
      questions_overtime: questionsOvertime
    };
  }
  
  static analyzeQuestions(answers: TestAnswer[]): QuestionAnalysis[] {
    return answers.map(answer => {
      const expectedTime = EXPECTED_TIME_BY_DIFFICULTY[answer.difficulty_level as keyof typeof EXPECTED_TIME_BY_DIFFICULTY];
      const efficiencyScore = Math.min(100, (expectedTime / answer.time_spent) * 100);
      
      return {
        question_id: answer.question_id,
        question_type: answer.question_type,
        difficulty_level: answer.difficulty_level,
        grade_level: answer.grade_level,
        is_correct: answer.is_correct,
        time_spent: answer.time_spent,
        user_answer: answer.user_answer,
        correct_answer: answer.correct_answer,
        efficiency_score: Math.round(efficiencyScore)
      };
    });
  }
  
  static calculatePercentileRank(score: number, gradeLevel: string): number {
    // Mock percentile calculation (would use real data in production)
    const gradeAvg = GRADE_AVERAGES[gradeLevel as keyof typeof GRADE_AVERAGES]?.overall || 75;
    
    if (score >= gradeAvg + 15) return 95;
    if (score >= gradeAvg + 10) return 85;
    if (score >= gradeAvg + 5) return 75;
    if (score >= gradeAvg) return 60;
    if (score >= gradeAvg - 5) return 45;
    if (score >= gradeAvg - 10) return 30;
    if (score >= gradeAvg - 15) return 15;
    return 5;
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { testId: string } }
) {
  try {
    const testId = params.testId;
    
    if (!testId) {
      return NextResponse.json({ error: 'Test ID is required' }, { status: 400 });
    }
    
    // Fetch test session data
    const { data: session, error: sessionError } = await supabase
      .from('english_test_sessions')
      .select('*')
      .eq('id', testId)
      .single();
    
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Test session not found' }, { status: 404 });
    }
    
    if (!session.is_completed) {
      return NextResponse.json({ error: 'Test is not completed yet' }, { status: 400 });
    }
    
    // Fetch test answers
    const { data: answers, error: answersError } = await supabase
      .from('english_test_answers')
      .select('*')
      .eq('test_session_id', testId)
      .order('created_at', { ascending: true });
    
    if (answersError) {
      return NextResponse.json({ error: 'Error fetching test answers' }, { status: 500 });
    }
    
    // Check if results already exist
    const { data: existingResults } = await supabase
      .from('english_test_results')
      .select('*')
      .eq('test_session_id', testId)
      .single();
    
    if (existingResults) {
      // Return existing results
      return NextResponse.json(existingResults);
    }
    
    // Calculate comprehensive results
    const testAnswers: TestAnswer[] = answers?.map(answer => ({
      id: answer.id,
      question_id: answer.question_id,
      user_answer: answer.user_answer,
      correct_answer: answer.correct_answer,
      is_correct: answer.is_correct,
      time_spent: answer.time_spent || 60,
      question_type: answer.question_type,
      difficulty_level: answer.difficulty_level || 2,
      grade_level: answer.grade_level || 'grade-5'
    })) || [];
    
    const overallGradeLevel = EnglishTestScoringEngine.calculateOverallGradeLevel(testAnswers);
    const languageMastery = EnglishTestScoringEngine.calculateLanguageMastery(testAnswers);
    const readingComprehension = EnglishTestScoringEngine.calculateReadingComprehension(testAnswers);
    const timeMetrics = EnglishTestScoringEngine.calculateTimeMetrics(testAnswers);
    const questionAnalysis = EnglishTestScoringEngine.analyzeQuestions(testAnswers);
    
    const overallScore = Math.round((languageMastery.overall + readingComprehension.overall) / 2);
    
    // Get historical data for progress tracking
    const { data: historicalResults } = await supabase
      .from('english_test_results')
      .select('completion_date, overall_score, overall_grade_level')
      .eq('user_id', session.user_id)
      .order('completion_date', { ascending: true });
    
    const progressOverTime = historicalResults?.map(result => ({
      date: result.completion_date,
      overall_score: result.overall_score,
      grade_level: result.overall_grade_level
    })) || [];
    
    // Calculate visualization data
    const gradeAvg = GRADE_AVERAGES[overallGradeLevel as keyof typeof GRADE_AVERAGES] || GRADE_AVERAGES['grade-5'];
    const percentileRank = EnglishTestScoringEngine.calculatePercentileRank(overallScore, overallGradeLevel);
    
    const visualizationData: VisualizationData = {
      radar_chart: {
        categories: ['Vocabulary', 'Grammar', 'Spelling', 'Literal Reading', 'Inferential Reading', 'Evaluative Reading'],
        user_scores: [
          languageMastery.vocabulary,
          languageMastery.grammar,
          languageMastery.spelling,
          readingComprehension.literal_comprehension,
          readingComprehension.inferential_comprehension,
          readingComprehension.evaluative_comprehension
        ],
        grade_averages: [
          gradeAvg.vocabulary,
          gradeAvg.grammar,
          gradeAvg.spelling,
          gradeAvg.reading,
          gradeAvg.reading,
          gradeAvg.reading
        ]
      },
      progress_over_time: progressOverTime,
      subscore_comparisons: {
        'Language Mastery': {
          user_score: languageMastery.overall,
          grade_average: gradeAvg.overall,
          national_average: NATIONAL_AVERAGES.overall,
          percentile_rank: percentileRank
        },
        'Reading Comprehension': {
          user_score: readingComprehension.overall,
          grade_average: gradeAvg.reading,
          national_average: NATIONAL_AVERAGES.reading,
          percentile_rank: EnglishTestScoringEngine.calculatePercentileRank(readingComprehension.overall, overallGradeLevel)
        },
        'Vocabulary': {
          user_score: languageMastery.vocabulary,
          grade_average: gradeAvg.vocabulary,
          national_average: NATIONAL_AVERAGES.vocabulary,
          percentile_rank: EnglishTestScoringEngine.calculatePercentileRank(languageMastery.vocabulary, overallGradeLevel)
        },
        'Grammar': {
          user_score: languageMastery.grammar,
          grade_average: gradeAvg.grammar,
          national_average: NATIONAL_AVERAGES.grammar,
          percentile_rank: EnglishTestScoringEngine.calculatePercentileRank(languageMastery.grammar, overallGradeLevel)
        },
        'Spelling': {
          user_score: languageMastery.spelling,
          grade_average: gradeAvg.spelling,
          national_average: NATIONAL_AVERAGES.spelling,
          percentile_rank: EnglishTestScoringEngine.calculatePercentileRank(languageMastery.spelling, overallGradeLevel)
        }
      }
    };
    
    // Determine certificate eligibility
    const certificateEligibility: CertificateEligibility = {
      is_eligible: overallScore >= 70 && session.test_type === 'full',
      grade_level_achieved: overallGradeLevel,
      overall_score: overallScore,
      minimum_score_met: overallScore >= 70,
      is_full_test: session.test_type === 'full',
      certificate_type: overallScore >= 70 && session.test_type === 'full' ? 
        `English Proficiency - ${overallGradeLevel.toUpperCase().replace('-', ' ')}` : null,
      issued_date: overallScore >= 70 && session.test_type === 'full' ? 
        new Date().toISOString() : null
    };
    
    const comprehensiveResults: ComprehensiveResults = {
      test_id: testId,
      user_id: session.user_id,
      overall_grade_level: overallGradeLevel,
      overall_score: overallScore,
      language_mastery: languageMastery,
      reading_comprehension: readingComprehension,
      question_analysis: questionAnalysis,
      time_metrics: timeMetrics,
      visualization_data: visualizationData,
      certificate_eligibility: certificateEligibility,
      completion_date: session.end_time || new Date().toISOString()
    };
    
    // Store results in database
    const { error: insertError } = await supabase
      .from('english_test_results')
      .insert({
        test_session_id: testId,
        user_id: session.user_id,
        overall_grade_level: overallGradeLevel,
        overall_score: overallScore,
        language_mastery_score: languageMastery.overall,
        reading_comprehension_score: readingComprehension.overall,
        vocabulary_score: languageMastery.vocabulary,
        grammar_score: languageMastery.grammar,
        spelling_score: languageMastery.spelling,
        time_efficiency_score: timeMetrics.time_efficiency_score,
        total_duration: timeMetrics.total_duration,
        certificate_eligible: certificateEligibility.is_eligible,
        percentile_rank: percentileRank,
        detailed_results: comprehensiveResults,
        completion_date: comprehensiveResults.completion_date
      });
    
    if (insertError) {
      console.error('Error storing results:', insertError);
      // Continue anyway, return calculated results
    }
    
    return NextResponse.json(comprehensiveResults);
    
  } catch (error) {
    console.error('Error calculating test results:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { testId: string } }
) {
  try {
    const testId = params.testId;
    const body = await request.json();
    const { regenerate = false } = body;
    
    if (!testId) {
      return NextResponse.json({ error: 'Test ID is required' }, { status: 400 });
    }
    
    if (regenerate) {
      // Delete existing results and recalculate
      await supabase
        .from('english_test_results')
        .delete()
        .eq('test_session_id', testId);
      
      // Call GET method to recalculate
      return GET(request, { params });
    }
    
    return NextResponse.json({ error: 'Invalid operation' }, { status: 400 });
    
  } catch (error) {
    console.error('Error in POST request:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
