-- Enum for ai_generation_queue status
CREATE TYPE ai_generation_status AS ENUM (
    'PENDING',
    'PROCESSING',
    'COMPLETED',
    'FAILED'
);

-- Table for tracking AI question generation
CREATE TABLE ai_generation_queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    test_type VARCHAR(255) NOT NULL,
    question_type VARCHAR(255) NOT NULL,
    grade_level INTEGER NOT NULL,
    status ai_generation_status NOT NULL DEFAULT 'PENDING',
    priority INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT
);

-- Table for tracking question bank statistics
CREATE TABLE question_bank_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    test_type VARCHAR(255) NOT NULL,
    question_type VARCHAR(255) NOT NULL,
    grade_level INTEGER NOT NULL,
    total_questions INTEGER NOT NULL DEFAULT 0,
    active_questions INTEGER NOT NULL DEFAULT 0,
    last_generated_at TIMESTAMP WITH TIME ZONE,
    generation_needed BOOLEAN NOT NULL DEFAULT FALSE,
    UNIQUE(test_type, question_type, grade_level)
);

-- Add an index on status and priority for faster querying of the queue
CREATE INDEX idx_ai_generation_queue_status_priority ON ai_generation_queue (status, priority);

-- Add an index on generation_needed for faster querying of stats
CREATE INDEX idx_question_bank_stats_generation_needed ON question_bank_stats (generation_needed);
