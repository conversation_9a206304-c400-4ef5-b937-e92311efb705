# Database Schema Documentation

## Overview

The Rubicon Programs Testing Application uses **PostgreSQL** as the primary database with **Prisma ORM** for type-safe database operations. The schema is designed to support multi-user testing, comprehensive user management, detailed analytics, and flexible test configurations.

## Database Configuration

```prisma
generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "linux-musl-arm64-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url = env("DATABASE_URL")
}
```

## Core Entities

### 1. User Management

#### Users Table
The central user entity supporting multiple user types and comprehensive profile data.

```prisma
model users {
  id                    String    @id @default(cuid())
  email                 String    @unique
  emailVerified         DateTime?
  password              String?
  firstName             String
  lastName              String
  middleInitial         String?
  namePrefix            String?
  nameSuffix            String?
  dateOfBirth           DateTime
  zipCode               String
  phoneNumber           String?
  englishFirst          Boolean?
  educationLevel        String?
  profileImage          String?
  userType              UserType  @default(USER)
  isPrimaryAdmin        Boolean   @default(false)
  primaryAdminDate      DateTime?
  twoFactorEnabled      Boolean   @default(false)
  isDeactivated         Boolean   @default(false)
  requirePasswordChange Boolean   @default(false)
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  
  // Relationships
  Account               Account[]
  EmailVerification     EmailVerification[]
  PasswordReset         PasswordReset[]
  Session               Session[]
  admin_actions         admin_actions[]
  test_requests         test_requests[]
  test_results          test_results[]
  tests                 tests[]
  user_test_access      user_test_access[]
  typing_sessions       typing_test_sessions[]
  math_tests            MathTest[]
}
```

**Key Features:**
- **Unique Email**: Primary identifier for user authentication
- **Flexible Naming**: Support for prefixes, suffixes, middle initials
- **Profile Completion**: Date of birth validation for profile completeness
- **Administrative Controls**: Deactivation and password change requirements
- **Audit Trail**: Creation and modification timestamps

#### User Types Enum
```prisma
enum UserType {
  USER      # Standard test-taking users
  ADMIN     # Administrative users with management privileges
}
```

### 2. Authentication System

#### Account Model (NextAuth Integration)
```prisma
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  users             users   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}
```

#### Session Management
```prisma
model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  users        users    @relation(fields: [userId], references: [id], onDelete: Cascade)
}
```

#### Email Verification
```prisma
model EmailVerification {
  id        String   @id @default(cuid())
  userId    String
  email     String
  token     String   @unique
  expires   DateTime
  verified  Boolean  @default(false)
  createdAt DateTime @default(now())
  users     users    @relation(fields: [userId], references: [id], onDelete: Cascade)
}
```

#### Password Reset
```prisma
model PasswordReset {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expires   DateTime
  used      Boolean  @default(false)
  createdAt DateTime @default(now())
  users     users    @relation(fields: [userId], references: [id], onDelete: Cascade)
}
```

### 3. Test System Architecture

#### Test Types
Defines the four core assessment areas with configuration options.

```prisma
model test_types {
  id                   String                @id @default(cuid())
  name                 String                @unique
  displayName          String
  description          String?
  isActive             Boolean               @default(true)
  
  // Relationships
  one_time_codes       one_time_codes[]
  practice_test_config practice_test_config?
  questions            questions[]
  test_requests        test_requests[]
  tests                tests[]
  user_test_access     user_test_access[]
}
```

**Standard Test Types:**
- `typing-keyboard`: Keyboard typing proficiency
- `typing-10key`: Numeric keypad proficiency
- `digital-literacy`: Computer and technology skills
- `basic-math`: Mathematical competency (5th-12th grade)
- `basic-english`: Language and reading comprehension

#### Tests Table
Central test session management with comprehensive state tracking.

```prisma
model tests {
  id                String                 @id @default(cuid())
  userId            String?
  testTypeId        String
  oneTimeCodeId     String?
  status            TestStatus             @default(STARTED)
  isPractice        Boolean                @default(false)
  startedAt         DateTime               @default(now())
  pausedAt          DateTime?
  completedAt       DateTime?
  cancelledAt       DateTime?
  cancelReason      String?
  timeLimit         Int?                   # in minutes
  currentQuestionId String?
  questionsOrder    Json?                  # Array of question IDs
  answers           Json?                  # User responses
  score             Float?
  gradeLevelScore   String?
  additionalData    Json?                  # Test-specific data
  createdAt         DateTime               @default(now())
  updatedAt         DateTime               @updatedAt
  
  // Relationships
  test_results      test_results?
  one_time_codes    one_time_codes?        @relation(fields: [oneTimeCodeId], references: [id])
  test_types        test_types             @relation(fields: [testTypeId], references: [id])
  users             users?                 @relation(fields: [userId], references: [id])
  typing_session    typing_test_sessions?
}
```

#### Test Status Enum
```prisma
enum TestStatus {
  STARTED    # Test session initiated
  PAUSED     # Test temporarily suspended
  COMPLETED  # Test finished successfully
  CANCELLED  # Test terminated before completion
}
```

#### Test Results
Comprehensive scoring and analytics for completed tests.

```prisma
model test_results {
  id                 String   @id @default(cuid())
  testId             String   @unique
  userId             String
  testTypeId         String
  score              Float                 # Primary score (0-100 or specific metric)
  gradeLevelScore    String?               # Grade level equivalent (e.g., "8.5")
  timeToComplete     Int?                  # Duration in seconds
  accuracy           Float?                # Accuracy percentage
  weightedSpeed      Float?                # For typing tests (weighted WPM)
  rawSpeed           Float?                # For typing tests (raw WPM)
  languageScore      Float?                # For English tests
  comprehensionScore Float?                # For reading comprehension
  difficultyLevel    Int?                  # Adaptive test final difficulty
  questionsCorrect   Int?                  # Number of correct answers
  questionsTotal     Int?                  # Total questions attempted
  detailedResults    Json?                 # Test-specific detailed metrics
  completedAt        DateTime
  createdAt          DateTime @default(now())
  
  // Relationships
  tests              tests    @relation(fields: [testId], references: [id], onDelete: Cascade)
  users              users    @relation(fields: [userId], references: [id])
}
```

### 4. Question Management

#### Questions Table
Flexible question storage supporting multiple question types and metadata.

```prisma
model questions {
  id              String       @id @default(cuid())
  testTypeId      String
  content         String                   # Question text or passage
  questionType    QuestionType
  options         Json?                    # Multiple choice options
  correctAnswer   Json                     # Correct answer(s)
  explanation     String?                  # Answer explanation
  difficultyLevel Int?                     # 1-10 scale
  gradeLevel      String?                  # Target grade level
  metadata        Json?                    # Additional question data
  timesAsked      Int          @default(0) # Usage tracking
  timesCorrect    Int          @default(0) # Success rate tracking
  isActive        Boolean      @default(true)
  createdBy       String       @default("AI")
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt
  
  // Typing test specific fields
  passageText     String?                  # For keyboard typing passages
  expectedText    String?                  # For 10-key sequences
  wordCount       Int?                     # Words in passage
  characterCount  Int?                     # Total characters
  
  // Relationships
  test_types      test_types   @relation(fields: [testTypeId], references: [id])
}
```

#### Question Types Enum
```prisma
enum QuestionType {
  MULTIPLE_CHOICE        # Standard multiple choice
  TRUE_FALSE            # Boolean questions
  FILL_IN_BLANK         # Text input questions
  TYPING_PASSAGE        # Keyboard typing test
  TYPING_SEQUENCE       # 10-key numeric sequence
  SIMULATION            # Interactive simulations
  READING_COMPREHENSION # Reading with comprehension questions
  MATH                  # Mathematical problems
}
```

### 5. Access Control System

#### User Test Access
Granular permissions system for test access management.

```prisma
model user_test_access {
  id         String     @id @default(cuid())
  userId     String
  testTypeId String
  accessType AccessType @default(NONE)
  grantedBy  String?                    # Admin who granted access
  grantedAt  DateTime?
  expiresAt  DateTime?                  # Optional expiration
  isActive   Boolean    @default(true)
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt
  
  // Relationships
  test_types test_types @relation(fields: [testTypeId], references: [id])
  users      users      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, testTypeId])         # One access record per user per test type
}
```

#### Access Types Enum
```prisma
enum AccessType {
  NONE          # No access to test
  PRACTICE_ONLY # Practice mode only
  ONE_TIME      # Single official test attempt
  UNLIMITED     # Unlimited official test attempts
}
```

#### One-Time Codes
Temporary access codes for test distribution.

```prisma
model one_time_codes {
  id         String     @id @default(cuid())
  code       String     @unique              # 8-character alphanumeric code
  testTypeId String
  createdBy  String                          # Admin who created code
  usedBy     String?                         # User who redeemed code
  usedAt     DateTime?                       # Redemption timestamp
  isActive   Boolean    @default(true)
  createdAt  DateTime   @default(now())
  expiresAt  DateTime?                       # Optional expiration
  
  // Relationships
  test_types test_types @relation(fields: [testTypeId], references: [id])
  tests      tests[]
}
```

#### Test Requests
User requests for test access with approval workflow.

```prisma
model test_requests {
  id         String        @id @default(cuid())
  userId     String
  testTypeId String
  status     RequestStatus @default(PENDING)
  reason     String?                         # User's request reason
  reviewedBy String?                         # Admin who reviewed
  reviewedAt DateTime?
  response   String?                         # Admin's response
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt
  
  // Relationships
  test_types test_types    @relation(fields: [testTypeId], references: [id])
  users      users         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, testTypeId])             # One request per user per test type
}
```

#### Request Status Enum
```prisma
enum RequestStatus {
  PENDING   # Awaiting admin review
  APPROVED  # Request approved and access granted
  DENIED    # Request denied by admin
}
```

### 6. Specialized Test Systems

#### Typing Test Sessions
Real-time typing test tracking with keystroke analytics.

```prisma
model typing_test_sessions {
  id                String    @id @default(cuid())
  testId            String    @unique
  userId            String
  testType          String              # "keyboarding" or "10-key"
  passageText       String              # Text to be typed
  expectedText      String              # Expected input
  startedAt         DateTime  @default(now())
  completedAt       DateTime?
  timeElapsed       Int?                # Duration in seconds
  
  // Real-time tracking
  currentPosition   Int       @default(0)    # Character position
  currentWordIndex  Int       @default(0)    # Word position
  typedText         String    @default("")   # Current typed text
  keystrokeLog      Json[]    @default([])   # Keystroke timing data
  
  // Results calculation
  totalCharacters   Int       @default(0)
  correctCharacters Int       @default(0)
  incorrectCharacters Int     @default(0)
  totalWords        Int       @default(0)
  correctWords      Int       @default(0)
  wpm               Float     @default(0)    # Words per minute
  accuracy          Float     @default(0)    # Accuracy percentage
  weightedWpm       Float     @default(0)    # Accuracy-adjusted WPM
  
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  
  // Relationships
  tests             tests     @relation(fields: [testId], references: [id], onDelete: Cascade)
  users             users     @relation(fields: [userId], references: [id])
}
```

#### Math Test System
Adaptive mathematics testing with grade-level progression.

```prisma
model MathTest {
  id                      String             @id @default(cuid())
  userId                  String
  status                  TestStatus         @default(STARTED)
  currentGradeLevel       Int                @default(5)
  incorrectAnswersByGrade Json               @default("{}")
  correctAnswersByGrade   Json               @default("{}")
  finalScore              Int?               # Final grade level achieved
  startedAt               DateTime           @default(now())
  pausedAt                DateTime?
  completedAt             DateTime?
  isPractice              Boolean            @default(false)
  
  // Relationships
  user                    users              @relation(fields: [userId], references: [id])
  mathTestQuestions       MathTestQuestion[]
}

model MathQuestion {
  id                String             @id @default(cuid())
  questionText      String
  answer            String
  gradeLevel        Int                # 5-12 grade levels
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  mathTestQuestions MathTestQuestion[]
}

model MathTestQuestion {
  id             String       @id @default(cuid())
  mathTestId     String
  mathQuestionId String
  userAnswer     String?
  isCorrect      Boolean?
  timestamp      DateTime     @default(now())
  
  // Relationships
  mathTest       MathTest     @relation(fields: [mathTestId], references: [id])
  mathQuestion   MathQuestion @relation(fields: [mathQuestionId], references: [id])
}
```

### 7. System Configuration

#### Application Settings
Global system configuration and feature flags.

```prisma
model app_settings {
  id                     String   @id @default(cuid())
  twoFactorEnabled       Boolean  @default(false)
  customSignatureEnabled Boolean  @default(false)
  signatureName          String?
  signatureTitle         String?
  signatureImage         String?
  testPausingEnabled     Boolean  @default(true)
  pdfDownloadEnabled     Boolean  @default(true)
  practiceTestEnabled    Boolean  @default(true)
  aiServiceProvider      String   @default("abacusai")
  aiServiceApiKey        String?
  aiServiceModel         String   @default("gpt-4.1-mini")
  aiServiceEnabled       Boolean  @default(true)
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt
}
```

#### Practice Test Configuration
Customizable settings for practice tests by test type.

```prisma
model practice_test_config {
  id               String     @id @default(cuid())
  testTypeId       String     @unique
  questionCount    Int        @default(5)      # Number of practice questions
  timeLimit        Int?                        # Optional time limit in minutes
  minBankQuestions Int        @default(0)      # Minimum questions needed in bank
  createdAt        DateTime   @default(now())
  updatedAt        DateTime   @updatedAt
  
  // Relationships
  test_types       test_types @relation(fields: [testTypeId], references: [id])
}
```

#### Admin Actions Audit Log
Comprehensive logging of administrative actions for compliance and auditing.

```prisma
model admin_actions {
  id          String          @id @default(cuid())
  adminUserId String                          # Admin who performed action
  action      AdminActionType                 # Type of action performed
  targetId    String?                         # ID of affected entity
  details     Json?                           # Action-specific details
  createdAt   DateTime        @default(now())
  
  // Relationships
  users       users           @relation(fields: [adminUserId], references: [id])
}

enum AdminActionType {
  USER_CREATED      # New user account created
  USER_UPDATED      # User profile modified
  USER_DELETED      # User account deleted
  TEST_CANCELLED    # Test session cancelled
  TEST_DELETED      # Test record deleted
  ACCESS_GRANTED    # Test access granted
  ACCESS_REVOKED    # Test access revoked
  PASSWORD_RESET    # Password reset initiated
  SETTINGS_UPDATED  # System settings modified
}
```

## Key Database Features

### 1. Data Integrity
- **Foreign Key Constraints**: Maintain referential integrity
- **Unique Constraints**: Prevent duplicate critical data
- **Cascade Deletes**: Automatic cleanup of related records
- **Check Constraints**: Validate data ranges and formats

### 2. Performance Optimization
```sql
-- Strategic indexes for common queries
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_tests_user_status ON tests(userId, status);
CREATE INDEX idx_test_results_completed ON test_results(completedAt);
CREATE INDEX idx_user_access_active ON user_test_access(userId, isActive);
```

### 3. Security Features
- **Encrypted Passwords**: bcrypt hashing with salt
- **Token-based Authentication**: JWT session management
- **Audit Trails**: Complete action logging
- **Data Anonymization**: Capability for GDPR compliance

### 4. Scalability Considerations
- **Partitioning Strategy**: Large tables partitioned by date
- **Read Replicas**: Separate read and write operations
- **Connection Pooling**: Efficient database connections
- **Query Optimization**: Efficient ORM queries with Prisma

## Migration Strategy

### Schema Evolution
```typescript
// Example migration for adding new test type
generator client {
  provider = "prisma-client-js"
}

// Add new test type
await prisma.test_types.create({
  data: {
    name: 'advanced-math',
    displayName: 'Advanced Mathematics',
    description: 'College-level mathematics assessment',
    isActive: true
  }
});
```

### Backup and Recovery
- **Automated Backups**: Daily PostgreSQL backups
- **Point-in-time Recovery**: Transaction log archival
- **Migration Rollbacks**: Version-controlled schema changes
- **Data Validation**: Integrity checks after migrations

---

*This comprehensive database schema provides the foundation for secure, scalable, and maintainable testing platform operations.*