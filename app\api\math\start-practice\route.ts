
import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { requireAuth } from '@/lib/auth';

export async function POST(req: Request) {
  try {
    const user = await requireAuth();
    const userId = user.id;

    // 1. Check if there are at least 5 questions in the bank.
    const questionCount = await prisma.mathQuestion.count();
    if (questionCount < 5) {
      return NextResponse.json({ error: 'Not enough questions in the bank to start a practice test.' }, { status: 400 });
    }

    // 2. Get 5 questions, starting from the lowest grade level and moving up.
    const questions = [];
    const gradeLevels = await prisma.mathQuestion.groupBy({
      by: ['gradeLevel'],
      orderBy: {
        gradeLevel: 'asc',
      },
    });

    for (const grade of gradeLevels) {
      if (questions.length >= 5) break;

      const questionsAtGrade = await prisma.mathQuestion.findMany({
        where: { gradeLevel: grade.gradeLevel },
      });

      for (const question of questionsAtGrade) {
        if (questions.length < 5) {
          questions.push(question);
        } else {
          break;
        }
      }
    }


    // 3. Create a new practice MathTest
    const test = await prisma.mathTest.create({
      data: {
        userId,
        isPractice: true,
        status: 'STARTED',
        mathTestQuestions: {
          create: questions.map(q => ({
            mathQuestionId: q.id,
          })),
        }
      },
      include: {
        mathTestQuestions: {
          include: {
            mathQuestion: true,
          }
        }
      }
    });

    const practiceQuestions = test.mathTestQuestions.map(qt => qt.mathQuestion);

    return NextResponse.json({ testId: test.id, questions: practiceQuestions });
  } catch (error) {
    console.error("Error starting practice test:", error);
    return NextResponse.json({ error: 'Something went wrong.' }, { status: 500 });
  }
}
