import { NextRequest } from 'next/server';
import { prisma } from '@/lib/db';
import { requireAuth } from '@/lib/auth';

export async function GET(req: NextRequest) {
  try {
    const user = await requireAuth();
    
    // Get query parameters for pagination, sorting, and filtering
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const sortBy = searchParams.get('sortBy') || 'startedAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const jobTitle = searchParams.get('jobTitle') || '';
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');
    
    // Build where clause
    const where: any = {
      userId: user.id,
      status: 'COMPLETED',
    };
    
    // Add job title filter if provided
    if (jobTitle) {
      where.test_types = {
        displayName: {
          contains: jobTitle,
          mode: 'insensitive'
        }
      };
    }
    
    // Add date range filter if provided
    if (dateFrom || dateTo) {
      where.startedAt = {};
      if (dateFrom) {
        where.startedAt.gte = new Date(dateFrom);
      }
      if (dateTo) {
        where.startedAt.lte = new Date(dateTo);
      }
    }
    
    // Build orderBy clause
    const orderBy: any = {};
    if (sortBy === 'startedAt' || sortBy === 'score') {
      orderBy[sortBy] = sortOrder;
    } else {
      orderBy.startedAt = 'desc'; // default
    }
    
    // Fetch tests with pagination
    const [tests, total] = await Promise.all([
      prisma.tests.findMany({
        where,
        include: {
          test_types: true,
          test_results: true
        },
        orderBy,
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.tests.count({ where })
    ]);
    
    // Transform data for the frontend
    const formattedTests = tests.map((test: any) => {
      const duration = test.completedAt 
        ? Math.floor(((test.completedAt as Date).getTime() - (test.startedAt as Date).getTime()) / 1000) 
        : 0;
      
      return {
        id: test.id,
        testDate: test.startedAt,
        jobTitle: test.test_types.displayName,
        finalScore: test.test_results?.score ?? test.score ?? null,
        testType: test.isPractice ? 'Practice Interview' : 'Full Interview',
        testDuration: duration, // in seconds
        status: test.status
      };
    });
    
    return Response.json({
      tests: formattedTests,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching test history:', error);
    return Response.json({ error: 'Failed to fetch test history' }, { status: 500 });
  }
}