# Project Overview

This is a Next.js application that appears to be a testing platform. It uses NextAuth.js for authentication, Prisma as the ORM, and a PostgreSQL database. The frontend is built with React, TypeScript, and Tailwind CSS, and it uses a variety of UI components from Radix UI and other libraries.

The application seems to have a comprehensive feature set, including:

*   User authentication and authorization with different roles (USER and ADMIN).
*   A testing system with different types of tests, including typing tests and math tests.
*   A question bank with various question types.
*   Test requests and access management.
*   Admin dashboard for managing users, tests, and application settings.
*   User dashboard for taking tests and viewing results.

# Building and Running

To build and run the project, you can use the following commands:

```bash
# Install dependencies
yarn install

# Run the development server
yarn dev

# Create a production build
yarn build

# Start the production server
yarn start

# Lint the code
yarn lint

# Seed the database
yarn prisma db seed
```

**TODO:** The `prisma` seed script is defined in `package.json` as `tsx --require dotenv/config scripts/seed.ts`. You may need to create a `.env` file and set the `DATABASE_URL` environment variable for the seed script to work.

# Development Conventions

*   **Authentication:** The application uses NextAuth.js for authentication. The configuration is in `lib/auth-config.ts` and the helper functions are in `lib/auth.ts`.
*   **Database:** The project uses Prisma as the ORM. The database schema is defined in `prisma/schema.prisma`. To interact with the database, you can use the Prisma Client.
*   **UI:** The UI is built with React, TypeScript, and Tailwind CSS. It uses a variety of UI components from Radix UI and other libraries. The components are located in the `components` directory.
*   **API:** The application has a number of API routes in the `app/api` directory. These routes are used to handle various backend tasks, such as user management, test administration, and AI-powered question generation.
*   **Testing:** There is no testing framework configured in the `package.json` file. You may want to add a testing framework like Jest or Vitest to the project.
g.ts`, and helper functions are in `lib/auth.ts`.
*   **Database:** The database schema is defined in `prisma/schema.prisma`. Changes to the schema should be made by editing this file and running `npx prisma migrate dev`.
