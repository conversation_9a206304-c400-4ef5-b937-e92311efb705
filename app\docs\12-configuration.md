# Configuration Documentation

## Overview

The Rubicon Programs Testing Application uses a comprehensive configuration system that supports environment-specific settings, feature flags, and runtime configuration. This document covers all configuration options, environment variables, and settings management.

## Environment Configuration

### Core Environment Variables

#### Database Configuration
```env
# Primary database connection
DATABASE_URL="postgresql://username:password@localhost:5432/rubicon_testing_db?schema=public"

# Connection pool settings (optional)
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=20
DATABASE_TIMEOUT=30000

# SSL configuration for production
DATABASE_SSL_MODE="require"
DATABASE_SSL_CERT_PATH="/path/to/cert.pem"
DATABASE_SSL_KEY_PATH="/path/to/key.pem"
DATABASE_SSL_CA_PATH="/path/to/ca.pem"
```

#### Authentication Configuration
```env
# NextAuth.js settings
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-key-minimum-32-characters"
NEXTAUTH_JWT_SECRET="jwt-specific-secret-key-for-token-signing"

# Session configuration
SESSION_TIMEOUT_MINUTES=1440      # 24 hours
SESSION_REFRESH_THRESHOLD=300     # 5 minutes before expiry
MAX_CONCURRENT_SESSIONS=3         # Per user

# Password policy
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SYMBOLS=true
PASSWORD_HISTORY_COUNT=5          # Prevent reusing last 5 passwords
```

#### Email Service Configuration
```env
# SMTP settings
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_SECURE=false         # true for 465, false for other ports
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-specific-password"

# Email addresses
EMAIL_FROM="<EMAIL>"
EMAIL_FROM_NAME="Rubicon Programs"
EMAIL_REPLY_TO="<EMAIL>"
EMAIL_BCC_ALL=""                  # Optional BCC for all emails

# Email template settings
EMAIL_TEMPLATE_DIR="./email-templates"
EMAIL_LOGO_URL="https://your-domain.com/logo.png"
EMAIL_FOOTER_TEXT="© 2024 Rubicon Programs. All rights reserved."
```

### Application Configuration

#### Feature Flags
```env
# Core features
FEATURE_TWO_FACTOR_AUTH=true
FEATURE_TEST_PAUSING=true
FEATURE_PDF_DOWNLOAD=true
FEATURE_PRACTICE_TESTS=true
FEATURE_CERTIFICATE_GENERATION=true

# Advanced features
FEATURE_AI_QUESTION_GENERATION=false
FEATURE_ADAPTIVE_TESTING=true
FEATURE_REAL_TIME_ANALYTICS=true
FEATURE_WEBHOOK_INTEGRATION=false
FEATURE_API_ACCESS=true

# Administrative features
FEATURE_USER_IMPERSONATION=true
FEATURE_BULK_USER_OPERATIONS=true
FEATURE_ADVANCED_REPORTING=true
FEATURE_CUSTOM_BRANDING=false
```

#### Test Configuration
```env
# Default test settings
DEFAULT_TEST_TIMEOUT_MINUTES=60
DEFAULT_TYPING_TEST_DURATION=5
DEFAULT_MATH_TEST_QUESTIONS=20
DEFAULT_PRACTICE_QUESTION_COUNT=10

# Test security
TEST_SESSION_ENCRYPTION_KEY="32-character-encryption-key-here"
TEST_INTEGRITY_CHECK=true
TEST_ANOMALY_DETECTION=true
TEST_IP_RESTRICTION=false

# Question generation
QUESTION_CACHE_SIZE=1000
QUESTION_GENERATION_BATCH_SIZE=50
QUESTION_VALIDATION_REQUIRED=true
```

#### AI Service Configuration
```env
# AI service provider
AI_SERVICE_PROVIDER="openai"      # openai, abacus, custom
AI_SERVICE_ENABLED=true
AI_SERVICE_API_KEY="your-ai-service-api-key"
AI_SERVICE_MODEL="gpt-4"
AI_SERVICE_MAX_TOKENS=2048
AI_SERVICE_TEMPERATURE=0.7

# AI service endpoints (for custom providers)
AI_SERVICE_BASE_URL="https://api.custom-provider.com"
AI_SERVICE_TIMEOUT=30000          # 30 seconds
AI_SERVICE_RETRY_ATTEMPTS=3
```

### Security Configuration

#### Encryption & Secrets
```env
# Application encryption keys
ENCRYPTION_KEY="your-32-character-encryption-key-here"
HASH_SECRET="your-hash-secret-for-data-integrity"
API_SECRET_KEY="your-api-authentication-secret"

# JWT configuration
JWT_SIGNING_ALGORITHM="HS256"
JWT_ISSUER="rubicon-programs"
JWT_AUDIENCE="testing-application"
JWT_EXPIRY="24h"

# Rate limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW_MS=900000       # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100       # Per window per IP
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false
```

#### CORS & Security Headers
```env
# CORS settings
CORS_ORIGIN="https://your-domain.com,https://admin.your-domain.com"
CORS_METHODS="GET,POST,PUT,DELETE,OPTIONS"
CORS_CREDENTIALS=true

# Security headers
SECURITY_HSTS_ENABLED=true
SECURITY_CSP_ENABLED=true
SECURITY_XFRAME_OPTIONS="DENY"
SECURITY_XSS_PROTECTION=true
```

### File Storage Configuration

#### Upload Settings
```env
# File upload configuration
UPLOAD_DIR="./public/uploads"
UPLOAD_MAX_FILE_SIZE=5242880      # 5MB in bytes
UPLOAD_ALLOWED_TYPES="image/jpeg,image/png,application/pdf"
UPLOAD_VIRUS_SCANNING=false       # Enable for production

# Cloud storage (optional)
CLOUD_STORAGE_PROVIDER="aws"      # aws, gcp, azure
AWS_REGION="us-east-1"
AWS_BUCKET_NAME="rubicon-uploads"
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
```

#### CDN Configuration
```env
# Content Delivery Network
CDN_ENABLED=false
CDN_BASE_URL="https://cdn.your-domain.com"
CDN_CACHE_CONTROL="public, max-age=31536000"  # 1 year
```

### Monitoring & Analytics

#### Logging Configuration
```env
# Application logging
LOG_LEVEL="info"                  # error, warn, info, debug
LOG_FORMAT="json"                 # json, text
LOG_DESTINATION="file"            # console, file, both
LOG_FILE_PATH="./logs/application.log"
LOG_MAX_SIZE="10MB"
LOG_MAX_FILES=5

# Database query logging
DB_LOG_QUERIES=false
DB_LOG_SLOW_QUERIES=true
DB_SLOW_QUERY_THRESHOLD=1000      # milliseconds
```

#### Analytics & Monitoring
```env
# Application monitoring
MONITORING_ENABLED=true
HEALTH_CHECK_ENDPOINT="/api/health"
METRICS_ENDPOINT="/api/metrics"

# External monitoring services
NEWRELIC_LICENSE_KEY=""           # Optional New Relic integration
DATADOG_API_KEY=""               # Optional DataDog integration
SENTRY_DSN=""                    # Optional Sentry error tracking

# Performance monitoring
PERFORMANCE_MONITORING=true
API_RESPONSE_TIME_THRESHOLD=2000  # milliseconds
DATABASE_RESPONSE_TIME_THRESHOLD=1000
```

## Application Settings

### Database Configuration Table

The application uses a `app_settings` table for runtime configuration:

```sql
CREATE TABLE app_settings (
    id SERIAL PRIMARY KEY,
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT NOT NULL,
    value_type VARCHAR(50) NOT NULL, -- 'string', 'number', 'boolean', 'json'
    description TEXT,
    category VARCHAR(100),
    is_public BOOLEAN DEFAULT FALSE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(255)
);
```

### Default Application Settings

```typescript
interface ApplicationSettings {
  // User Management
  allowSelfRegistration: boolean;
  requireEmailVerification: boolean;
  defaultUserAccess: 'NONE' | 'PRACTICE_ONLY';
  
  // Test Configuration
  defaultTestTimeout: number;        // minutes
  allowTestPausing: boolean;
  showTestTimer: boolean;
  maxRetakeAttempts: number;
  
  // Feature Toggles
  enableTwoFactorAuth: boolean;
  enablePracticeTests: boolean;
  enableCertificateDownload: boolean;
  enableAIQuestionGeneration: boolean;
  
  // UI Customization
  applicationName: string;
  primaryColor: string;
  logoUrl: string;
  customFooterText: string;
  
  // Email Settings
  emailEnabled: boolean;
  welcomeEmailEnabled: boolean;
  testReminderEnabled: boolean;
  
  // Security Settings
  sessionTimeoutMinutes: number;
  passwordExpiryDays: number;
  maxLoginAttempts: number;
  
  // Analytics
  enableUsageTracking: boolean;
  enablePerformanceMetrics: boolean;
  dataRetentionDays: number;
}
```

### Settings Management API

```typescript
// Get application settings
export async function getApplicationSettings(): Promise<ApplicationSettings> {
  const settings = await prisma.app_settings.findMany();
  
  return settings.reduce((acc, setting) => {
    let value: any = setting.value;
    
    switch (setting.value_type) {
      case 'boolean':
        value = setting.value === 'true';
        break;
      case 'number':
        value = parseInt(setting.value, 10);
        break;
      case 'json':
        value = JSON.parse(setting.value);
        break;
    }
    
    acc[setting.key] = value;
    return acc;
  }, {} as ApplicationSettings);
}

// Update application setting
export async function updateApplicationSetting(
  key: string, 
  value: any, 
  updatedBy: string
): Promise<boolean> {
  try {
    const valueType = typeof value;
    const stringValue = valueType === 'object' ? 
      JSON.stringify(value) : 
      String(value);
    
    await prisma.app_settings.upsert({
      where: { key },
      update: {
        value: stringValue,
        value_type: valueType === 'object' ? 'json' : valueType,
        updated_at: new Date(),
        updated_by: updatedBy
      },
      create: {
        key,
        value: stringValue,
        value_type: valueType === 'object' ? 'json' : valueType,
        updated_by: updatedBy
      }
    });
    
    return true;
  } catch (error) {
    console.error('Failed to update setting:', error);
    return false;
  }
}
```

## Test Type Configuration

### Test Type Settings
```typescript
interface TestTypeConfig {
  id: string;
  name: string;
  displayName: string;
  description: string;
  isActive: boolean;
  
  // Test behavior
  defaultTimeLimit?: number;        // minutes
  allowPausing: boolean;
  showProgress: boolean;
  showTimer: boolean;
  
  // Question settings
  questionCount?: number;           // for fixed-length tests
  adaptiveTesting: boolean;
  difficultyRange: [number, number]; // [min, max] difficulty
  
  // Scoring
  passingScore: number;             // minimum score to pass
  maxScore: number;                 // maximum possible score
  scoringMethod: 'PERCENTAGE' | 'GRADE_LEVEL' | 'WPM' | 'CUSTOM';
  
  // Access control
  requiresAccess: boolean;
  practiceAvailable: boolean;
  certificateEligible: boolean;
  
  // Retake policy
  retakePolicy: 'UNLIMITED' | 'LIMITED' | 'NONE';
  maxRetakes?: number;
  retakeCooldownHours?: number;
}

// Example test type configurations
const testTypeConfigs: TestTypeConfig[] = [
  {
    id: 'typing-keyboard',
    name: 'typing-keyboard',
    displayName: 'Keyboard Typing',
    description: 'Assess typing speed and accuracy on standard keyboard',
    isActive: true,
    defaultTimeLimit: 5,
    allowPausing: false,
    showProgress: true,
    showTimer: true,
    adaptiveTesting: false,
    passingScore: 25,               // 25 WPM minimum
    maxScore: 100,
    scoringMethod: 'WPM',
    requiresAccess: true,
    practiceAvailable: true,
    certificateEligible: true,
    retakePolicy: 'UNLIMITED'
  },
  
  {
    id: 'basic-math',
    name: 'basic-math',
    displayName: 'Basic Mathematics',
    description: 'Mathematical skills from 5th to 12th grade level',
    isActive: true,
    defaultTimeLimit: 60,
    allowPausing: true,
    showProgress: true,
    showTimer: true,
    adaptiveTesting: true,
    difficultyRange: [5, 12],       // Grade levels
    passingScore: 5.0,              // 5th grade minimum
    maxScore: 12.0,
    scoringMethod: 'GRADE_LEVEL',
    requiresAccess: true,
    practiceAvailable: true,
    certificateEligible: true,
    retakePolicy: 'LIMITED',
    maxRetakes: 2,
    retakeCooldownHours: 48
  }
];
```

## Email Template Configuration

### Template Settings
```typescript
interface EmailTemplateConfig {
  // Welcome email
  welcomeEmail: {
    enabled: boolean;
    subject: string;
    includeLoginInstructions: boolean;
    includeTestingInformation: boolean;
  };
  
  // Verification email
  verificationEmail: {
    subject: string;
    linkExpiryHours: number;
    customMessage?: string;
  };
  
  // Password reset
  passwordResetEmail: {
    subject: string;
    linkExpiryHours: number;
    includeSecurityTips: boolean;
  };
  
  // Test notifications
  testNotifications: {
    accessGranted: {
      enabled: boolean;
      subject: string;
      includeInstructions: boolean;
    };
    
    testCompleted: {
      enabled: boolean;
      subject: string;
      includeCertificateLink: boolean;
    };
    
    testReminder: {
      enabled: boolean;
      subject: string;
      reminderDays: number[];        // [7, 3, 1] days before expiry
    };
  };
  
  // Global settings
  global: {
    senderName: string;
    organizationName: string;
    logoUrl: string;
    footerText: string;
    unsubscribeEnabled: boolean;
  };
}
```

## Integration Configuration

### Webhook Configuration
```env
# Webhook settings
WEBHOOK_ENABLED=false
WEBHOOK_SECRET="your-webhook-secret-key"
WEBHOOK_RETRY_ATTEMPTS=3
WEBHOOK_TIMEOUT=30000             # 30 seconds

# Webhook endpoints
WEBHOOK_USER_REGISTERED=""        # External URL for user registration events
WEBHOOK_TEST_COMPLETED=""         # External URL for test completion events
WEBHOOK_CERTIFICATE_GENERATED=""  # External URL for certificate events
```

### API Integration Settings
```typescript
interface APIIntegrationConfig {
  // External LMS integration
  lmsIntegration: {
    enabled: boolean;
    provider: 'moodle' | 'canvas' | 'blackboard' | 'custom';
    apiEndpoint: string;
    apiKey: string;
    syncUserData: boolean;
    syncTestResults: boolean;
  };
  
  // HR system integration
  hrIntegration: {
    enabled: boolean;
    provider: 'workday' | 'bamboohr' | 'adp' | 'custom';
    apiEndpoint: string;
    authentication: 'bearer' | 'basic' | 'oauth2';
    credentials: any;
    syncEmployeeData: boolean;
  };
  
  // Analytics platforms
  analyticsIntegration: {
    googleAnalytics: {
      enabled: boolean;
      trackingId: string;
      anonymizeIp: boolean;
    };
    
    customAnalytics: {
      enabled: boolean;
      endpoint: string;
      batchSize: number;
      flushInterval: number;       // milliseconds
    };
  };
}
```

## Performance Configuration

### Caching Settings
```env
# Redis cache configuration
REDIS_URL="redis://localhost:6379"
REDIS_PASSWORD=""
REDIS_DB=0
REDIS_TTL=3600                    # 1 hour default TTL

# Application cache settings
CACHE_ENABLED=true
CACHE_USER_SESSIONS=true
CACHE_TEST_QUESTIONS=true
CACHE_APPLICATION_SETTINGS=true
CACHE_STATISTICS=true

# Cache TTL settings (seconds)
CACHE_TTL_SESSIONS=3600           # 1 hour
CACHE_TTL_QUESTIONS=86400         # 24 hours
CACHE_TTL_SETTINGS=300            # 5 minutes
CACHE_TTL_STATISTICS=600          # 10 minutes
```

### Database Performance
```env
# Connection pooling
DB_POOL_MIN=5
DB_POOL_MAX=50
DB_POOL_ACQUIRE_TIMEOUT=60000     # 60 seconds
DB_POOL_IDLE_TIMEOUT=300000       # 5 minutes

# Query optimization
DB_ENABLE_QUERY_CACHE=true
DB_QUERY_TIMEOUT=30000            # 30 seconds
DB_STATEMENT_TIMEOUT=60000        # 60 seconds
DB_LOCK_TIMEOUT=10000            # 10 seconds

# Maintenance settings
DB_AUTO_VACUUM=true
DB_ANALYZE_THRESHOLD=0.1          # 10% of table size
DB_CHECKPOINT_TIMEOUT=300         # 5 minutes
```

## Environment-Specific Configurations

### Development Environment
```env
NODE_ENV="development"
DEBUG="true"
VERBOSE_LOGGING="true"

# Development features
ENABLE_MOCK_SERVICES="true"
ENABLE_TEST_ROUTES="true"
DISABLE_EMAIL_SENDING="true"
DISABLE_RATE_LIMITING="true"

# Development database
DATABASE_URL="postgresql://dev_user:dev_pass@localhost:5432/rubicon_dev"

# Hot reloading
NEXT_DEV_CACHE_DISABLED="true"
FAST_REFRESH="true"
```

### Staging Environment
```env
NODE_ENV="staging"
DEBUG="false"
LOG_LEVEL="info"

# Staging-specific settings
ENABLE_PERFORMANCE_MONITORING="true"
ENABLE_ERROR_TRACKING="true"
DATABASE_POOL_MAX=20

# Email testing
EMAIL_INTERCEPT="<EMAIL>"
EMAIL_PREFIX="[STAGING] "
```

### Production Environment
```env
NODE_ENV="production"
DEBUG="false"
LOG_LEVEL="warn"

# Production security
SECURITY_HEADERS_ENABLED="true"
RATE_LIMITING_STRICT="true"
SESSION_SECURE_COOKIES="true"
DATABASE_SSL_REQUIRED="true"

# Performance optimization
CACHE_AGGRESSIVE="true"
DATABASE_POOL_MAX=100
CDN_ENABLED="true"

# Monitoring
HEALTH_CHECKS_ENABLED="true"
METRICS_COLLECTION="true"
ERROR_TRACKING="true"
```

## Configuration Validation

### Environment Variable Validation
```typescript
import { z } from 'zod';

const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'staging', 'production']),
  DATABASE_URL: z.string().url(),
  NEXTAUTH_SECRET: z.string().min(32),
  NEXTAUTH_URL: z.string().url(),
  
  // Email configuration
  EMAIL_SERVER_HOST: z.string(),
  EMAIL_SERVER_PORT: z.coerce.number(),
  EMAIL_SERVER_USER: z.string().email(),
  EMAIL_FROM: z.string().email(),
  
  // Feature flags
  FEATURE_TWO_FACTOR_AUTH: z.coerce.boolean(),
  FEATURE_PRACTICE_TESTS: z.coerce.boolean(),
  
  // Optional configurations
  AI_SERVICE_ENABLED: z.coerce.boolean().optional(),
  REDIS_URL: z.string().url().optional(),
});

// Validate environment on startup
export const env = envSchema.parse(process.env);
```

### Configuration Health Check
```typescript
export async function validateConfiguration(): Promise<ConfigurationHealth> {
  const health: ConfigurationHealth = {
    database: false,
    email: false,
    ai: false,
    storage: false,
    cache: false,
    errors: []
  };
  
  // Test database connection
  try {
    await prisma.$queryRaw`SELECT 1`;
    health.database = true;
  } catch (error) {
    health.errors.push(`Database connection failed: ${error.message}`);
  }
  
  // Test email configuration
  if (env.EMAIL_SERVER_HOST) {
    try {
      // Test SMTP connection
      health.email = true;
    } catch (error) {
      health.errors.push(`Email service failed: ${error.message}`);
    }
  }
  
  // Additional health checks...
  
  return health;
}
```

---

*This comprehensive configuration documentation covers all aspects of system configuration, from environment variables to runtime settings, ensuring proper setup and maintenance of the Rubicon Programs Testing Application.*