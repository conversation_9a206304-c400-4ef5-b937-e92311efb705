import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { requireAuth } from '@/lib/auth';

const MAX_PAUSE_MINUTES = 60; // limit in minutes

export async function POST(_req: Request, { params }: { params: { id: string } }) {
  try {
    const user = await requireAuth();
    const userId = user.id;
    const id = params.id;

    const test = await prisma.mathTest.findUnique({ where: { id } });
    if (!test) {
      return NextResponse.json({ error: 'Test not found' }, { status: 404 });
    }
    if (test.userId !== userId) {
      return NextResponse.json({ error: 'Not authorized to modify this test' }, { status: 403 });
    }
    if (test.status !== 'PAUSED' || !test.pausedAt) {
      return NextResponse.json({ error: 'Test is not paused' }, { status: 400 });
    }

    const now = new Date();
    const pausedAt = test.pausedAt as unknown as Date;
    const pausedMs = now.getTime() - pausedAt.getTime();

    if (pausedMs > MAX_PAUSE_MINUTES * 60 * 1000) {
      console.log(`[analytics] MATH_TEST_RESUME_DENIED_MAX_PAUSE testId=${id} userId=${userId} pausedMs=${pausedMs}`);
      return NextResponse.json({ error: `Pause exceeds maximum allowed duration (${MAX_PAUSE_MINUTES} minutes)` }, { status: 400 });
    }

    const newStartedAt = new Date((test.startedAt as unknown as Date).getTime() + pausedMs);

    const updated = await prisma.mathTest.update({
      where: { id },
      data: { pausedAt: null, status: 'STARTED', startedAt: newStartedAt },
    });

    console.log(`[analytics] MATH_TEST_RESUMED testId=${id} userId=${userId} pausedMs=${pausedMs}`);

    return NextResponse.json({
      id: updated.id,
      status: updated.status,
      pausedAt: updated.pausedAt,
      startedAt: updated.startedAt,
    });
  } catch (error) {
    console.error('Error resuming test:', error);
    return NextResponse.json({ error: 'Something went wrong.' }, { status: 500 });
  }
}
