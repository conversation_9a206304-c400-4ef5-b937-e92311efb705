import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import EnglishTestService from '@/lib/services/english-test-service';

interface EnglishTestSession {
  testId: string;
  userId: string;
  currentGradeLevel: number;
  startingGradeLevel: number;
  questionsAnswered: {
    questionId: string;
    isCorrect: boolean;
    gradeLevel: number;
    questionType: string;
    timeSpent: number;
    answer: string;
  }[];
  currentPassageId?: string;
  passageQuestions: string[];
  adaptiveState: {
    correctByGrade: Record<number, number>;
    totalByGrade: Record<number, number>;
    consecutiveCorrect: number;
    consecutiveIncorrect: number;
  };
  runningScores: {
    languageMastery: number;
    readingComprehension: number;
    overall: number;
  };
  isPassageViewingLocked: boolean;
  startedAt: Date;
  lastActivityAt: Date;
}

// POST /api/tests/english/answer - Submit answer for current question
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { testId, questionId, answer, timeSpent = 0 } = body;

    if (!testId || !questionId || answer === undefined) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Get test session
    const test = await prisma.tests.findFirst({
      where: {
        id: testId,
        userId: session.user.id,
        status: { in: ['STARTED', 'PAUSED'] },
      },
    });

    if (!test) {
      return NextResponse.json({ error: 'Test session not found' }, { status: 404 });
    }

    // Get the question being answered
    const question = await prisma.english_questions.findUnique({
      where: { id: questionId },
      include: {
        wrong_answers: true,
      },
    });

    if (!question) {
      return NextResponse.json({ error: 'Question not found' }, { status: 404 });
    }

    // Validate answer format based on question type
    let isValidAnswer = true;
    switch (question.question_type) {
      case 'VOCABULARY':
      case 'GRAMMAR':
      case 'PUNCTUATION':
      case 'SPELLING':
      case 'READING_COMPREHENSION':
        // Multiple choice - answer should be one of the options
        const allOptions = [question.correct_answer, ...question.wrong_answers.map(wa => wa.wrong_answer)];
        isValidAnswer = allOptions.includes(answer);
        break;
      default:
        // For other types, accept any string answer
        isValidAnswer = typeof answer === 'string';
    }

    if (!isValidAnswer) {
      return NextResponse.json({ error: 'Invalid answer format' }, { status: 400 });
    }

    // Check if answer is correct
    const isCorrect = question.correct_answer === answer;

    // Get current session data
    const sessionData = test.additionalData as { englishTestSession: EnglishTestSession };
    let englishSession = sessionData?.englishTestSession;

    if (!englishSession) {
      return NextResponse.json({ error: 'Invalid test session data' }, { status: 400 });
    }

    // Update question statistics
    await prisma.english_questions.update({
      where: { id: questionId },
      data: {
        times_used: { increment: 1 },
        times_correct: isCorrect ? { increment: 1 } : undefined,
        updated_at: new Date(),
      },
    });

    // Add answer to session
    const answerRecord = {
      questionId,
      isCorrect,
      gradeLevel: question.grade_level,
      questionType: question.question_type,
      timeSpent,
      answer,
    };

    englishSession.questionsAnswered.push(answerRecord);

    // Update adaptive state
    const gradeLevel = question.grade_level;
    englishSession.adaptiveState.totalByGrade[gradeLevel] = 
      (englishSession.adaptiveState.totalByGrade[gradeLevel] || 0) + 1;

    if (isCorrect) {
      englishSession.adaptiveState.correctByGrade[gradeLevel] = 
        (englishSession.adaptiveState.correctByGrade[gradeLevel] || 0) + 1;
      englishSession.adaptiveState.consecutiveCorrect++;
      englishSession.adaptiveState.consecutiveIncorrect = 0;
    } else {
      englishSession.adaptiveState.consecutiveIncorrect++;
      englishSession.adaptiveState.consecutiveCorrect = 0;
    }

    // Calculate running scores
    const responses = englishSession.questionsAnswered.map(qa => ({
      questionId: qa.questionId,
      isCorrect: qa.isCorrect,
      gradeLevel: qa.gradeLevel,
      questionType: qa.questionType,
    }));

    englishSession.runningScores.languageMastery = 
      EnglishTestService.calculateLanguageMasteryScore(responses);
    englishSession.runningScores.readingComprehension = 
      EnglishTestService.calculateReadingComprehensionScore(responses);
    englishSession.runningScores.overall = 
      (englishSession.runningScores.languageMastery + englishSession.runningScores.readingComprehension) / 2;

    // Determine if we need to lock passage viewing for certain question types
    if (['GRAMMAR', 'PUNCTUATION', 'SPELLING', 'VOCABULARY'].includes(question.question_type)) {
      englishSession.isPassageViewingLocked = true;
    } else {
      englishSession.isPassageViewingLocked = false;
    }

    // Update adaptive grade level
    const previousGradeLevel = englishSession.currentGradeLevel;
    const newGradeLevel = await updateAdaptiveGradeLevel(englishSession, question.grade_level, isCorrect);
    englishSession.currentGradeLevel = newGradeLevel;

    // Update last activity
    englishSession.lastActivityAt = new Date();

    // Check if test should be completed
    const shouldComplete = await shouldCompleteTest(englishSession);
    let nextQuestion = null;
    let testCompleted = false;

    if (shouldComplete) {
      // Complete the test
      testCompleted = true;
      await completeEnglishTest(test.id, englishSession);
    } else {
      // Get next question
      const nextQuestions = await EnglishTestService.generateAdaptiveQuestions(
        englishSession.currentGradeLevel,
        responses
      );

      if (nextQuestions.length > 0) {
        nextQuestion = nextQuestions[0];
        
        // Update current question in test
        await prisma.tests.update({
          where: { id: testId },
          data: {
            currentQuestionId: nextQuestion.id,
            additionalData: { englishTestSession: englishSession },
            updatedAt: new Date(),
          },
        });
      } else {
        // No more questions available, complete test
        testCompleted = true;
        await completeEnglishTest(test.id, englishSession);
      }
    }

    if (!testCompleted) {
      // Update test with new session data
      await prisma.tests.update({
        where: { id: testId },
        data: {
          additionalData: { englishTestSession: englishSession },
          updatedAt: new Date(),
        },
      });
    }

    // Prepare response
    const response: any = {
      success: true,
      data: {
        isCorrect,
        correctAnswer: question.correct_answer,
        explanation: question.explanation,
        gradeLevel: question.grade_level,
        newGradeLevel: newGradeLevel,
        gradeLevelChanged: newGradeLevel !== previousGradeLevel,
        runningScores: englishSession.runningScores,
        questionsAnswered: englishSession.questionsAnswered.length,
        testCompleted,
      }
    };

    // Add next question if not completed
    if (!testCompleted && nextQuestion) {
      const questionData = await prisma.english_questions.findUnique({
        where: { id: nextQuestion.id },
        include: {
          wrong_answers: true,
        },
      });

      if (questionData) {
        response.data.nextQuestion = {
          id: questionData.id,
          questionText: questionData.question_text,
          questionType: questionData.question_type,
          gradeLevel: questionData.grade_level,
          options: [
            questionData.correct_answer,
            ...questionData.wrong_answers.map(wa => wa.wrong_answer)
          ].sort(() => Math.random() - 0.5), // Shuffle options
        };
      }
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error submitting English test answer:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

async function updateAdaptiveGradeLevel(
  session: EnglishTestSession,
  currentQuestionGrade: number,
  isCorrect: boolean
): Promise<number> {
  const currentGrade = session.currentGradeLevel;
  const adaptiveState = session.adaptiveState;

  // Get accuracy at current grade level (need at least 3 questions)
  const totalAtCurrentGrade = adaptiveState.totalByGrade[currentGrade] || 0;
  if (totalAtCurrentGrade < 3) {
    return currentGrade; // Stay at current level
  }

  const correctAtCurrentGrade = adaptiveState.correctByGrade[currentGrade] || 0;
  const accuracy = correctAtCurrentGrade / totalAtCurrentGrade;

  // Adaptive logic based on accuracy and consecutive answers
  if (accuracy >= 0.8 && adaptiveState.consecutiveCorrect >= 2) {
    // Move up (max 2 grade levels)
    return Math.min(currentGrade + 2, 12);
  } else if (accuracy < 0.6 && adaptiveState.consecutiveIncorrect >= 2) {
    // Move down (1 grade level)
    return Math.max(currentGrade - 1, 5);
  }

  return currentGrade; // Stay at current level
}

async function shouldCompleteTest(session: EnglishTestSession): Promise<boolean> {
  const config = await prisma.english_test_config.findFirst();
  if (!config) return false;

  const answered = session.questionsAnswered;
  const languageMasteryCount = answered.filter(q => q.questionType !== 'READING_COMPREHENSION').length;
  const readingCompCount = answered.filter(q => q.questionType === 'READING_COMPREHENSION').length;

  // Check minimum questions requirement
  const hasMinLanguageMastery = languageMasteryCount >= config.min_questions_language_mastery;
  const hasMinReadingComp = readingCompCount >= config.min_questions_reading_comp;

  // Check if we've reached stability in grade level (5 consecutive at same level)
  let consecutiveSameGrade = 0;
  let lastGrade = null;
  
  for (let i = answered.length - 1; i >= 0; i--) {
    if (lastGrade === null) {
      lastGrade = answered[i].gradeLevel;
      consecutiveSameGrade = 1;
    } else if (answered[i].gradeLevel === lastGrade) {
      consecutiveSameGrade++;
    } else {
      break;
    }
  }

  const isStable = consecutiveSameGrade >= 5;

  return hasMinLanguageMastery && hasMinReadingComp && isStable;
}

async function completeEnglishTest(testId: string, session: EnglishTestSession): Promise<void> {
  const responses = session.questionsAnswered.map(qa => ({
    questionId: qa.questionId,
    isCorrect: qa.isCorrect,
    gradeLevel: qa.gradeLevel,
    questionType: qa.questionType,
  }));

  // Calculate final scores
  const languageMasteryScore = EnglishTestService.calculateLanguageMasteryScore(responses);
  const readingComprehensionScore = EnglishTestService.calculateReadingComprehensionScore(responses);
  const overallScore = (languageMasteryScore + readingComprehensionScore) / 2;

  // Determine final grade level
  const finalGradeLevel = EnglishTestService.determineGradeLevel({
    languageMastery: languageMasteryScore,
    readingComprehension: readingComprehensionScore,
  });

  // Update test status
  await prisma.tests.update({
    where: { id: testId },
    data: {
      status: 'COMPLETED',
      completedAt: new Date(),
      score: overallScore,
      gradeLevelScore: finalGradeLevel.toString(),
      additionalData: { englishTestSession: session },
    },
  });

  // Create test results
  await prisma.english_test_results.create({
    data: {
      test_id: testId,
      language_mastery_score: languageMasteryScore,
      reading_comprehension_score: readingComprehensionScore,
      overall_score: overallScore,
      vocabulary_score: calculateSubScore(responses, 'VOCABULARY'),
      spelling_score: calculateSubScore(responses, 'SPELLING'),
      grammar_score: calculateSubScore(responses, 'GRAMMAR'),
      punctuation_score: calculateSubScore(responses, 'PUNCTUATION'),
      grade_level_achieved: finalGradeLevel,
    },
  });
}

function calculateSubScore(responses: any[], questionType: string): number {
  const typeResponses = responses.filter(r => r.questionType === questionType);
  if (typeResponses.length === 0) return 0;
  
  const correct = typeResponses.filter(r => r.isCorrect).length;
  return (correct / typeResponses.length) * 100;
}
