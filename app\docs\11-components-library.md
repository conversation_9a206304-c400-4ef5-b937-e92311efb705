# Components Library Documentation

## Overview

The Rubicon Programs Testing Application uses a comprehensive component library built on **Radix UI primitives** and styled with **Tailwind CSS**. This design system ensures consistency, accessibility, and maintainability across the entire application.

## Component Architecture

### Design System Foundation
```typescript
// Base design tokens
export const designTokens = {
  colors: {
    primary: {
      50: '#eff6ff',
      500: '#3b82f6',
      900: '#1e3a8a'
    },
    secondary: {
      50: '#f8fafc',
      500: '#64748b',
      900: '#0f172a'
    },
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444'
  },
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['Fira Code', 'monospace']
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem'
    }
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem'
  }
};
```

### Component Categories
- **Base Components**: Fundamental UI building blocks
- **Form Components**: Input elements and form controls
- **Layout Components**: Structural and positioning elements
- **Navigation Components**: Menu and navigation elements
- **Feedback Components**: Alerts, toasts, and status indicators
- **Data Display**: Tables, cards, and data visualization
- **Testing Components**: Specialized test-taking interfaces

## Base Components

### Button Component
```typescript
interface ButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  disabled?: boolean;
  loading?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

// Usage Examples
<Button variant="default" size="lg">
  Start Test
</Button>

<Button variant="outline" disabled>
  Not Available
</Button>

<Button variant="destructive" loading>
  Deleting...
</Button>
```

**Button Variants:**
- `default`: Primary action button (blue background)
- `destructive`: Dangerous actions (red background)
- `outline`: Secondary actions (border only)
- `secondary`: Alternative actions (gray background)
- `ghost`: Minimal styling (hover effects only)
- `link`: Text-styled button (underlined on hover)

### Card Component
```typescript
interface CardProps {
  className?: string;
  children: React.ReactNode;
}

interface CardHeaderProps {
  className?: string;
  children: React.ReactNode;
}

interface CardContentProps {
  className?: string;
  children: React.ReactNode;
}

interface CardFooterProps {
  className?: string;
  children: React.ReactNode;
}

// Usage Example
<Card>
  <CardHeader>
    <h3>Test Results</h3>
  </CardHeader>
  <CardContent>
    <p>Your typing speed: 65 WPM</p>
  </CardContent>
  <CardFooter>
    <Button>Download Certificate</Button>
  </CardFooter>
</Card>
```

### Badge Component
```typescript
interface BadgeProps {
  variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  children: React.ReactNode;
  className?: string;
}

// Status badges for test results
<Badge variant="default">Completed</Badge>
<Badge variant="destructive">Failed</Badge>
<Badge variant="secondary">In Progress</Badge>
<Badge variant="outline">Practice</Badge>
```

### Avatar Component
```typescript
interface AvatarProps {
  src?: string;
  alt?: string;
  fallback: string;
  size?: 'sm' | 'md' | 'lg';
}

// User profile avatar
<Avatar 
  src="/user-photos/john-doe.jpg"
  alt="John Doe"
  fallback="JD"
  size="md"
/>
```

## Form Components

### Input Component
```typescript
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  required?: boolean;
  icon?: React.ReactNode;
}

// Form input with validation
<Input
  label="Email Address"
  type="email"
  placeholder="Enter your email"
  required
  error={errors.email}
  helperText="We'll never share your email"
/>
```

### Textarea Component
```typescript
interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helperText?: string;
  required?: boolean;
  rows?: number;
}

// Multi-line text input
<Textarea
  label="Reason for Access Request"
  placeholder="Please explain why you need access to this test"
  rows={4}
  required
/>
```

### Select Component
```typescript
interface SelectProps {
  label?: string;
  placeholder?: string;
  options: SelectOption[];
  value?: string;
  onValueChange?: (value: string) => void;
  error?: string;
  required?: boolean;
}

interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

// Dropdown selection
<Select
  label="Education Level"
  placeholder="Select your education level"
  options={[
    { value: 'highschool', label: 'High School' },
    { value: 'bachelors', label: "Bachelor's Degree" },
    { value: 'masters', label: "Master's Degree" }
  ]}
  onValueChange={setEducationLevel}
/>
```

### Checkbox Component
```typescript
interface CheckboxProps {
  id: string;
  label?: string;
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
  disabled?: boolean;
  required?: boolean;
}

// Boolean input control
<Checkbox
  id="terms"
  label="I agree to the Terms and Conditions"
  checked={agreedToTerms}
  onCheckedChange={setAgreedToTerms}
  required
/>
```

### Radio Group Component
```typescript
interface RadioGroupProps {
  label?: string;
  options: RadioOption[];
  value?: string;
  onValueChange?: (value: string) => void;
  required?: boolean;
}

interface RadioOption {
  value: string;
  label: string;
  disabled?: boolean;
}

// Single choice selection
<RadioGroup
  label="English as First Language?"
  options={[
    { value: 'yes', label: 'Yes' },
    { value: 'no', label: 'No' }
  ]}
  value={englishFirst}
  onValueChange={setEnglishFirst}
/>
```

## Layout Components

### Container Component
```typescript
interface ContainerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  className?: string;
  children: React.ReactNode;
}

// Responsive container with max-width
<Container size="lg">
  <h1>Dashboard Content</h1>
</Container>
```

### Grid Component
```typescript
interface GridProps {
  cols?: 1 | 2 | 3 | 4 | 6 | 12;
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
  children: React.ReactNode;
}

// CSS Grid layout
<Grid cols={3} gap="md">
  <Card>Test Type 1</Card>
  <Card>Test Type 2</Card>
  <Card>Test Type 3</Card>
</Grid>
```

### Flex Component
```typescript
interface FlexProps {
  direction?: 'row' | 'col';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around';
  align?: 'start' | 'center' | 'end' | 'stretch';
  gap?: 'sm' | 'md' | 'lg';
  wrap?: boolean;
  className?: string;
  children: React.ReactNode;
}

// Flexbox layout
<Flex direction="row" justify="between" align="center">
  <h2>Test Results</h2>
  <Button>Download</Button>
</Flex>
```

### Separator Component
```typescript
interface SeparatorProps {
  orientation?: 'horizontal' | 'vertical';
  className?: string;
}

// Visual divider
<div>
  <h3>Section 1</h3>
  <Separator />
  <h3>Section 2</h3>
</div>
```

## Navigation Components

### Header Component
```typescript
interface HeaderProps {
  user?: User;
  showNavigation?: boolean;
  onSignOut?: () => void;
}

// Application header with navigation
<Header 
  user={currentUser}
  showNavigation={true}
  onSignOut={handleSignOut}
/>
```

### Breadcrumb Component
```typescript
interface BreadcrumbProps {
  items: BreadcrumbItem[];
  separator?: React.ReactNode;
}

interface BreadcrumbItem {
  href?: string;
  label: string;
  current?: boolean;
}

// Navigation breadcrumbs
<Breadcrumb
  items={[
    { href: '/dashboard', label: 'Dashboard' },
    { href: '/admin', label: 'Admin' },
    { label: 'Users', current: true }
  ]}
/>
```

### Tabs Component
```typescript
interface TabsProps {
  defaultValue?: string;
  children: React.ReactNode;
  onValueChange?: (value: string) => void;
}

interface TabsListProps {
  children: React.ReactNode;
}

interface TabsTriggerProps {
  value: string;
  children: React.ReactNode;
}

interface TabsContentProps {
  value: string;
  children: React.ReactNode;
}

// Tabbed interface
<Tabs defaultValue="overview">
  <TabsList>
    <TabsTrigger value="overview">Overview</TabsTrigger>
    <TabsTrigger value="results">Results</TabsTrigger>
    <TabsTrigger value="settings">Settings</TabsTrigger>
  </TabsList>
  <TabsContent value="overview">
    <p>Dashboard overview content</p>
  </TabsContent>
  <TabsContent value="results">
    <p>Test results content</p>
  </TabsContent>
</Tabs>
```

## Feedback Components

### Alert Component
```typescript
interface AlertProps {
  variant?: 'default' | 'destructive' | 'warning' | 'success';
  title?: string;
  children: React.ReactNode;
  className?: string;
}

// Status alerts and notifications
<Alert variant="success">
  <CheckCircle className="h-4 w-4" />
  <AlertTitle>Test Completed Successfully</AlertTitle>
  <AlertDescription>
    Your test results have been saved and a certificate has been generated.
  </AlertDescription>
</Alert>

<Alert variant="destructive">
  <AlertCircle className="h-4 w-4" />
  <AlertTitle>Error</AlertTitle>
  <AlertDescription>
    Unable to save test results. Please try again.
  </AlertDescription>
</Alert>
```

### Toast Component
```typescript
interface ToastProps {
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive' | 'success';
  action?: {
    label: string;
    onClick: () => void;
  };
}

// Temporary notification system
import { useToast } from '@/hooks/use-toast';

const { toast } = useToast();

// Success toast
toast({
  title: "Test Submitted",
  description: "Your answers have been saved successfully.",
  variant: "success"
});

// Error toast with action
toast({
  title: "Connection Lost",
  description: "Your progress has been saved locally.",
  variant: "destructive",
  action: {
    label: "Retry",
    onClick: () => retryConnection()
  }
});
```

### Progress Component
```typescript
interface ProgressProps {
  value: number;          // 0-100
  max?: number;           // Default: 100
  className?: string;
  showLabel?: boolean;
  label?: string;
}

// Progress indicators
<Progress 
  value={75} 
  showLabel 
  label="Test Progress: 3 of 4 complete"
/>

// Indeterminate progress
<Progress value={null} label="Loading..." />
```

### Skeleton Component
```typescript
interface SkeletonProps {
  className?: string;
  lines?: number;
  width?: string | number;
  height?: string | number;
}

// Loading placeholders
<div>
  <Skeleton width="60%" height="2rem" />
  <Skeleton lines={3} />
  <Skeleton width="100px" height="40px" />
</div>
```

## Data Display Components

### Table Component
```typescript
interface TableProps {
  data: any[];
  columns: TableColumn[];
  pagination?: PaginationConfig;
  sorting?: SortingConfig;
  loading?: boolean;
  emptyMessage?: string;
}

interface TableColumn {
  key: string;
  header: string;
  accessor?: string | ((row: any) => React.ReactNode);
  sortable?: boolean;
  width?: string;
}

// Data table with pagination and sorting
<Table
  data={users}
  columns={[
    { key: 'name', header: 'Name', sortable: true },
    { key: 'email', header: 'Email', sortable: true },
    { key: 'created', header: 'Created', accessor: (row) => formatDate(row.createdAt) },
    { key: 'actions', header: 'Actions', accessor: (row) => <ActionButtons user={row} /> }
  ]}
  pagination={{ 
    currentPage: 1, 
    totalPages: 10, 
    pageSize: 20 
  }}
  loading={loading}
/>
```

### Pagination Component
```typescript
interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showPageSize?: boolean;
  pageSizeOptions?: number[];
  onPageSizeChange?: (size: number) => void;
}

// Table pagination controls
<Pagination
  currentPage={currentPage}
  totalPages={totalPages}
  onPageChange={setCurrentPage}
  showPageSize
  pageSizeOptions={[10, 20, 50]}
  onPageSizeChange={setPageSize}
/>
```

### Chart Components
```typescript
interface ChartProps {
  data: ChartData[];
  width?: number;
  height?: number;
  title?: string;
  xAxisLabel?: string;
  yAxisLabel?: string;
}

// Line chart for score trends
<LineChart
  data={scoreHistory}
  title="Score Improvement Over Time"
  xAxisLabel="Test Attempts"
  yAxisLabel="Score"
  height={300}
/>

// Bar chart for test type distribution
<BarChart
  data={testTypeStats}
  title="Tests by Type"
  xAxisLabel="Test Type"
  yAxisLabel="Number of Tests"
/>

// Pie chart for access type distribution
<PieChart
  data={accessTypeStats}
  title="Access Type Distribution"
  height={250}
/>
```

## Testing-Specific Components

### Test Timer Component
```typescript
interface TestTimerProps {
  startTime: Date;
  duration?: number;      // Duration in minutes
  onTimeUp?: () => void;
  showWarnings?: boolean; // Show warnings at 5min, 1min remaining
  format?: 'mm:ss' | 'h:mm:ss';
}

// Countdown timer for timed tests
<TestTimer
  startTime={testStartTime}
  duration={45}
  onTimeUp={handleTimeUp}
  showWarnings
  format="mm:ss"
/>
```

### Progress Indicator Component
```typescript
interface TestProgressProps {
  currentQuestion: number;
  totalQuestions: number;
  completedQuestions: number;
  showDetails?: boolean;
}

// Test progress visualization
<TestProgress
  currentQuestion={5}
  totalQuestions={20}
  completedQuestions={4}
  showDetails
/>
```

### Typing Test Display Component
```typescript
interface TypingDisplayProps {
  originalText: string;
  typedText: string;
  currentPosition: number;
  highlightErrors?: boolean;
  showCursor?: boolean;
}

// Real-time typing test interface
<TypingDisplay
  originalText={passage}
  typedText={userTypedText}
  currentPosition={cursorPosition}
  highlightErrors
  showCursor
/>
```

### Score Display Component
```typescript
interface ScoreDisplayProps {
  score: number;
  maxScore?: number;
  showPercentage?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'success' | 'warning' | 'error';
  label?: string;
}

// Prominent score display
<ScoreDisplay
  score={85.5}
  showPercentage
  size="lg"
  variant="success"
  label="Final Score"
/>
```

## Modal & Overlay Components

### Dialog Component
```typescript
interface DialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
  description?: string;
  children: React.ReactNode;
}

// Modal dialogs
<Dialog open={showModal} onOpenChange={setShowModal}>
  <DialogHeader>
    <DialogTitle>Confirm Test Submission</DialogTitle>
    <DialogDescription>
      Are you sure you want to submit your test? This action cannot be undone.
    </DialogDescription>
  </DialogHeader>
  <DialogContent>
    <p>You have answered 18 of 20 questions.</p>
  </DialogContent>
  <DialogFooter>
    <Button variant="outline" onClick={() => setShowModal(false)}>
      Cancel
    </Button>
    <Button onClick={handleSubmit}>
      Submit Test
    </Button>
  </DialogFooter>
</Dialog>
```

### Popover Component
```typescript
interface PopoverProps {
  trigger: React.ReactNode;
  children: React.ReactNode;
  side?: 'top' | 'right' | 'bottom' | 'left';
  align?: 'start' | 'center' | 'end';
}

// Contextual information popup
<Popover 
  trigger={<Button variant="ghost">Help</Button>}
  side="top"
>
  <div className="p-4">
    <h4>Typing Test Instructions</h4>
    <p>Type the text exactly as shown, including punctuation and capitalization.</p>
  </div>
</Popover>
```

### Tooltip Component
```typescript
interface TooltipProps {
  content: string | React.ReactNode;
  children: React.ReactNode;
  side?: 'top' | 'right' | 'bottom' | 'left';
  delayDuration?: number;
}

// Hover information
<Tooltip content="Your words per minute typing speed">
  <span>WPM: 65</span>
</Tooltip>
```

## Component Composition Patterns

### Form Composition
```typescript
// Compound form pattern
<Form onSubmit={handleSubmit}>
  <FormField>
    <FormLabel>Email</FormLabel>
    <Input type="email" />
    <FormDescription>We'll never share your email</FormDescription>
    <FormMessage />
  </FormField>
  
  <FormField>
    <FormLabel>Password</FormLabel>
    <Input type="password" />
    <FormMessage />
  </FormField>
  
  <FormActions>
    <Button type="submit">Sign In</Button>
    <Button type="button" variant="outline">Cancel</Button>
  </FormActions>
</Form>
```

### Card Composition
```typescript
// Test result card pattern
<Card>
  <CardHeader>
    <div className="flex justify-between items-center">
      <CardTitle>Typing Test Results</CardTitle>
      <Badge variant="success">Completed</Badge>
    </div>
    <CardDescription>
      Completed on {formatDate(completedAt)}
    </CardDescription>
  </CardHeader>
  
  <CardContent>
    <Grid cols={2} gap="md">
      <ScoreDisplay score={wpm} label="WPM" />
      <ScoreDisplay score={accuracy} label="Accuracy" showPercentage />
    </Grid>
    
    <Separator className="my-4" />
    
    <div>
      <h4>Performance Breakdown</h4>
      <Progress value={accuracyPercentage} label="Accuracy" />
      <Progress value={speedPercentage} label="Speed" />
    </div>
  </CardContent>
  
  <CardFooter>
    <Button className="w-full">Download Certificate</Button>
  </CardFooter>
</Card>
```

## Accessibility Features

### Built-in Accessibility
- **ARIA Labels**: All interactive components include proper ARIA attributes
- **Keyboard Navigation**: Full keyboard support for all components
- **Focus Management**: Logical tab order and focus indicators
- **Screen Reader Support**: Semantic HTML and descriptive text
- **Color Contrast**: WCAG AA compliant color combinations
- **Text Scaling**: Components work with browser zoom up to 200%

### Accessibility Props
```typescript
interface AccessibilityProps {
  'aria-label'?: string;
  'aria-describedby'?: string;
  'aria-expanded'?: boolean;
  'aria-hidden'?: boolean;
  role?: string;
  tabIndex?: number;
}

// Example with accessibility props
<Button
  aria-label="Start typing test"
  aria-describedby="test-description"
  onClick={startTest}
>
  Begin Test
</Button>
<p id="test-description">
  This will start a 5-minute typing assessment.
</p>
```

## Theme Customization

### CSS Custom Properties
```css
:root {
  /* Primary colors */
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  
  /* Secondary colors */
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  
  /* Status colors */
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --success: 142.1 76.2% 36.3%;
  --warning: 47.9 95.8% 53.1%;
  
  /* Component colors */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
}

/* Dark theme overrides */
[data-theme="dark"] {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
}
```

### Component Customization
```typescript
// Custom component variants
const customButtonVariants = {
  variants: {
    variant: {
      premium: "bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600",
      test: "bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6",
    }
  }
};

<Button variant="premium">Upgrade to Premium</Button>
<Button variant="test">Start Official Test</Button>
```

---

*This comprehensive components library documentation provides complete understanding of all UI components, their usage patterns, accessibility features, and customization options within the Rubicon Programs Testing Application.*