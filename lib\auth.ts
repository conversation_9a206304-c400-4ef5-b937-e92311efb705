
import { NextAuthOptions } from 'next-auth';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { prisma } from '@/lib/db';

export async function getCurrentUser() {
  const session = await getServerSession(authOptions);
  
  if (!session?.user?.email) {
    return null;
  }

  const user = await prisma.users.findUnique({
    where: { email: session.user.email },
    include: {
      user_test_access: {
        include: { test_types: true }
      }
    }
  });

  return user;
}

export async function requireAuth() {
  const user = await getCurrentUser();
  
  if (!user) {
    throw new Error('Authentication required');
  }
  
  return user;
}

export async function requireAdmin() {
  const user = await requireAuth();
  
  if (user.userType !== 'ADMIN') {
    throw new Error('Admin access required');
  }
  
  return user;
}

export async function isImpersonating(): Promise<boolean> {
  const session = await getServerSession(authOptions);
  return !!(session?.impersonating);
}

export async function returnToAdmin() {
  // This function would typically be called from a client component
  // The actual implementation depends on how session updates are handled
  // For now, we'll just provide the structure
  const session = await getServerSession(authOptions);
  if (session?.impersonating) {
    // Logic to clear impersonation would go here
    // This would typically involve updating the session
    return true;
  }
  return false;
}

export async function getImpersonatedUser() {
  const session = await getServerSession(authOptions);
  return session?.impersonating?.impersonatedUser || null;
}

export async function getRealUser() {
  const session = await getServerSession(authOptions);
  if (session?.impersonating) {
    // Return the actual admin user who is impersonating
    const adminUser = await prisma.users.findUnique({
      where: { id: session.impersonating.adminUser.id }
    });
    return adminUser;
  }
  // If not impersonating, return current user
  return getCurrentUser();
}
