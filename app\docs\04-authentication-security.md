# Authentication & Security

## Overview

The Rubicon Programs Testing Application implements a comprehensive, multi-layered security architecture designed to protect user data, ensure test integrity, and maintain system reliability. The security system is built on industry-standard practices and modern authentication protocols.

## Authentication Architecture

### NextAuth.js Integration

The application uses **NextAuth.js v4.24.11** as the primary authentication provider, offering:

- **Session Management**: JWT-based sessions with automatic refresh
- **Credential Provider**: Email/password authentication with custom validation
- **Middleware Protection**: Route-level authentication enforcement
- **Type Safety**: Full TypeScript integration with custom session types

### Authentication Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant NextAuth
    participant Database
    participant Middleware
    
    User->>Frontend: Login Attempt
    Frontend->>NextAuth: Credentials
    NextAuth->>Database: Validate User
    Database->>NextAuth: User Data
    NextAuth->>Frontend: JWT Token
    Frontend->>Middleware: Protected Route Request
    Middleware->>NextAuth: Token Validation
    NextAuth->>Middleware: Session Data
    Middleware->>Frontend: Access Granted/Denied
```

## Authentication Configuration

### NextAuth Options
```typescript
export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        // Email and password validation
        if (!credentials?.email || !credentials?.password) {
          throw new Error('Email and password required');
        }

        // User lookup with case-insensitive email
        const user = await prisma.users.findUnique({
          where: { email: credentials.email.toLowerCase() }
        });

        // Account existence check
        if (!user || !user.password) {
          throw new Error('Invalid email or password');
        }

        // Email verification requirement
        if (!user.emailVerified) {
          throw new Error('Please verify your email address before signing in');
        }

        // Password validation using bcrypt
        const isPasswordValid = await bcryptjs.compare(
          credentials.password,
          user.password
        );

        if (!isPasswordValid) {
          throw new Error('Invalid email or password');
        }

        // Return user data for session
        return {
          id: user.id,
          email: user.email,
          name: `${user.firstName} ${user.lastName}`,
          firstName: user.firstName,
          lastName: user.lastName,
          userType: user.userType,
          isPrimaryAdmin: user.isPrimaryAdmin,
          isDeactivated: user.isDeactivated,
          requirePasswordChange: user.requirePasswordChange
        };
      }
    })
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  // ... additional configuration
};
```

### Session Management

#### JWT Token Structure
```typescript
interface JWT {
  sub: string;                    // User ID
  email: string;                  // User email
  userType: string;               // USER | ADMIN
  firstName: string;              // User's first name
  lastName: string;               // User's last name
  isPrimaryAdmin: boolean;        // Primary admin flag
  isDeactivated: boolean;         // Account status
  requirePasswordChange: boolean; // Password change flag
  iat: number;                    // Issued at timestamp
  exp: number;                    // Expiration timestamp
  jti: string;                    // JWT ID
  
  // Impersonation support
  impersonating?: {
    originalAdmin: UserInfo;
    impersonatedUser: UserInfo;
  };
}
```

#### Session Callbacks
```typescript
callbacks: {
  async jwt({ token, user, trigger, session }) {
    // Initial token creation
    if (user) {
      token.userType = user.userType;
      token.firstName = user.firstName;
      token.lastName = user.lastName;
      token.isPrimaryAdmin = user.isPrimaryAdmin;
      token.isDeactivated = user.isDeactivated;
      token.requirePasswordChange = user.requirePasswordChange;
    }
    
    // Token refresh - update user status
    if (token.sub && !user) {
      const currentUser = await prisma.users.findUnique({
        where: { id: token.sub },
        select: {
          isDeactivated: true,
          requirePasswordChange: true,
          isPrimaryAdmin: true,
          userType: true
        }
      });
      
      if (currentUser) {
        token.isDeactivated = currentUser.isDeactivated;
        token.requirePasswordChange = currentUser.requirePasswordChange;
        token.isPrimaryAdmin = currentUser.isPrimaryAdmin;
        token.userType = currentUser.userType;
      }
    }
    
    return token;
  },
  
  async session({ session, token }) {
    // Build session object from token
    session.user.id = token.sub!;
    session.user.userType = token.userType as string;
    session.user.firstName = token.firstName as string;
    session.user.lastName = token.lastName as string;
    session.user.isPrimaryAdmin = token.isPrimaryAdmin as boolean;
    session.user.isDeactivated = token.isDeactivated as boolean;
    session.user.requirePasswordChange = token.requirePasswordChange as boolean;
    
    // Include impersonation data
    if (token.impersonating) {
      session.impersonating = token.impersonating as any;
    }
    
    return session;
  }
}
```

## Password Security

### Password Hashing
The application uses **bcryptjs** with salt rounds for secure password storage:

```typescript
import bcryptjs from 'bcryptjs';

export class PasswordService {
  private static SALT_ROUNDS = 12;

  static async hashPassword(password: string): Promise<string> {
    return bcryptjs.hash(password, this.SALT_ROUNDS);
  }

  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcryptjs.compare(password, hash);
  }
}
```

### Password Strength Requirements
```typescript
export interface PasswordStrength {
  score: number;           // 0-100 strength score
  hasMinLength: boolean;   // At least 8 characters
  hasUppercase: boolean;   // Contains uppercase letter
  hasLowercase: boolean;   // Contains lowercase letter
  hasNumber: boolean;      // Contains numeric digit
  hasSpecialChar: boolean; // Contains special character
}

export function validatePasswordStrength(password: string): PasswordStrength {
  return {
    score: calculatePasswordScore(password),
    hasMinLength: password.length >= 8,
    hasUppercase: /[A-Z]/.test(password),
    hasLowercase: /[a-z]/.test(password),
    hasNumber: /\d/.test(password),
    hasSpecialChar: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
  };
}
```

### Password Reset System
```typescript
// Password reset token generation
export async function createPasswordResetToken(userId: string): Promise<string> {
  const token = crypto.randomBytes(32).toString('hex');
  const expires = new Date(Date.now() + 3600000); // 1 hour

  await prisma.passwordReset.create({
    data: {
      userId,
      token,
      expires,
      used: false
    }
  });

  return token;
}

// Token validation and usage
export async function validatePasswordResetToken(token: string): Promise<boolean> {
  const resetRecord = await prisma.passwordReset.findUnique({
    where: { token },
    include: { users: true }
  });

  if (!resetRecord || resetRecord.used || resetRecord.expires < new Date()) {
    return false;
  }

  return true;
}
```

## Email Verification System

### Verification Process
```typescript
export class EmailVerificationService {
  static async createVerificationToken(userId: string, email: string): Promise<string> {
    const token = crypto.randomUUID();
    const expires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    await prisma.emailVerification.create({
      data: {
        userId,
        email: email.toLowerCase(),
        token,
        expires,
        verified: false
      }
    });

    return token;
  }

  static async verifyEmail(token: string): Promise<boolean> {
    const verification = await prisma.emailVerification.findUnique({
      where: { token },
      include: { users: true }
    });

    if (!verification || verification.expires < new Date()) {
      return false;
    }

    // Update verification status
    await prisma.emailVerification.update({
      where: { id: verification.id },
      data: { verified: true }
    });

    // Update user email verification
    await prisma.users.update({
      where: { id: verification.userId },
      data: { emailVerified: new Date() }
    });

    return true;
  }
}
```

## Route Protection & Middleware

### Middleware Configuration
```typescript
export default withAuth(
  async function middleware(req) {
    const token = req.nextauth.token;
    const pathname = req.nextUrl.pathname;

    // Public routes - no authentication required
    const publicRoutes = [
      '/auth/',
      '/api/',
      '/_next/',
      '/favicon.ico',
      '/restricted-access',
      '/request-access',
      '/test-practice'
    ];

    // Allow public routes
    if (publicRoutes.some(route => pathname.startsWith(route))) {
      return NextResponse.next();
    }

    // Account deactivation check
    if (token?.isDeactivated) {
      return NextResponse.redirect(new URL('/restricted-access', req.url));
    }

    // Required password change check
    if (token?.requirePasswordChange) {
      if (pathname === '/auth/change-password') {
        return NextResponse.next();
      }
      return NextResponse.redirect(new URL('/auth/change-password', req.url));
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const pathname = req.nextUrl.pathname;
        
        // Allow public routes
        if (isPublicRoute(pathname)) {
          return true;
        }
        
        // Require valid token for protected routes
        return !!token;
      },
    },
  }
);
```

### Authorization Helpers
```typescript
// Server-side authentication utilities
export async function getCurrentUser(): Promise<users | null> {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) return null;

  return prisma.users.findUnique({
    where: { id: session.user.id },
    include: {
      user_test_access: {
        include: { test_types: true }
      }
    }
  });
}

export async function requireAuth(): Promise<users> {
  const user = await getCurrentUser();
  if (!user) {
    redirect('/auth/signin');
  }
  return user;
}

export async function requireAdmin(): Promise<users> {
  const user = await requireAuth();
  if (user.userType !== 'ADMIN') {
    redirect('/dashboard');
  }
  return user;
}
```

## Role-Based Access Control

### User Types & Permissions

#### Standard User Permissions
```typescript
const USER_PERMISSIONS = {
  // Profile management
  viewProfile: true,
  editProfile: true,
  changePassword: true,
  
  // Test taking
  takePracticeTests: true,
  takeOfficialTests: (accessType: AccessType) => accessType !== 'NONE',
  viewTestResults: true,
  downloadResults: true,
  
  // System access
  requestTestAccess: true,
  viewDashboard: true,
  
  // Restrictions
  viewAdminPanel: false,
  manageUsers: false,
  createTests: false,
  viewAllResults: false
};
```

#### Administrator Permissions
```typescript
const ADMIN_PERMISSIONS = {
  // All user permissions plus:
  viewAdminPanel: true,
  manageUsers: true,
  createUsers: true,
  deleteUsers: true,
  resetPasswords: true,
  
  // Test management
  createTests: true,
  editTests: true,
  deleteTests: true,
  viewAllResults: true,
  exportResults: true,
  
  // Access control
  grantTestAccess: true,
  revokeTestAccess: true,
  createOneTimeCodes: true,
  reviewAccessRequests: true,
  
  // System administration
  viewAuditLogs: true,
  configureSettings: true,
  impersonateUsers: (isPrimaryAdmin: boolean) => isPrimaryAdmin,
  
  // Advanced features
  viewAnalytics: true,
  generateReports: true,
  systemMaintenance: (isPrimaryAdmin: boolean) => isPrimaryAdmin
};
```

### Access Control Implementation
```typescript
// Permission checking middleware
export function withPermission(permission: string) {
  return function(handler: NextApiHandler) {
    return async (req: NextApiRequest, res: NextApiResponse) => {
      const session = await getServerSession(req, res, authOptions);
      
      if (!session) {
        return res.status(401).json({ error: 'Authentication required' });
      }
      
      if (!hasPermission(session.user, permission)) {
        return res.status(403).json({ error: 'Insufficient permissions' });
      }
      
      return handler(req, res);
    };
  };
}

// Permission validation
function hasPermission(user: any, permission: string): boolean {
  const permissions = user.userType === 'ADMIN' ? ADMIN_PERMISSIONS : USER_PERMISSIONS;
  return permissions[permission] === true || 
         (typeof permissions[permission] === 'function' && permissions[permission](user));
}
```

## Test Security

### Test Session Integrity
```typescript
export class TestSecurityService {
  // Prevent test session tampering
  static generateTestHash(testId: string, userId: string, timestamp: number): string {
    const secret = process.env.TEST_SECURITY_SECRET;
    return crypto
      .createHmac('sha256', secret)
      .update(`${testId}-${userId}-${timestamp}`)
      .digest('hex');
  }

  // Validate test session
  static validateTestSession(testId: string, hash: string, userId: string, timestamp: number): boolean {
    const expectedHash = this.generateTestHash(testId, userId, timestamp);
    return crypto.timingSafeEqual(
      Buffer.from(hash, 'hex'),
      Buffer.from(expectedHash, 'hex')
    );
  }

  // Detect suspicious activity
  static async checkTestAnomalies(testId: string): Promise<boolean> {
    const test = await prisma.tests.findUnique({
      where: { id: testId },
      include: { typing_session: true }
    });

    if (!test) return false;

    // Check for timing anomalies
    const duration = test.completedAt && test.startedAt ? 
      test.completedAt.getTime() - test.startedAt.getTime() : 0;
    
    if (duration < 60000) { // Less than 1 minute
      return true; // Suspicious
    }

    // Additional anomaly checks...
    return false;
  }
}
```

### One-Time Code Security
```typescript
export class OneTimeCodeService {
  // Generate secure random codes
  static generateCode(): string {
    const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'; // Avoid confusing chars
    let code = '';
    for (let i = 0; i < 8; i++) {
      code += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return code;
  }

  // Rate limiting for code usage
  static async checkRateLimit(userId: string): Promise<boolean> {
    const recentCodes = await prisma.one_time_codes.count({
      where: {
        usedBy: userId,
        usedAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        }
      }
    });

    return recentCodes < 3; // Limit 3 codes per day
  }
}
```

## Data Protection

### Input Validation & Sanitization
```typescript
// Comprehensive input validation
export const ValidationSchemas = {
  email: z.string().email().max(255),
  password: z.string().min(8).max(128),
  name: z.string().min(1).max(100).regex(/^[a-zA-Z\s\-'\.]+$/),
  zipCode: z.string().regex(/^\d{5}(-\d{4})?$/),
  phoneNumber: z.string().regex(/^\+?[\d\s\-\(\)\.]+$/).optional()
};

// API input sanitization
export function sanitizeInput(data: any): any {
  if (typeof data === 'string') {
    return DOMPurify.sanitize(data.trim());
  }
  
  if (Array.isArray(data)) {
    return data.map(sanitizeInput);
  }
  
  if (data && typeof data === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitizeInput(value);
    }
    return sanitized;
  }
  
  return data;
}
```

### SQL Injection Prevention
The application uses **Prisma ORM**, which provides built-in protection against SQL injection through:

- **Parameterized Queries**: All queries are parameterized automatically
- **Type Safety**: TypeScript ensures query parameter types
- **Query Builder**: No raw SQL construction
- **Input Validation**: Schema validation before database operations

```typescript
// Safe query example with Prisma
export async function getUserTests(userId: string, testType?: string) {
  return prisma.tests.findMany({
    where: {
      userId,
      ...(testType && { test_types: { name: testType } })
    },
    include: {
      test_types: true,
      test_results: true
    }
  });
}
```

## Security Monitoring & Logging

### Audit Trail System
```typescript
export enum AdminActionType {
  USER_CREATED = 'USER_CREATED',
  USER_UPDATED = 'USER_UPDATED',
  USER_DELETED = 'USER_DELETED',
  TEST_CANCELLED = 'TEST_CANCELLED',
  ACCESS_GRANTED = 'ACCESS_GRANTED',
  ACCESS_REVOKED = 'ACCESS_REVOKED',
  PASSWORD_RESET = 'PASSWORD_RESET',
  SETTINGS_UPDATED = 'SETTINGS_UPDATED'
}

export async function logAdminAction(
  adminUserId: string, 
  action: AdminActionType, 
  targetId?: string, 
  details?: any
) {
  await prisma.admin_actions.create({
    data: {
      adminUserId,
      action,
      targetId,
      details: details ? JSON.stringify(details) : null
    }
  });
}
```

### Security Event Detection
```typescript
export class SecurityMonitor {
  // Failed login attempt tracking
  static async trackFailedLogin(email: string, ip: string) {
    // Implement rate limiting logic
    const attempts = await getFailedAttempts(email, ip);
    
    if (attempts > 5) {
      // Lock account temporarily
      await this.lockAccount(email, 15); // 15 minutes
    }
  }

  // Suspicious activity detection
  static async detectAnomalies(userId: string, activity: string) {
    // Analyze user behavior patterns
    // Flag unusual access patterns
    // Notify administrators of potential security issues
  }
}
```

## Compliance & Data Privacy

### GDPR Compliance Features
- **Data Portability**: Export user data in standard formats
- **Right to Erasure**: Complete data deletion capabilities
- **Consent Management**: Explicit consent tracking
- **Data Minimization**: Collect only necessary information
- **Privacy by Design**: Built-in privacy protections

### Data Retention Policies
```typescript
export class DataRetentionService {
  // Automatic cleanup of expired data
  static async cleanupExpiredData() {
    // Remove expired password reset tokens
    await prisma.passwordReset.deleteMany({
      where: {
        expires: { lt: new Date() },
        used: true
      }
    });

    // Remove old email verification tokens
    await prisma.emailVerification.deleteMany({
      where: {
        expires: { lt: new Date() },
        verified: true
      }
    });

    // Archive old test sessions (configurable retention period)
    const retentionDate = new Date();
    retentionDate.setFullYear(retentionDate.getFullYear() - 7); // 7 year retention

    await prisma.tests.updateMany({
      where: {
        completedAt: { lt: retentionDate }
      },
      data: {
        // Move to archive or mark for deletion
      }
    });
  }
}
```

---

*This comprehensive security documentation ensures the application maintains the highest standards of data protection, user privacy, and system integrity.*