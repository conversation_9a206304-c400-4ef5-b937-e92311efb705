generator client {
    provider = "prisma-client-js"
    binaryTargets = ["native", "linux-musl-arm64-openssl-3.0.x"]
    output = "/home/<USER>/rubicon-testing-app/app/node_modules/.prisma/client"
}

datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  users             users   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model EmailVerification {
  id        String   @id @default(cuid())
  userId    String
  email     String
  token     String   @unique
  expires   DateTime
  verified  Boolean  @default(false)
  createdAt DateTime @default(now())
  users     users    @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model PasswordReset {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expires   DateTime
  used      Boolean  @default(false)
  createdAt DateTime @default(now())
  users     users    @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  users        users    @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model admin_actions {
  id          String          @id @default(cuid())
  adminUserId String
  action      AdminActionType
  targetId    String?
  details     Json?
  createdAt   DateTime        @default(now())
  users       users           @relation(fields: [adminUserId], references: [id])
}

model app_settings {
  id                     String   @id @default(cuid())
  twoFactorEnabled       Boolean  @default(false)
  customSignatureEnabled Boolean  @default(false)
  signatureName          String?
  signatureTitle         String?
  signatureImage         String?
  testPausingEnabled     Boolean  @default(true)
  pdfDownloadEnabled     Boolean  @default(true)
  practiceTestEnabled    Boolean  @default(true)
  aiServiceProvider      String   @default("abacusai")
  aiServiceApiKey        String?
  aiServiceModel         String   @default("gpt-4.1-mini")
  aiServiceEnabled       Boolean  @default(true)
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt

  english_test_config    english_test_config? @relation(fields: [english_test_config_id], references: [id])
  english_test_config_id String?
}

model one_time_codes {
  id         String     @id @default(cuid())
  code       String     @unique
  testTypeId String
  createdBy  String
  usedBy     String?
  usedAt     DateTime?
  isActive   Boolean    @default(true)
  createdAt  DateTime   @default(now())
  expiresAt  DateTime?
  test_types test_types @relation(fields: [testTypeId], references: [id])
  tests      tests[]
}

model practice_test_config {
  id               String     @id @default(cuid())
  testTypeId       String     @unique
  questionCount    Int        @default(5)
  timeLimit        Int?
  minBankQuestions Int        @default(0)
  createdAt        DateTime   @default(now())
  updatedAt        DateTime   @updatedAt
  test_types       test_types @relation(fields: [testTypeId], references: [id])

  // English test specific settings
  english_adaptive_testing_enabled Boolean?
  english_show_explanations_practice Boolean?
  english_include_none_of_above    Boolean?
}

model questions {
  id              String       @id @default(cuid())
  testTypeId      String
  content         String
  questionType    QuestionType
  options         Json?
  correctAnswer   Json
  explanation     String?
  difficultyLevel Int?
  gradeLevel      String?
  metadata        Json?
  timesAsked      Int          @default(0)
  timesCorrect    Int          @default(0)
  isActive        Boolean      @default(true)
  createdBy       String       @default("AI")
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt
  test_types      test_types   @relation(fields: [testTypeId], references: [id])
  
  // Typing test specific fields
  passageText     String?      // For keyboarding passages
  expectedText    String?      // For 10-key number sequences
  wordCount       Int?         // Number of words in passage
  characterCount  Int?         // Total characters including spaces
}

model typing_test_sessions {
  id                String    @id @default(cuid())
  testId            String    @unique
  userId            String
  testType          String    // "keyboarding" or "10-key"
  passageText       String
  expectedText      String
  startedAt         DateTime  @default(now())
  completedAt       DateTime?
  timeElapsed       Int?      // in seconds
  
  // Real-time tracking
  currentPosition   Int       @default(0)  // Character position
  currentWordIndex  Int       @default(0)  // Word position
  typedText         String    @default("")
  keystrokeLog      Json[]    @default([]) // Keystroke timing data
  
  // Results
  totalCharacters   Int       @default(0)
  correctCharacters Int       @default(0)
  incorrectCharacters Int     @default(0)
  totalWords        Int       @default(0)
  correctWords      Int       @default(0)
  wpm               Float     @default(0)
  accuracy          Float     @default(0)
  weightedWpm       Float     @default(0)
  
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  
  tests             tests     @relation(fields: [testId], references: [id], onDelete: Cascade)
  users             users     @relation(fields: [userId], references: [id])
}

model test_requests {
  id         String        @id @default(cuid())
  userId     String
  testTypeId String
  status     RequestStatus @default(PENDING)
  reason     String?
  reviewedBy String?
  reviewedAt DateTime?
  response   String?
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt
  test_types test_types    @relation(fields: [testTypeId], references: [id])
  users      users         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, testTypeId])
}

model test_results {
  id                 String   @id @default(cuid())
  testId             String   @unique
  userId             String
  testTypeId         String
  score              Float
  gradeLevelScore    String?
  timeToComplete     Int?
  accuracy           Float?
  weightedSpeed      Float?
  rawSpeed           Float?
  languageScore      Float?
  comprehensionScore Float?
  difficultyLevel    Int?
  questionsCorrect   Int?
  questionsTotal     Int?
  detailedResults    Json?
  completedAt        DateTime
  createdAt          DateTime @default(now())
  tests              tests    @relation(fields: [testId], references: [id], onDelete: Cascade)
  users              users    @relation(fields: [userId], references: [id])
}

model test_types {
  id                   String                @id @default(cuid())
  name                 String                @unique
  displayName          String
  description          String?
  isActive             Boolean               @default(true)
  one_time_codes       one_time_codes[]
  practice_test_config practice_test_config?
  questions            questions[]
  test_requests        test_requests[]
  tests                tests[]
  user_test_access     user_test_access[]
}

model tests {
  id                String                 @id @default(cuid())
  userId            String?
  testTypeId        String
  oneTimeCodeId     String?
  status            TestStatus             @default(STARTED)
  isPractice        Boolean                @default(false)
  startedAt         DateTime               @default(now())
  pausedAt          DateTime?
  completedAt       DateTime?
  cancelledAt       DateTime?
  cancelReason      String?
  timeLimit         Int?
  currentQuestionId String?
  questionsOrder    Json?
  answers           Json?
  score             Float?
  gradeLevelScore   String?
  additionalData    Json?
  createdAt         DateTime               @default(now())
  updatedAt         DateTime               @updatedAt
  test_results      test_results?
  one_time_codes    one_time_codes?        @relation(fields: [oneTimeCodeId], references: [id])
  test_types        test_types             @relation(fields: [testTypeId], references: [id])
  users             users?                 @relation(fields: [userId], references: [id])
  typing_session    typing_test_sessions?
  english_test_results english_test_results?
}

model user_test_access {
  id         String     @id @default(cuid())
  userId     String
  testTypeId String
  accessType AccessType @default(NONE)
  grantedBy  String?
  grantedAt  DateTime?
  expiresAt  DateTime?
  isActive   Boolean    @default(true)
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt
  test_types test_types @relation(fields: [testTypeId], references: [id])
  users      users      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, testTypeId])
}

model users {
  id                    String                 @id @default(cuid())
  email                 String                 @unique
  emailVerified         DateTime?
  password              String?
  firstName             String
  lastName              String
  middleInitial         String?
  namePrefix            String?
  nameSuffix            String?
  dateOfBirth           DateTime
  zipCode               String
  phoneNumber           String?
  englishFirst          Boolean?
  educationLevel        String?
  profileImage          String?
  userType              UserType               @default(USER)
  isPrimaryAdmin        Boolean                @default(false)
  primaryAdminDate      DateTime?
  twoFactorEnabled      Boolean                @default(false)
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt
  isDeactivated         Boolean                @default(false)
  requirePasswordChange Boolean                @default(false)
  Account               Account[]
  EmailVerification     EmailVerification[]
  PasswordReset         PasswordReset[]
  Session               Session[]
  admin_actions         admin_actions[]
  test_requests         test_requests[]
  test_results          test_results[]
  tests                 tests[]
  user_test_access      user_test_access[]
  typing_sessions       typing_test_sessions[]
  math_tests            MathTest[]
}

model MathQuestion {
  id                String             @id @default(cuid())
  questionText      String
  answer            String
  gradeLevel        Int
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  mathTestQuestions MathTestQuestion[]
}

model MathTest {
  id                      String             @id @default(cuid())
  userId                  String
  user                    users              @relation(fields: [userId], references: [id])
  status                  TestStatus         @default(STARTED)
  currentGradeLevel       Int                @default(5)
  incorrectAnswersByGrade Json               @default("{}")
  correctAnswersByGrade   Json               @default("{}")
  mathTestQuestions       MathTestQuestion[]
  finalScore              Int?
  startedAt               DateTime           @default(now())
  pausedAt                DateTime?
  completedAt             DateTime?
  isPractice              Boolean            @default(false)
}

model MathTestQuestion {
  id             String       @id @default(cuid())
  mathTestId     String
  mathTest       MathTest     @relation(fields: [mathTestId], references: [id])
  mathQuestionId String
  mathQuestion   MathQuestion @relation(fields: [mathQuestionId], references: [id])
  userAnswer     String?
  isCorrect      Boolean?
  timestamp      DateTime     @default(now())
}

model english_questions {
  id                    String                  @id @default(cuid())
  question_type         EnglishQuestionType
  grade_level           Int
  question_text         String
  correct_answer        String
  explanation           String?
  difficulty_score      Float
  times_used            Int                     @default(0)
  times_correct         Int                     @default(0)
  created_at            DateTime                @default(now())
  updated_at            DateTime                @updatedAt
  is_active             Boolean                 @default(true)
  wrong_answers         english_wrong_answers[]
  passage_questions     passage_questions[]
}

model english_wrong_answers {
  id                String            @id @default(cuid())
  question_id       String
  question          english_questions @relation(fields: [question_id], references: [id])
  wrong_answer      String
  plausibility_score Float
  times_shown       Int               @default(0)
  times_selected    Int               @default(0)

  @@index([question_id])
}

model reading_passages {
  id               String              @id @default(cuid())
  title            String              @db.VarChar(255)
  passage_text     String
  grade_level      Int
  word_count       Int
  complexity_score Float
  genre            ReadingPassageGenre
  times_used       Int                 @default(0)
  is_active        Boolean             @default(true)
  passage_questions passage_questions[]
}

model passage_questions {
  id          String            @id @default(cuid())
  passage_id  String
  passage     reading_passages  @relation(fields: [passage_id], references: [id])
  question_id String
  question    english_questions @relation(fields: [question_id], references: [id])
  question_order Int

  @@index([passage_id])
  @@index([question_id])
}

model english_test_results {
  id                        String   @id @default(cuid())
  test_id                   String   @unique
  test                      tests    @relation(fields: [test_id], references: [id])
  language_mastery_score    Float
  reading_comprehension_score Float
  overall_score             Float
  vocabulary_score          Float
  spelling_score            Float
  grammar_score             Float
  punctuation_score         Float
  grade_level_achieved      Int
}

model english_test_config {
  id                             String   @id @default(cuid())
  min_questions_language_mastery Int      @default(20)
  min_questions_reading_comp   Int      @default(15)
  passages_per_test              Int      @default(3)
  questions_per_passage_min      Int      @default(3)
  questions_per_passage_max      Int      @default(5)
  adaptive_testing_enabled       Boolean  @default(true)
  show_explanations_practice     Boolean  @default(true)
  time_limit_minutes             Int      @default(60)
  question_bank_threshold        Int      @default(100)
  ai_generation_ratio            Float    @default(0.5)
  include_none_of_above          Boolean  @default(true)
  none_of_above_frequency        Float    @default(0.2)

  app_settings app_settings[]
}

enum AccessType {
  NONE
  PRACTICE_ONLY
  ONE_TIME
  UNLIMITED
}

enum AdminActionType {
  USER_CREATED
  USER_UPDATED
  USER_DELETED
  TEST_CANCELLED
  TEST_DELETED
  ACCESS_GRANTED
  ACCESS_REVOKED
  PASSWORD_RESET
  SETTINGS_UPDATED
}

enum QuestionType {
  MULTIPLE_CHOICE
  TRUE_FALSE
  FILL_IN_BLANK
  TYPING_PASSAGE
  TYPING_SEQUENCE
  SIMULATION
  READING_COMPREHENSION
  MATH
}

enum RequestStatus {
  PENDING
  APPROVED
  DENIED
}

enum TestStatus {
  STARTED
  PAUSED
  COMPLETED
  CANCELLED
}

enum UserType {
  USER
  ADMIN
}

enum EnglishQuestionType {
  VOCABULARY
  SPELLING
  GRAMMAR
  PUNCTUATION
  READING_COMPREHENSION
}

enum ReadingPassageGenre {
  FICTION
  NON_FICTION
  POETRY
  TECHNICAL
}
