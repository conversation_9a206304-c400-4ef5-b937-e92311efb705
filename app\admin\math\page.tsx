'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { PlusCircle, Edit, Trash2, Search, FileDown } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

const questionSchema = z.object({
  text: z.string().min(1, 'Question text is required'),
  category: z.string().min(1, 'Category is required'),
  difficulty: z.enum(['Easy', 'Medium', 'Hard']),
  points: z.coerce.number().int().min(1, 'Points must be at least 1'),
  options: z.array(z.string().min(1, 'Option cannot be empty')).min(2, 'At least two options are required'),
  correctAnswer: z.string().min(1, 'Correct answer is required'),
});

type QuestionFormValues = z.infer<typeof questionSchema>;

interface MathQuestion {
  id: string;
  text: string;
  category: string;
  difficulty: string;
  points: number;
  options: string[];
  correctAnswer: string;
}

const mockResults = [
    { id: '1', userName: 'Alice', testDate: '2023-10-27', score: 85, timeTaken: '15m 30s', questionsAttempted: 20 },
    { id: '2', userName: 'Bob', testDate: '2023-10-26', score: 92, timeTaken: '12m 10s', questionsAttempted: 20 },
    { id: '3', userName: 'Charlie', testDate: '2023-10-25', score: 74, timeTaken: '18m 45s', questionsAttempted: 20 },
  ];
  
  const mockSettings = {
    numQuestions: 20,
    timeLimit: 30,
    passingScore: 70,
    selectionMethod: 'random',
  };

export default function MathAdminPage() {
  const [activeTab, setActiveTab] = useState('questions');
  const { toast } = useToast();
  const [questions, setQuestions] = useState<MathQuestion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<MathQuestion | null>(null);

  const {
    control,
    handleSubmit,
    register,
    reset,
    formState: { errors },
  } = useForm<QuestionFormValues>({
    resolver: zodResolver(questionSchema),
    defaultValues: {
      text: '',
      category: '',
      difficulty: 'Easy',
      points: 10,
      options: ['', '', '', ''],
      correctAnswer: '',
    },
  });

  useEffect(() => {
    if (activeTab === 'questions') {
      fetchQuestions();
    }
  }, [activeTab]);

  const fetchQuestions = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/math/questions');
      if (response.ok) {
        const data = await response.json();
        setQuestions(data);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to fetch questions.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmit = async (data: QuestionFormValues) => {
    setIsSubmitting(true);
    const url = editingQuestion
      ? `/api/admin/math/questions/${editingQuestion.id}`
      : '/api/admin/math/questions';
    const method = editingQuestion ? 'PUT' : 'POST';

    try {
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: `Question ${editingQuestion ? 'updated' : 'added'} successfully.`,
        });
        fetchQuestions();
        reset();
        setEditingQuestion(null);
      } else {
        const errorData = await response.json();
        toast({
          title: 'Error',
          description: errorData.error || 'Failed to save question.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (question: MathQuestion) => {
    setEditingQuestion(question);
    reset({
      text: question.text,
      category: question.category,
      difficulty: question.difficulty as 'Easy' | 'Medium' | 'Hard',
      points: question.points,
      options: question.options,
      correctAnswer: question.correctAnswer,
    });
  };

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this question?')) {
      try {
        const response = await fetch(`/api/admin/math/questions/${id}`, {
          method: 'DELETE',
        });
        if (response.ok) {
          toast({
            title: 'Success',
            description: 'Question deleted successfully.',
          });
          fetchQuestions();
        } else {
          toast({
            title: 'Error',
            description: 'Failed to delete question.',
            variant: 'destructive',
          });
        }
      } catch (error) {
        toast({
          title: 'Error',
          description: 'An unexpected error occurred.',
          variant: 'destructive',
        });
      }
    }
  };

  const handleSaveSettings = () => {
    toast({
      title: 'Settings Saved',
      description: 'Math test configuration has been updated.',
    });
  };

  return (
    <div className="container mx-auto py-8">
      <CardHeader>
        <CardTitle className="text-3xl font-bold">Math Test Management</CardTitle>
        <CardDescription>Manage question bank, view results, and configure test settings.</CardDescription>
      </CardHeader>

      <div className="flex border-b mb-6">
        <button
          className={`py-2 px-4 text-sm font-medium ${activeTab === 'questions' ? 'border-b-2 border-primary text-primary' : 'text-muted-foreground'}`}
          onClick={() => setActiveTab('questions')}
        >
          Question Bank
        </button>
        <button
          className={`py-2 px-4 text-sm font-medium ${activeTab === 'results' ? 'border-b-2 border-primary text-primary' : 'text-muted-foreground'}`}
          onClick={() => setActiveTab('results')}
        >
          Test Results
        </button>
        <button
          className={`py-2 px-4 text-sm font-medium ${activeTab === 'config' ? 'border-b-2 border-primary text-primary' : 'text-muted-foreground'}`}
          onClick={() => setActiveTab('config')}
        >
          Configuration
        </button>
      </div>

      {activeTab === 'questions' && (
        <Card>
          <CardHeader>
            <CardTitle>Question Bank</CardTitle>
            <CardDescription>Manage the questions for the math tests.</CardDescription>
            <div className="flex justify-between items-center pt-4">
              <div className="relative w-1/3">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input placeholder="Search questions..." className="pl-10" />
              </div>
              <Dialog onOpenChange={() => { setEditingQuestion(null); reset(); }}>
                <DialogTrigger asChild>
                  <Button>
                    <PlusCircle className="mr-2 h-4 w-4" /> Add Question
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>{editingQuestion ? 'Edit' : 'Add New'} Question</DialogTitle>
                  </DialogHeader>
                  <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                    {/* Form fields */}
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? 'Saving...' : 'Save Question'}
                    </Button>
                  </form>
                </DialogContent>
              </Dialog>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <p>Loading questions...</p>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Question Text</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Difficulty</TableHead>
                    <TableHead>Points</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {questions.map((q) => (
                    <TableRow key={q.id}>
                      <TableCell>{q.text}</TableCell>
                      <TableCell>{q.category}</TableCell>
                      <TableCell>{q.difficulty}</TableCell>
                      <TableCell>{q.points}</TableCell>
                      <TableCell className="text-right">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="ghost" size="icon" onClick={() => handleEdit(q)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Edit Question</DialogTitle>
                            </DialogHeader>
                            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                              {/* Form fields */}
                              <Button type="submit" disabled={isSubmitting}>
                                {isSubmitting ? 'Saving...' : 'Save Changes'}
                              </Button>
                            </form>
                          </DialogContent>
                        </Dialog>
                        <Button variant="ghost" size="icon" className="text-red-500" onClick={() => handleDelete(q.id)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      )}

      {activeTab === 'results' && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
            <CardDescription>Review user test submissions and performance.</CardDescription>
            <div className="flex justify-between items-center pt-4">
              <Input type="date" className="w-auto" />
              <Button variant="outline">
                <FileDown className="mr-2 h-4 w-4" /> Export CSV
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Score</TableHead>
                  <TableHead>Time Taken</TableHead>
                  <TableHead>Questions Attempted</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mockResults.map((r) => (
                  <TableRow key={r.id}>
                    <TableCell>{r.userName}</TableCell>
                    <TableCell>{r.testDate}</TableCell>
                    <TableCell>{r.score}%</TableCell>
                    <TableCell>{r.timeTaken}</TableCell>
                    <TableCell>{r.questionsAttempted}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {activeTab === 'config' && (
        <Card>
          <CardHeader>
            <CardTitle>Test Configuration</CardTitle>
            <CardDescription>Adjust the parameters for math tests.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label htmlFor="numQuestions" className="block text-sm font-medium text-muted-foreground">
                Number of Questions per Test
              </label>
              <Input id="numQuestions" type="number" defaultValue={mockSettings.numQuestions} />
            </div>
            <div>
              <label htmlFor="timeLimit" className="block text-sm font-medium text-muted-foreground">
                Time Limit (minutes)
              </label>
              <Input id="timeLimit" type="number" defaultValue={mockSettings.timeLimit} />
            </div>
            <div>
              <label htmlFor="passingScore" className="block text-sm font-medium text-muted-foreground">
                Passing Score (%)
              </label>
              <Input id="passingScore" type="number" defaultValue={mockSettings.passingScore} />
            </div>
            <div>
              <label htmlFor="selectionMethod" className="block text-sm font-medium text-muted-foreground">
                Question Selection Method
              </label>
              <Select defaultValue={mockSettings.selectionMethod}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="random">Random</SelectItem>
                  <SelectItem value="difficulty">By Difficulty</SelectItem>
                  <SelectItem value="category">By Category</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-end">
              <Button onClick={handleSaveSettings}>Save Settings</Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}