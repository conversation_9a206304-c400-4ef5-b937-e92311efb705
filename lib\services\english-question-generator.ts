
import { AIService, AIQuestionRequest, AIQuestionResponse, getAIServiceConfig, createAIService } from '../ai-service';

export interface EnglishQuestionRequest extends AIQuestionRequest {
  genre?: string;
  passage?: string;
  count?: number;
}

export interface EnglishQuestionResponse extends AIQuestionResponse {
  wrongAnswers: string[];
  difficultyScore: number;
}

export interface PassageResponse {
  title: string;
  passageText: string;
  gradeLevel: number;
  wordCount: number;
  fleschKincaidScore: number;
}

export class EnglishQuestionGenerator {
  private aiService: AIService;

  constructor(aiService: AIService) {
    this.aiService = aiService;
  }

  async generateVocabularyQuestion(gradeLevel: number): Promise<EnglishQuestionResponse> {
    const prompt = this.getPromptTemplate('vocabulary', gradeLevel);
    const request: EnglishQuestionRequest = { gradeLevel, questionType: 'VOCABULARY' };
    // In a real implementation, we would call the AI service here.
    // const response = await this.aiService.generateEnglishQuestion(request, prompt);
    return this.mockQuestionResponse('vocabulary');
  }

  async generateSpellingQuestion(gradeLevel: number): Promise<EnglishQuestionResponse> {
    const prompt = this.getPromptTemplate('spelling', gradeLevel);
    const request: EnglishQuestionRequest = { gradeLevel, questionType: 'SPELLING' };
    return this.mockQuestionResponse('spelling');
  }

  async generateGrammarQuestion(gradeLevel: number): Promise<EnglishQuestionResponse> {
    const prompt = this.getPromptTemplate('grammar', gradeLevel);
    const request: EnglishQuestionRequest = { gradeLevel, questionType: 'GRAMMAR' };
    return this.mockQuestionResponse('grammar');
  }

  async generatePunctuationQuestion(gradeLevel: number): Promise<EnglishQuestionResponse> {
    const prompt = this.getPromptTemplate('punctuation', gradeLevel);
    const request: EnglishQuestionRequest = { gradeLevel, questionType: 'PUNCTUATION' };
    return this.mockQuestionResponse('punctuation');
  }

  async generateReadingPassage(gradeLevel: number, genre: string): Promise<PassageResponse> {
    const prompt = this.getPromptTemplate('passage', gradeLevel, genre);
    const request: EnglishQuestionRequest = { gradeLevel, genre };
    return this.mockPassageResponse();
  }

  async generateComprehensionQuestions(passage: string, count: number): Promise<EnglishQuestionResponse[]> {
    const prompt = this.getPromptTemplate('comprehension', 0, undefined, passage, count);
    const request: EnglishQuestionRequest = { gradeLevel: 0, questionType: 'READING_COMPREHENSION', passage, count };
    const responses: EnglishQuestionResponse[] = [];
    for (let i = 0; i < count; i++) {
      responses.push(this.mockQuestionResponse('reading_comprehension'));
    }
    return responses;
  }

  private getPromptTemplate(type: string, gradeLevel: number, genre?: string, passage?: string, count?: number): string {
    switch (type) {
      case 'vocabulary':
        return `Generate a grade ${gradeLevel} vocabulary question. The user should choose the word that best fits in the sentence. Provide one correct answer and 6 plausible wrong answers. Include an explanation for the correct answer and a difficulty score from 1-10.`;
      case 'spelling':
        return `Generate a grade ${gradeLevel} spelling question. The user should identify the misspelled word in a sentence. Provide one correct answer (the misspelled word) and 6 plausible wrong answers. Include an explanation for the correct answer and a difficulty score from 1-10.`;
      case 'grammar':
        return `Generate a grade ${gradeLevel} grammar question. The user should identify the grammatical error in a sentence. Provide one correct answer and 6 plausible wrong answers. Include an explanation for the correct answer and a difficulty score from 1-10.`;
      case 'punctuation':
        return `Generate a grade ${gradeLevel} punctuation question. The user should identify the punctuation error in a sentence. Provide one correct answer and 6 plausible wrong answers. Include an explanation for the correct answer and a difficulty score from 1-10.`;
      case 'passage':
        return `Generate a reading passage for grade ${gradeLevel} in the ${genre} genre. The passage should be between 150-250 words and have a Flesch-Kincaid score appropriate for the grade level.`;
      case 'comprehension':
        return `Generate ${count} reading comprehension questions for the following passage: \n\n${passage}\n\nFor each question, provide one correct answer and 6 plausible wrong answers. Include an explanation for the correct answer and a difficulty score from 1-10.`;
      default:
        return '';
    }
  }

  private mockQuestionResponse(type: string): EnglishQuestionResponse {
    return {
      questionText: `This is a mock ${type} question.`,
      answer: 'Correct Answer',
      explanation: 'This is the explanation.',
      wrongAnswers: ['Wrong 1', 'Wrong 2', 'Wrong 3', 'Wrong 4', 'Wrong 5', 'Wrong 6'],
      difficultyScore: 5,
      category: type.toUpperCase(),
    };
  }

  private mockPassageResponse(): PassageResponse {
    return {
      title: 'Mock Passage Title',
      passageText: 'This is a mock passage text. It is of a certain length and complexity.',
      gradeLevel: 8,
      wordCount: 180,
      fleschKincaidScore: 8.5,
    };
  }
}

// Factory function to create the generator
export async function createEnglishQuestionGenerator(): Promise<EnglishQuestionGenerator> {
  const config = await getAIServiceConfig();
  const aiService = createAIService(config);
  return new EnglishQuestionGenerator(aiService);
}

