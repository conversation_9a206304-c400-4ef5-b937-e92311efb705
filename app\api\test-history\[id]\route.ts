import { NextRequest } from 'next/server';
import { prisma } from '@/lib/db';
import { requireAuth } from '@/lib/auth';

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const user = await requireAuth();
    const testId = params.id;
    
    // Fetch the test with all related data
    const test = await prisma.tests.findUnique({
      where: {
        id: testId,
        userId: user.id
      },
      include: {
        test_types: true,
        test_results: true,
        typing_session: true
      }
    });
    
    if (!test) {
      return Response.json({ error: 'Test not found' }, { status: 404 });
    }
    
    // Calculate test duration
    let duration = 0;
    if (test.startedAt && test.completedAt) {
      duration = Math.floor(((test.completedAt as Date).getTime() - (test.startedAt as Date).getTime()) / 1000);
    }
    
    // Format the test data
    const formattedTest = {
      id: test.id,
      testDate: test.startedAt,
      jobTitle: test.test_types.displayName,
      finalScore: test.test_results?.score ?? test.score ?? null,
      testType: test.isPractice ? 'Practice Interview' : 'Full Interview',
      testDuration: duration, // in seconds
      status: test.status,
      // Add any additional metadata from test_results if available
      detailedResults: test.test_results?.detailedResults ?? null
    };
    
    // Extract questions and answers from the test data
    let questions: any[] = [];
    
    // For typing tests, we can get data from typing_session
    if (test.typing_session) {
      questions = [{
        id: 'typing-passage',
        questionText: 'Typing Test Passage',
        userAnswer: test.typing_session.typedText,
        aiFeedback: `Typing Speed: ${test.typing_session.wpm} WPM\nAccuracy: ${test.typing_session.accuracy}%`,
        suggestedImprovement: 'Continue practicing to improve speed and accuracy.'
      }];
    } 
    // For other tests, check if we have answers in JSON format
    else if (test.answers) {
      try {
        const answers = typeof test.answers === 'string' 
          ? JSON.parse(test.answers) 
          : test.answers;
        
        // Transform answers into question format
        questions = Object.entries(answers).map(([questionId, answerData]: [string, any], index) => ({
          id: questionId,
          questionText: `Question ${index + 1}`,
          userAnswer: typeof answerData === 'object' ? answerData.answer || JSON.stringify(answerData) : answerData,
          aiFeedback: typeof answerData === 'object' && answerData.feedback ? answerData.feedback : 'No feedback available',
          suggestedImprovement: typeof answerData === 'object' && answerData.suggestion ? answerData.suggestion : 'Continue practicing to improve your skills.'
        }));
      } catch (parseError) {
        console.warn('Failed to parse answers JSON:', parseError);
        // Fallback to empty questions array
      }
    }
    
    return Response.json({
      test: formattedTest,
      questions
    });
  } catch (error) {
    console.error('Error fetching test details:', error);
    return Response.json({ error: 'Failed to fetch test details' }, { status: 500 });
  }
}