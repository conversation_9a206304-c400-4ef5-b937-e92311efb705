# Troubleshooting Guide

## Overview

This comprehensive troubleshooting guide provides solutions to common issues, debugging procedures, and diagnostic tools for the Rubicon Programs Testing Application. It covers both development and production environments.

## Quick Diagnostic Checklist

### Before You Begin
- [ ] Check application health endpoint: `/api/health`
- [ ] Review recent logs for errors
- [ ] Verify environment variables are set correctly
- [ ] Confirm database connectivity
- [ ] Check system resources (CPU, memory, disk space)

## Common Issues & Solutions

### 1. Application Won't Start

#### Issue: "Port already in use"
```bash
Error: listen EADDRINUSE: address already in use :::3000
```

**Solutions:**
```bash
# Find and kill process using port 3000
lsof -ti :3000 | xargs kill -9

# Or use a different port
PORT=3001 npm run dev

# Check what's using the port
netstat -tulnp | grep :3000
```

#### Issue: "Cannot connect to database"
```bash
Error: Can't reach database server at localhost:5432
```

**Solutions:**
```bash
# Check if PostgreSQL is running
sudo systemctl status postgresql

# Start PostgreSQL if not running
sudo systemctl start postgresql

# Verify DATABASE_URL is correct
echo $DATABASE_URL

# Test database connection
psql $DATABASE_URL -c "SELECT 1;"

# Check database exists
psql -U postgres -l | grep rubicon

# Create database if missing
createdb rubicon_testing_db
```

#### Issue: "Prisma Client not generated"
```bash
Error: Cannot find module '.prisma/client'
```

**Solutions:**
```bash
# Generate Prisma client
npx prisma generate

# If schema changed, run migration
npx prisma migrate dev

# Reset database if corrupted
npx prisma migrate reset
npx prisma db seed
```

### 2. Authentication Issues

#### Issue: "NextAuth session not working"
```bash
Error: [next-auth][error][CLIENT_FETCH_ERROR]
```

**Solutions:**
```bash
# Verify environment variables
echo $NEXTAUTH_SECRET
echo $NEXTAUTH_URL

# Clear browser cookies and localStorage
# In browser dev tools:
localStorage.clear();
document.cookie.split(";").forEach(c => {
  document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
});

# Check if secret is properly set (minimum 32 characters)
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

#### Issue: "Email verification not working"
```bash
Error: Invalid or expired verification token
```

**Solutions:**
```sql
-- Check verification tokens in database
SELECT * FROM email_verification WHERE user_id = 'user-id';

-- Delete expired tokens
DELETE FROM email_verification WHERE expires < NOW();

-- Generate new verification token
INSERT INTO email_verification (user_id, token, expires, verified)
VALUES ('user-id', 'new-token', NOW() + INTERVAL '24 hours', false);
```

#### Issue: "Password reset not working"
**Check email configuration:**
```bash
# Test SMTP connection
node -e "
const nodemailer = require('nodemailer');
const transporter = nodemailer.createTransporter({
  host: process.env.EMAIL_SERVER_HOST,
  port: process.env.EMAIL_SERVER_PORT,
  auth: {
    user: process.env.EMAIL_SERVER_USER,
    pass: process.env.EMAIL_SERVER_PASSWORD
  }
});
transporter.verify().then(console.log).catch(console.error);
"
```

### 3. Test-Related Issues

#### Issue: "Tests not loading or saving"
```bash
Error: Test session not found or expired
```

**Diagnostic Steps:**
```sql
-- Check test session in database
SELECT * FROM tests WHERE id = 'test-id';

-- Check for orphaned test sessions
SELECT * FROM tests WHERE status = 'STARTED' AND created_at < NOW() - INTERVAL '2 hours';

-- Clean up old sessions
UPDATE tests SET status = 'CANCELLED' 
WHERE status IN ('STARTED', 'PAUSED') AND created_at < NOW() - INTERVAL '24 hours';
```

#### Issue: "Typing test not responding to keystrokes"
**Client-side debugging:**
```javascript
// In browser console, check event listeners
const input = document.querySelector('[data-testid="typing-input"]');
console.log(getEventListeners(input));

// Test keystroke logging
input.addEventListener('keydown', (e) => {
  console.log('Key pressed:', e.key, 'Code:', e.code);
});

// Check if WebSocket connection is working
console.log(window.WebSocket ? 'WebSocket supported' : 'WebSocket not supported');
```

#### Issue: "Math questions not generating"
```bash
Error: AI service unavailable or quota exceeded
```

**Solutions:**
```bash
# Check AI service configuration
echo $AI_SERVICE_ENABLED
echo $AI_SERVICE_API_KEY
echo $AI_SERVICE_PROVIDER

# Test AI service connectivity
curl -X POST https://api.openai.com/v1/completions \
  -H "Authorization: Bearer $AI_SERVICE_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"model":"text-davinci-003","prompt":"Test","max_tokens":5}'

# Fallback to database questions if AI fails
psql $DATABASE_URL -c "SELECT COUNT(*) FROM questions WHERE test_type_id = 'basic-math';"
```

### 4. Performance Issues

#### Issue: "Application running slowly"
**Memory diagnostics:**
```bash
# Check Node.js memory usage
node --expose-gc -e "
console.log('Memory usage:', process.memoryUsage());
global.gc();
console.log('After GC:', process.memoryUsage());
"

# Monitor with PM2
pm2 monit

# Check database performance
psql $DATABASE_URL -c "
SELECT query, mean_time, calls, total_time 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;"
```

**Database optimization:**
```sql
-- Analyze table statistics
ANALYZE;

-- Check for missing indexes
SELECT schemaname, tablename, attname, n_distinct, correlation
FROM pg_stats
WHERE schemaname = 'public' AND n_distinct > 100;

-- Find slow queries
SELECT query, mean_time, calls
FROM pg_stat_statements
WHERE mean_time > 1000
ORDER BY mean_time DESC;
```

#### Issue: "High CPU usage"
**Debugging steps:**
```bash
# Find CPU-intensive processes
top -p $(pgrep -f "node.*next")

# Profile Node.js application
node --prof app.js
# Process profiling output
node --prof-process isolate-*.log > profile.txt

# Check for memory leaks
node --inspect server.js
# Open chrome://inspect in Chrome browser
```

### 5. File Upload Issues

#### Issue: "File uploads failing"
```bash
Error: File too large or invalid format
```

**Solutions:**
```bash
# Check upload directory permissions
ls -la public/uploads
chmod 755 public/uploads
chown -R nodejs:nodejs public/uploads

# Verify environment settings
echo $UPLOAD_MAX_FILE_SIZE
echo $UPLOAD_ALLOWED_TYPES

# Check disk space
df -h
```

**Nginx configuration for large uploads:**
```nginx
# /etc/nginx/sites-available/your-site
server {
    client_max_body_size 10M;
    client_body_timeout 60;
    client_header_timeout 60;
    
    location /api/upload {
        proxy_request_buffering off;
        proxy_pass http://localhost:3000;
    }
}
```

## Development Environment Issues

### 1. Hot Reloading Not Working

#### Issue: "Changes not reflected in browser"
```bash
# Clear Next.js cache
rm -rf .next
npm run dev

# Check file watcher limits (Linux)
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# Restart development server
npm run dev -- --reset-cache
```

### 2. TypeScript Errors

#### Issue: "Type errors in IDE but not in build"
```bash
# Restart TypeScript server in VS Code
Cmd/Ctrl + Shift + P -> "TypeScript: Restart TS Server"

# Check TypeScript version consistency
npx tsc --version
npm list typescript

# Regenerate type definitions
rm -rf node_modules/@types
npm install
npx prisma generate
```

#### Issue: "Module resolution errors"
```json
// tsconfig.json - verify paths configuration
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"],
      "@/components/*": ["components/*"],
      "@/lib/*": ["lib/*"]
    }
  }
}
```

### 3. Build Issues

#### Issue: "Build failing with memory errors"
```bash
Error: JavaScript heap out of memory
```

**Solutions:**
```bash
# Increase memory limit
NODE_OPTIONS="--max-old-space-size=4096" npm run build

# Check for circular dependencies
npx madge --circular --extensions ts,tsx,js,jsx src/

# Analyze bundle size
npm run analyze
```

## Production Environment Issues

### 1. SSL Certificate Issues

#### Issue: "SSL certificate errors"
```bash
# Check certificate validity
openssl s_client -connect your-domain.com:443 -servername your-domain.com

# Check certificate expiration
openssl x509 -in /etc/ssl/certs/your-cert.crt -text -noout | grep "Not After"

# Renew Let's Encrypt certificate
sudo certbot renew

# Test SSL configuration
curl -I https://your-domain.com
```

### 2. Database Connection Pool Issues

#### Issue: "Too many database connections"
```sql
-- Check current connections
SELECT count(*) FROM pg_stat_activity;

-- Show connection details
SELECT pid, usename, application_name, client_addr, state
FROM pg_stat_activity
WHERE state = 'active';

-- Kill idle connections
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity
WHERE state = 'idle' AND state_change < now() - interval '1 hour';
```

**Prisma connection pool configuration:**
```javascript
// lib/db.ts
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  },
  // Connection pool settings
  __internal: {
    engine: {
      connectionLimit: 20
    }
  }
});
```

### 3. Email Delivery Issues

#### Issue: "Emails not being delivered"
```bash
# Check email queue
sudo mailq

# Test SMTP configuration
telnet your-smtp-server.com 587

# Check DNS records
dig MX your-domain.com
dig TXT your-domain.com | grep -i spf

# Test email sending
node -e "
const nodemailer = require('nodemailer');
const transporter = nodemailer.createTransporter({
  host: 'your-smtp-server.com',
  port: 587,
  secure: false,
  auth: {
    user: 'your-username',
    pass: 'your-password'
  }
});

transporter.sendMail({
  from: '<EMAIL>',
  to: '<EMAIL>',
  subject: 'Test Email',
  text: 'This is a test email'
}).then(console.log).catch(console.error);
"
```

## Debugging Tools & Commands

### 1. Application Diagnostics

#### Health Check Script
```bash
#!/bin/bash
# health-check.sh

echo "=== Application Health Check ==="

# Check if application is running
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    echo "✓ Application is responding"
else
    echo "✗ Application is not responding"
fi

# Check database connectivity
if psql $DATABASE_URL -c "SELECT 1" > /dev/null 2>&1; then
    echo "✓ Database is accessible"
else
    echo "✗ Database connection failed"
fi

# Check disk space
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -lt 85 ]; then
    echo "✓ Disk space OK ($DISK_USAGE%)"
else
    echo "⚠ Disk space critical ($DISK_USAGE%)"
fi

# Check memory usage
MEM_USAGE=$(free | grep Mem | awk '{printf "%.0f", ($3/$2) * 100}')
if [ $MEM_USAGE -lt 90 ]; then
    echo "✓ Memory usage OK ($MEM_USAGE%)"
else
    echo "⚠ Memory usage high ($MEM_USAGE%)"
fi

# Check recent errors in logs
ERROR_COUNT=$(tail -1000 /var/log/application.log | grep -c "ERROR" 2>/dev/null || echo 0)
if [ $ERROR_COUNT -eq 0 ]; then
    echo "✓ No recent errors in logs"
else
    echo "⚠ $ERROR_COUNT errors in recent logs"
fi

echo "=== Health Check Complete ==="
```

### 2. Database Diagnostics

#### Database Analysis Script
```sql
-- database-diagnostics.sql

-- Connection information
SELECT 
    count(*) as total_connections,
    count(*) FILTER (WHERE state = 'active') as active_connections,
    count(*) FILTER (WHERE state = 'idle') as idle_connections
FROM pg_stat_activity;

-- Table sizes
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Index usage
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
WHERE idx_tup_read = 0;

-- Slow queries (requires pg_stat_statements)
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements
WHERE mean_time > 100
ORDER BY mean_time DESC
LIMIT 10;
```

### 3. Log Analysis

#### Log Analysis Commands
```bash
# Find errors in application logs
grep -i "error" /var/log/application.log | tail -20

# Find authentication failures
grep -i "authentication failed" /var/log/application.log | wc -l

# Find slow requests
grep "slow request" /var/log/application.log | awk '{print $NF}' | sort -n | tail -10

# Analyze request patterns
awk '{print $1}' /var/log/nginx/access.log | sort | uniq -c | sort -nr | head -10

# Find 5xx errors
grep " 5[0-9][0-9] " /var/log/nginx/access.log | tail -20
```

#### Structured Log Analysis
```bash
# If using structured JSON logs
jq '.level' /var/log/application.log | sort | uniq -c
jq 'select(.level=="error") | .message' /var/log/application.log | tail -10
jq 'select(.responseTime > 1000) | {url, responseTime}' /var/log/application.log
```

## Error Code Reference

### HTTP Status Codes
| Code | Meaning | Common Causes | Solutions |
|------|---------|---------------|-----------|
| 400 | Bad Request | Invalid input data, malformed JSON | Check request validation, verify input formats |
| 401 | Unauthorized | Missing or invalid authentication | Verify session, check JWT token, re-authenticate |
| 403 | Forbidden | Insufficient permissions | Check user roles, verify access controls |
| 404 | Not Found | Resource doesn't exist | Verify URLs, check database records |
| 409 | Conflict | Duplicate resource, concurrent modification | Check for unique constraints, implement optimistic locking |
| 429 | Too Many Requests | Rate limit exceeded | Implement backoff, check rate limiting configuration |
| 500 | Internal Server Error | Application error, database issue | Check logs, verify database connectivity |
| 502 | Bad Gateway | Upstream server error | Check reverse proxy, application server status |
| 503 | Service Unavailable | Database down, maintenance mode | Check dependencies, verify service health |

### Application-Specific Errors
| Error Code | Description | Resolution |
|------------|-------------|------------|
| `EMAIL_NOT_VERIFIED` | User email not verified | Send new verification email or manually verify |
| `TEST_ACCESS_EXPIRED` | Test access has expired | Grant new access or extend existing access |
| `TEST_SESSION_INVALID` | Invalid test session | Start new test session |
| `QUESTION_GENERATION_FAILED` | AI service unavailable | Use fallback questions or fix AI configuration |
| `CERTIFICATE_GENERATION_FAILED` | PDF generation error | Check file permissions, verify PDF library |

## Performance Monitoring

### Key Metrics to Monitor
```javascript
// Performance monitoring script
const performanceMetrics = {
  // Response times
  avgResponseTime: 'SELECT AVG(response_time) FROM request_logs WHERE created_at > NOW() - INTERVAL \'1 hour\'',
  
  // Error rates
  errorRate: 'SELECT COUNT(*) FROM error_logs WHERE created_at > NOW() - INTERVAL \'1 hour\'',
  
  // Database performance
  slowQueries: 'SELECT COUNT(*) FROM pg_stat_statements WHERE mean_time > 1000',
  
  // Test completion rates
  testCompletionRate: `
    SELECT 
      COUNT(*) FILTER (WHERE status = 'COMPLETED') * 100.0 / COUNT(*) as completion_rate
    FROM tests 
    WHERE created_at > NOW() - INTERVAL '24 hours'
  `,
  
  // User activity
  activeUsers: 'SELECT COUNT(DISTINCT user_id) FROM user_sessions WHERE last_activity > NOW() - INTERVAL \'1 hour\''
};
```

### Alerts Configuration
```yaml
# monitoring/alerts.yml
alerts:
  - name: "High Error Rate"
    condition: "error_rate > 10/hour"
    severity: "critical"
    actions:
      - email: "<EMAIL>"
      - slack: "#alerts"
  
  - name: "Slow Response Time"
    condition: "avg_response_time > 2000ms"
    severity: "warning"
    actions:
      - email: "<EMAIL>"
  
  - name: "Database Connection Issues"
    condition: "db_connections > 80% of max"
    severity: "warning"
    actions:
      - email: "<EMAIL>"
      - restart: "application"
  
  - name: "Low Test Completion Rate"
    condition: "test_completion_rate < 85%"
    severity: "warning"
    actions:
      - email: "<EMAIL>"
```

## Recovery Procedures

### 1. Database Recovery

#### Point-in-Time Recovery
```bash
#!/bin/bash
# database-recovery.sh

BACKUP_DIR="/var/backups/postgresql"
RECOVERY_TIME="2024-01-15 14:30:00"

# Stop application
sudo systemctl stop rubicon-testing

# Stop PostgreSQL
sudo systemctl stop postgresql

# Restore from backup
sudo -u postgres pg_restore -d rubicon_testing_db "$BACKUP_DIR/latest_backup.sql"

# Configure recovery
echo "restore_command = 'cp /var/lib/postgresql/archive/%f %p'" > recovery.conf
echo "recovery_target_time = '$RECOVERY_TIME'" >> recovery.conf
sudo mv recovery.conf /var/lib/postgresql/data/

# Start PostgreSQL in recovery mode
sudo systemctl start postgresql

# Wait for recovery to complete
echo "Waiting for recovery to complete..."
sleep 30

# Restart application
sudo systemctl start rubicon-testing
```

### 2. Application Recovery

#### Service Recovery Script
```bash
#!/bin/bash
# service-recovery.sh

LOG_FILE="/var/log/recovery.log"

log() {
    echo "$(date): $1" >> $LOG_FILE
}

# Check if application is responding
if ! curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    log "Application not responding, attempting recovery"
    
    # Restart application
    pm2 restart rubicon-testing
    sleep 10
    
    # Check if recovery successful
    if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
        log "Application recovery successful"
    else
        log "Application recovery failed, escalating"
        # Send alert to admin
        echo "Application recovery failed at $(date)" | mail -s "Critical: App Recovery Failed" <EMAIL>
    fi
fi
```

## Preventive Maintenance

### Daily Maintenance Tasks
```bash
#!/bin/bash
# daily-maintenance.sh

# Clean up old logs
find /var/log -name "*.log" -mtime +30 -delete

# Clean up old test sessions
psql $DATABASE_URL -c "
DELETE FROM tests 
WHERE status IN ('STARTED', 'PAUSED') 
AND created_at < NOW() - INTERVAL '24 hours';
"

# Update database statistics
psql $DATABASE_URL -c "ANALYZE;"

# Backup database
pg_dump $DATABASE_URL | gzip > "/var/backups/db_$(date +%Y%m%d).sql.gz"

# Check disk space and send alert if low
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 85 ]; then
    echo "Disk usage is at $DISK_USAGE%" | mail -s "Disk Space Warning" <EMAIL>
fi
```

### Weekly Maintenance Tasks
```bash
#!/bin/bash
# weekly-maintenance.sh

# Vacuum database
psql $DATABASE_URL -c "VACUUM ANALYZE;"

# Reindex tables
psql $DATABASE_URL -c "REINDEX DATABASE rubicon_testing_db;"

# Clean up old backups
find /var/backups -name "*.sql.gz" -mtime +30 -delete

# Update SSL certificates
certbot renew --quiet

# Generate performance report
node scripts/generate-weekly-report.js
```

---

*This comprehensive troubleshooting guide provides solutions and procedures for maintaining the Rubicon Programs Testing Application in both development and production environments.*