'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { toast } from 'react-hot-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

interface MathFullTestProps {
  onComplete: (results: any) => void;
  onCancel: () => void;
}

interface MathQuestion {
    id: string;
    questionText: string;
    answer: string;
    gradeLevel: number;
}

function formatSeconds(totalSeconds: number) {
  const s = Math.max(0, Math.floor(totalSeconds));
  const mm = String(Math.floor(s / 60)).padStart(2, '0');
  const ss = String(s % 60).padStart(2, '0');
  return `${mm}:${ss}`;
}

export default function MathFullTest({ onComplete, onCancel }: MathFullTestProps) {
  const [testId, setTestId] = useState<string | null>(null);
  const [question, setQuestion] = useState<MathQuestion | null>(null);
  const [userAnswer, setUserAnswer] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [elapsedSeconds, setElapsedSeconds] = useState(0);
  const pollRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    async function startTest() {
      try {
        const response = await fetch('/api/math/start-test', {
          method: 'POST',
        });
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Failed to start test');
        }
        const data = await response.json();
        setTestId(data.testId);
        setQuestion(data.question);
        setIsLoading(false);
      } catch (error) {
        console.error('Error starting test:', error);
        toast.error(`Failed to start test: ${error instanceof Error ? error.message : 'Unknown error'}`);
        onCancel();
      }
    }
    startTest();
    return () => {
      if (pollRef.current) clearInterval(pollRef.current);
    }
  }, [onCancel]);

  useEffect(() => {
    if (!testId) return;

    const fetchStatus = async () => {
      try {
        const res = await fetch(`/api/math-tests/${testId}/status`);
        if (!res.ok) return;
        const data = await res.json();
        setIsPaused(Boolean(data.time?.isPaused || data.pausedAt));
        if (typeof data.time?.elapsedSeconds === 'number') {
          setElapsedSeconds(data.time.elapsedSeconds);
        }
      } catch (err) {
        // ignore
      }
    };

    fetchStatus();
    pollRef.current = setInterval(fetchStatus, 1000);

    return () => {
      if (pollRef.current) clearInterval(pollRef.current);
    };
  }, [testId]);

  const handlePause = async () => {
    if (!testId) return;
    try {
      const res = await fetch(`/api/math-tests/${testId}/pause`, { method: 'POST' });
      const data = await res.json();
      if (!res.ok) throw new Error(data.error || 'Failed to pause test');
      toast.success('Test paused');
      setIsPaused(true);
    } catch (e: any) {
      toast.error(e.message || 'Failed to pause test');
    }
  };

  const handleResume = async () => {
    if (!testId) return;
    try {
      const res = await fetch(`/api/math-tests/${testId}/resume`, { method: 'POST' });
      const data = await res.json();
      if (!res.ok) throw new Error(data.error || 'Failed to resume test');
      toast.success('Test resumed');
      setIsPaused(false);
    } catch (e: any) {
      toast.error(e.message || 'Failed to resume test');
    }
  };

  const handleSubmit = async () => {
    if (!testId || !question) return;
    if (isPaused) {
      toast.error('Test is paused. Resume to continue.');
      return;
    }

    setIsSubmitting(true);

    try {
        const response = await fetch('/api/math/submit-answer', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
                testId, 
                questionId: question.id, 
                userAnswer 
            }),
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Failed to submit answer');
        }

        const data = await response.json();

        if (data.status === 'completed') {
            onComplete({ finalScore: data.finalScore });
        } else {
            setQuestion(data.question);
            setUserAnswer('');
        }

    } catch (error) {
        console.error('Error submitting answer:', error);
        toast.error(`Failed to submit answer: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
        setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!question) {
    return <div>No question available for the test.</div>;
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Math Full Test</CardTitle>
          <div className="flex items-center gap-3">
            <span className="text-sm text-muted-foreground">Time: {formatSeconds(elapsedSeconds)}</span>
            {isPaused ? (
              <span className="text-xs px-2 py-1 rounded bg-yellow-100 text-yellow-800">Paused</span>
            ) : null}
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="secondary">{isPaused ? 'Resume' : 'Pause'}</Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>{isPaused ? 'Resume test?' : 'Pause test?'}</AlertDialogTitle>
                  <AlertDialogDescription>
                    {isPaused
                      ? 'You are about to resume the test. The timer will continue.'
                      : 'You are about to pause the test. You cannot answer questions while paused.'}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={isPaused ? handleResume : handlePause}>
                    {isPaused ? 'Resume' : 'Pause'}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <p className="text-lg font-semibold">Grade Level: {question.gradeLevel}</p>
          <p className="text-xl">{question.questionText}</p>
        </div>
        <div className="flex gap-2">
          <Input
            type="text"
            value={userAnswer}
            onChange={(e) => setUserAnswer(e.target.value)}
            placeholder="Your answer"
            disabled={isSubmitting || isPaused}
          />
          <Button onClick={handleSubmit} disabled={isSubmitting || isPaused}>
            {isSubmitting ? 'Submitting...' : 'Submit'}
          </Button>
        </div>
        <div className="mt-4">
            <Button onClick={onCancel} variant="outline">Cancel</Button>
        </div>
      </CardContent>
    </Card>
  );
}
