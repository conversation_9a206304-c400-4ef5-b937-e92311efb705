
'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { Brain, Save, TestTube } from 'lucide-react';

interface AppSettings {
  aiServiceProvider: string;
  aiServiceApiKey?: string;
  aiServiceModel: string;
  aiServiceEnabled: boolean;
}

export default function MathAdminPage() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testingAI, setTestingAI] = useState(false);
  const [formData, setFormData] = useState<AppSettings>({
    aiServiceProvider: 'abacusai',
    aiServiceApiKey: '',
    aiServiceModel: 'gpt-4.1-mini',
    aiServiceEnabled: true,
  });

  const updateFormData = (key: keyof AppSettings, value: any) => {
    setFormData(prev => ({ ...prev, [key]: value }));
  };

  // Load settings on component mount
  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/app-settings');
      if (response.ok) {
        const data = await response.json();
        setFormData({
          aiServiceProvider: data.settings.aiServiceProvider,
          aiServiceApiKey: data.settings.aiServiceApiKey || '',
          aiServiceModel: data.settings.aiServiceModel,
          aiServiceEnabled: data.settings.aiServiceEnabled,
        });
      } else {
        toast.error('Failed to fetch app settings');
      }
    } catch (error) {
      toast.error('Failed to fetch app settings');
      console.error('Error fetching settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      const response = await fetch('/api/admin/app-settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        const data = await response.json();
        toast.success('Math test settings saved successfully');
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to save settings');
      }
    } catch (error) {
      toast.error('Failed to save settings');
      console.error('Error saving settings:', error);
    } finally {
      setSaving(false);
    }
  };

  const testAIService = async () => {
    setTestingAI(true);
    try {
      const testResponse = await fetch('/api/ai/generate-question', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${formData.aiServiceApiKey || process.env.ABACUSAI_API_KEY}`
        },
        body: JSON.stringify({
          testType: 'basic-math',
          difficulty: 'medium',
          gradeLevel: 8,
          topic: 'algebra'
        })
      });

      if (testResponse.ok) {
        const data = await testResponse.json();
        toast.success(`AI Service Test Successful! Generated question: "${data.questions[0]?.question?.substring(0, 50)}..."`, {
          duration: 5000
        });
      } else {
        const error = await testResponse.json();
        toast.error(`AI Service Test Failed: ${error.error || 'Unknown error'}`);
      }
    } catch (error) {
      toast.error('AI Service Test Failed: Network or configuration error');
      console.error('AI test error:', error);
    } finally {
      setTestingAI(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI Question Generation
          </CardTitle>
          <CardDescription>
            Configure AI service for automatic question generation in math tests
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-base">Enable AI Generation</Label>
              <p className="text-sm text-muted-foreground">
                Use AI to generate new math questions during tests
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Switch
                checked={formData.aiServiceEnabled}
                onCheckedChange={(checked) => updateFormData('aiServiceEnabled', checked)}
              />
              <Badge variant={formData.aiServiceEnabled ? "default" : "secondary"}>
                {formData.aiServiceEnabled ? 'Enabled' : 'Disabled'}
              </Badge>
            </div>
          </div>

          {formData.aiServiceEnabled && (
            <div className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="aiProvider">AI Service Provider</Label>
                  <Select
                    value={formData.aiServiceProvider}
                    onValueChange={(value) => updateFormData('aiServiceProvider', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select AI provider" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="abacusai">Abacus AI</SelectItem>
                      <SelectItem value="openai">OpenAI (Coming Soon)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="aiModel">AI Model</Label>
                  <Select
                    value={formData.aiServiceModel}
                    onValueChange={(value) => updateFormData('aiServiceModel', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select model" />
                    </SelectTrigger>
                    <SelectContent>
                      {formData.aiServiceProvider === 'abacusai' && (
                        <>
                          <SelectItem value="gpt-4.1-mini">GPT-4.1-mini</SelectItem>
                          <SelectItem value="gpt-4o">GPT-4o</SelectItem>
                          <SelectItem value="gpt-4o-mini">GPT-4o-mini</SelectItem>
                        </>
                      )}
                      {formData.aiServiceProvider === 'openai' && (
                        <>
                          <SelectItem value="gpt-4">GPT-4</SelectItem>
                          <SelectItem value="gpt-4-turbo">GPT-4 Turbo</SelectItem>
                          <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                        </>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="aiApiKey">API Key (Optional)</Label>
                <Input
                  id="aiApiKey"
                  type="password"
                  placeholder="Leave blank to use system default"
                  value={formData.aiServiceApiKey}
                  onChange={(e) => updateFormData('aiServiceApiKey', e.target.value)}
                />
                <p className="text-sm text-muted-foreground">
                  Custom API key for {formData.aiServiceProvider}. If left blank, system default will be used.
                </p>
              </div>

              <div className="flex gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={testAIService}
                  disabled={testingAI}
                  className="flex items-center gap-2"
                >
                  {testingAI ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                      Testing...
                    </>
                  ) : (
                    <>
                      <TestTube className="h-4 w-4" />
                      Test AI Service
                    </>
                  )}
                </Button>
                <p className="text-sm text-muted-foreground flex items-center">
                  Test the AI service configuration with a sample math question
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Math Test Configuration</CardTitle>
          <CardDescription>
            General settings for math tests
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Additional math test settings can be configured here in future updates.
          </p>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button onClick={handleSave} disabled={saving} className="flex items-center gap-2">
          {saving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4" />
              Save Settings
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
